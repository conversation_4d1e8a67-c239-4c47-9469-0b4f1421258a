@include export-module('tab-highcontrast-icons') {
  #{&}.e-tab {

    .e-tab-header {

      .e-toolbar-items.e-hscroll.e-rtl {

        .e-nav-left-arrow::before {
          content: '\e219';
        }

        .e-nav-right-arrow::before {
          content: '\e98f';
        }
      }

      .e-close-icon::before {
        content: '\e7fc';
        position: relative;
      }
    }

    &.e-vertical-icon .e-tab-header {

      @media screen and (max-width: 480px) {

        .e-popup-up-icon::before {
          content: '\e85e';
        }

        .e-popup-down-icon::before {
          content: '\e84f';
        }
      }
    }

    &.e-vertical-tab {

      .e-tab-header {

        .e-popup-up-icon {

          &::before {
            content: 'More';
          }

          &::after {
            content: '\e85e';
          }
        }

        .e-popup-down-icon {

          &::before {
            content: 'More';
          }

          &::after {
            content: '\e84f';
          }
        }
      }

      &.e-icon-tab {

        .e-tab-header {

          .e-popup-up-icon {

            &::before {
              content: '';
            }

            &::after {
              content: '\ebb7';
            }
          }

          .e-popup-down-icon {

            &::before {
              content: '';
            }

            &::after {
              content: '\ebb7';
            }
          }
        }
      }
    }
  }

  .e-tab-clone-element {

    .e-close-icon::before {
      content: '\e7fc';
      position: relative;
    }
  }
}
