@import 'ej2-base/styles/definition/tailwind.scss';
@import 'signature/tailwind-definition.scss';
@import 'signature/all.scss';

@import 'input/tailwind-definition.scss';
@import 'input/icons/tailwind.scss';
@import 'input/all.scss';
@import 'numerictextbox/tailwind-definition.scss';
@import 'numerictextbox/icons/tailwind.scss';
@import 'numerictextbox/all.scss';
@import 'maskedtextbox/tailwind-definition.scss';
@import 'maskedtextbox/all.scss';
@import 'ej2-popups/styles/popup/tailwind-definition.scss';
@import 'ej2-popups/styles/tooltip/tailwind-definition.scss';
@import 'slider/tailwind-definition.scss';
@import 'slider/all.scss';
@import 'textbox/tailwind-definition.scss';
@import 'textbox/all.scss';
@import 'textarea/tailwind-definition.scss';
@import 'textarea/all.scss';
@import 'ej2-buttons/styles/button/tailwind-definition.scss';
@import 'ej2-popups/styles/spinner/tailwind-definition.scss';
@import 'uploader/tailwind-definition.scss';
@import 'uploader/icons/tailwind.scss';
@import 'uploader/all.scss';
@import 'ej2-splitbuttons/styles/split-button/tailwind-definition.scss';
@import 'color-picker/tailwind-definition.scss';
@import 'color-picker/icons/tailwind.scss';
@import 'color-picker/all.scss';
@import 'rating/tailwind-definition.scss';
@import 'rating/all.scss';
@import 'data-form/tailwind-definition.scss';
@import 'data-form/all.scss';
@import 'otp-input/tailwind-definition.scss';
@import 'otp-input/all.scss';
@import 'speech-to-text/tailwind-definition.scss';
@import 'speech-to-text/icons/tailwind.scss';
@import 'speech-to-text/all.scss';
@import 'smart-textarea/tailwind-definition.scss';
@import 'smart-textarea/all.scss';