

/* stylelint-disable property-no-vendor-prefix */
@-webkit-keyframes material-spinner-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes material-spinner-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes fabric-spinner-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes fabric-spinner-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons::before {
  content: "\e7e7";
}

.e-upload .e-upload-files .e-icons.e-file-pause-btn::before {
  content: "\e77b";
}

.e-upload .e-upload-files .e-icons.e-file-reload-btn::before {
  content: "\e706";
}

.e-upload .e-upload-files .e-icons.e-file-play-btn::before {
  content: "\e70c";
}

.e-upload .e-upload-files .e-file-delete-btn.e-icons::before {
  content: "\e820";
}

.e-upload .e-upload-files .e-file-abort-btn.e-icons::before {
  content: "\e81b";
}

.e-upload .e-upload-files .e-icons.e-msie::before {
  position: relative;
  right: 10px;
}

.e-upload .e-upload-files .e-icons.e-file-abort-icon.e-msie::before {
  right: 12px;
}

.e-upload {
  width: 100%;
}
.e-upload.e-control-wrapper {
  font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", sans-serif;
}
.e-upload .e-hidden-file-input {
  border: 0;
  height: 0;
  margin: 0;
  outline: none;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}
.e-upload .e-file-select-wrap {
  padding: 16px 0 16px 12px;
}
.e-upload .e-file-select-wrap .e-file-select,
.e-upload .e-file-select-wrap .e-file-select .e-uploader {
  display: inline-block;
  width: 0;
}
.e-upload .e-file-select-wrap .e-file-select .e-uploader {
  opacity: 0;
}
.e-upload .e-file-select-wrap .e-file-drop {
  font-family: inherit;
  font-size: 14px;
  margin-left: 12px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: rgba(var(--color-sf-error));
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 11px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: rgba(var(--color-sf-error));
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: rgba(var(--color-sf-error));
}
.e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 32px;
}
.e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-upload .e-upload-files {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.e-upload .e-upload-files .e-icons:focus {
  outline: none;
}
.e-upload .e-upload-files .e-upload-file-list {
  font-family: inherit;
  font-size: 14px;
  height: 100%;
  line-height: normal;
  min-height: 90px;
  position: relative;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container {
  display: block;
  height: 100%;
  margin-left: 12px;
  margin-right: 90px;
  min-height: 35px;
  position: relative;
  top: 3px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: left;
  font-family: inherit;
  font-size: 14px;
  max-width: 75%;
  overflow: hidden;
  padding-top: 12px;
  position: relative;
  text-overflow: ellipsis;
  top: 0;
  white-space: nowrap;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name::before {
  content: attr(data-tail);
  float: right;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-top: 12px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-hidden {
  visibility: hidden;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  display: block;
  font-size: 11px;
  padding: 8px 0;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  display: block;
  font-family: inherit;
  font-size: 11px;
  padding-bottom: 12px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-progress, .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information.e-upload-progress {
  display: none;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  display: block;
  height: 8px;
  padding-bottom: 6px;
  padding-top: 6px;
  position: absolute;
  width: 98%;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  border-radius: 0;
  display: block;
  height: 2px;
  width: 100%;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  border-radius: 0;
  display: inherit;
  height: 2px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: right;
  font-family: inherit;
  font-size: 11px;
  position: relative;
  right: 6px;
  top: -27px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 11px;
  height: 18px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 12px;
  margin-top: -18px;
  padding: 18px;
  position: absolute;
  right: 0;
  top: 50%;
  vertical-align: middle;
  width: 18px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-abort-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-pause-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-play-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-reload-btn.e-icons.e-upload-progress {
  cursor: default;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons {
  padding: 18px 13px 18px 23px;
}
.e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  right: 36px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):hover, .e-upload .e-upload-files .e-clear-icon-focus {
  background: rgba(var(--color-sf-on-surface), 0.08);
  border-color: transparent;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-upload .e-upload-files .e-clear-icon-focus {
  border-radius: 50%;
}
.e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 12px;
  opacity: 1;
}
.e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 15px;
  opacity: 1;
}
.e-upload .e-file-select-wrap .e-btn, .e-upload .e-upload-actions .e-btn {
  font-family: inherit;
}
.e-upload .e-upload-actions {
  position: relative;
  text-align: right;
}
.e-upload .e-upload-actions .e-file-upload-btn {
  margin: 12px;
}
.e-upload .e-upload-actions .e-file-clear-btn {
  margin: 12px;
}
.e-upload.e-rtl .e-file-select-wrap {
  padding: 16px 12px 16px 0;
}
.e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 10px;
}
.e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-upload.e-rtl .e-upload-actions {
  text-align: left;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  height: 100%;
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-rtl-container {
  direction: ltr;
  float: right;
  width: 100%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  float: right;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: right;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  direction: ltr;
  float: right;
  position: relative;
  text-align: right;
  width: 100%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  float: right;
  position: initial;
  top: 46px;
  width: 89%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: left;
  right: -8px;
  top: -25px;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons {
  left: 0;
  margin-left: 12px;
  margin-right: 12px;
  right: auto;
  top: 50%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons {
  left: 36px;
  right: auto;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 36px;
}
.e-upload.e-disabled .e-file-drop {
  color: rgba(var(--color-sf-outline));
}
.e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-type, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-size, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-outline));
}
.e-upload .e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 65px;
  min-height: 65px;
}

.e-small .e-upload .e-file-select-wrap {
  padding: 12px 0 12px 12px;
}
.e-small .e-upload .e-file-select-wrap .e-file-drop {
  font-size: 13px;
  margin-left: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list {
  min-height: 76px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  font-size: 12px;
  padding-top: 10px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  font-size: 11px;
  padding: 5px 0;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  font-size: 12px;
  padding-top: 10px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  padding-bottom: 10px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  padding-bottom: 3px;
  padding-top: 3px;
}
.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  font-size: 10px;
  height: 24px;
  padding: 12px;
  width: 24px;
}
.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-small .e-upload.e-rtl .e-file-select-wrap {
  padding: 16px 12px 16px 0;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 10px;
  padding-top: 6px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: rgba(var(--color-sf-error));
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 11px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: rgba(var(--color-sf-error));
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: rgba(var(--color-sf-error));
}
.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 34px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.e-upload {
  border: 1px dashed rgba(var(--color-sf-outline-variant));
  border-radius: 4px;
}
.e-upload .e-file-drop {
  color: rgba(var(--color-sf-on-surface));
  vertical-align: middle;
}
.e-upload .e-upload-files {
  border-top: 1px solid rgba(var(--color-sf-outline-variant));
}
.e-upload .e-upload-files .e-upload-file-list {
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  color: rgba(var(--color-sf-on-surface));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  color: rgba(var(--color-sf-on-surface));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  color: rgba(var(--color-sf-on-surface-variant));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-on-surface));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-success {
  color: rgba(var(--color-sf-success));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-fails {
  color: rgba(var(--color-sf-error));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-validation-fails {
  color: rgba(var(--color-sf-error));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap {
  background: -webkit-gradient(linear, left bottom, left top, from(rgba(var(--color-sf-primary), 0.11)), to(rgba(var(--color-sf-primary), 0.11))), rgba(var(--color-sf-surface));
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-progress {
  background: rgba(var(--color-sf-primary));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-success {
  background: rgba(var(--color-sf-success));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-failed {
  background: rgba(var(--color-sf-error));
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-bar-text {
  color: rgba(var(--color-sf-on-surface));
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons {
  color: rgba(var(--color-sf-on-surface-variant));
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:hover, .e-upload .e-upload-files .e-file-delete-btn.e-icons:hover, .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-upload .e-upload-files .e-file-abort-btn.e-icons:hover, .e-upload .e-upload-files .e-file-pause-btn.e-icons:hover, .e-upload .e-upload-files .e-file-play-btn.e-icons:hover {
  color: rgba(var(--color-sf-on-surface));
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-upload .e-upload-files .e-clear-icon-focus {
  background: rgba(var(--color-sf-on-surface), 0.08);
  border-color: transparent;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}

.e-bigger .e-upload {
  width: 100%;
}
.e-bigger .e-upload .e-file-select-wrap {
  padding: 20px 0 20px 20px;
}
.e-bigger .e-upload .e-file-select-wrap .e-file-drop {
  font-size: 15px;
  margin-left: 16px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list {
  font-size: 15px;
  line-height: normal;
  min-height: 100px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container {
  margin-left: 16px;
  top: 0;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  font-size: 15px;
  padding-top: 16px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  font-size: 12px;
  padding: 10px 0;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  font-size: 15px;
  padding-top: 16px;
  top: initial;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  font-size: 12px;
  padding-bottom: 16px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  height: 10px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  height: 3px;
  width: 98%;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  height: 3px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  font-size: 11px;
  right: 10px;
  top: -33px;
}
.e-bigger .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-size: 12px;
  height: 18px;
  margin: 16px;
  margin-top: -24px;
  padding: 20px;
  top: 50%;
  width: 18px;
}
.e-bigger .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger .e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons {
  padding: 20px 17px 20px 26px;
}
.e-bigger .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  right: 45px;
}
.e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 16px;
}
.e-bigger .e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 18px;
}
.e-bigger .e-upload .e-upload-actions .e-btn {
  margin-left: 16px;
}
.e-bigger .e-upload .e-upload-actions .e-file-upload-btn {
  margin: 15px;
}
.e-bigger .e-upload .e-upload-actions .e-file-clear-btn {
  margin: 15px;
}
.e-bigger .e-upload.e-rtl .e-file-select-wrap {
  padding: 20px 16px 20px 0;
}
.e-bigger .e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 0;
}
.e-bigger .e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-right: 16px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  margin-left: 60px;
  margin-right: 16px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  top: 54px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  left: 10px;
  right: initial;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  left: 0;
  margin-left: 16px;
  margin-right: 16px;
  padding: 20px;
  top: 50%;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons.e-disabled, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-msie.e-icons {
  padding: 20px 17px 20px 26px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 41px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 16px;
  padding-top: 12px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: rgba(var(--color-sf-error));
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 12px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: rgba(var(--color-sf-error));
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: rgba(var(--color-sf-error));
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 42px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.e-bigger.e-small .e-upload {
  width: 100%;
}
.e-bigger.e-small .e-upload.e-control-wrapper {
  font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", sans-serif;
}
.e-bigger.e-small .e-upload .e-hidden-file-input {
  border: 0;
  height: 0;
  margin: 0;
  outline: none;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}
.e-bigger.e-small .e-upload .e-file-select-wrap {
  padding: 16px 0 16px 12px;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-select,
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-select .e-uploader {
  display: inline-block;
  width: 0;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-select .e-uploader {
  opacity: 0;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-drop {
  font-family: inherit;
  font-size: 14px;
  margin-left: 12px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: rgba(var(--color-sf-error));
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 11px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: rgba(var(--color-sf-error));
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: rgba(var(--color-sf-error));
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 32px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger.e-small .e-upload .e-upload-files {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.e-bigger.e-small .e-upload .e-upload-files .e-icons:focus {
  outline: none;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list {
  font-family: inherit;
  font-size: 14px;
  height: 100%;
  line-height: normal;
  min-height: 90px;
  position: relative;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container {
  display: block;
  height: 100%;
  margin-left: 12px;
  margin-right: 90px;
  min-height: 35px;
  position: relative;
  top: 3px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: left;
  font-family: inherit;
  font-size: 14px;
  max-width: 75%;
  overflow: hidden;
  padding-top: 12px;
  position: relative;
  text-overflow: ellipsis;
  top: 0;
  white-space: nowrap;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name::before {
  content: attr(data-tail);
  float: right;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-top: 12px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-hidden {
  visibility: hidden;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  display: block;
  font-size: 11px;
  padding: 8px 0;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  display: block;
  font-family: inherit;
  font-size: 11px;
  padding-bottom: 12px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information.e-upload-progress {
  display: none;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  display: block;
  height: 8px;
  padding-bottom: 6px;
  padding-top: 6px;
  position: absolute;
  width: 98%;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  border-radius: 0;
  display: block;
  height: 2px;
  width: 100%;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  border-radius: 0;
  display: inherit;
  height: 2px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: right;
  font-family: inherit;
  font-size: 11px;
  position: relative;
  right: 6px;
  top: -27px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 11px;
  height: 18px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 12px;
  margin-top: -18px;
  padding: 18px;
  position: absolute;
  right: 0;
  top: 50%;
  vertical-align: middle;
  width: 18px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons.e-upload-progress {
  cursor: default;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: rgba(var(--color-sf-outline));
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons {
  padding: 18px 13px 18px 23px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  right: 36px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):hover, .e-bigger.e-small .e-upload .e-upload-files .e-clear-icon-focus {
  background: rgba(var(--color-sf-on-surface), 0.08);
  border-color: transparent;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-bigger.e-small .e-upload .e-upload-files .e-clear-icon-focus {
  border-radius: 50%;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 12px;
  opacity: 1;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 15px;
  opacity: 1;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-btn, .e-bigger.e-small .e-upload .e-upload-actions .e-btn {
  font-family: inherit;
}
.e-bigger.e-small .e-upload .e-upload-actions {
  position: relative;
  text-align: right;
}
.e-bigger.e-small .e-upload .e-upload-actions .e-file-upload-btn {
  margin: 12px;
}
.e-bigger.e-small .e-upload .e-upload-actions .e-file-clear-btn {
  margin: 12px;
}
.e-bigger.e-small .e-upload.e-rtl .e-file-select-wrap {
  padding: 16px 12px 16px 0;
}
.e-bigger.e-small .e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 10px;
}
.e-bigger.e-small .e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-actions {
  text-align: left;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  height: 100%;
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-rtl-container {
  direction: ltr;
  float: right;
  width: 100%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  float: right;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: right;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  direction: ltr;
  float: right;
  position: relative;
  text-align: right;
  width: 100%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  float: right;
  position: initial;
  top: 46px;
  width: 89%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: left;
  right: -8px;
  top: -25px;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons {
  left: 0;
  margin-left: 12px;
  margin-right: 12px;
  right: auto;
  top: 50%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons {
  left: 36px;
  right: auto;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 36px;
}
.e-bigger.e-small .e-upload.e-disabled .e-file-drop {
  color: rgba(var(--color-sf-outline));
}
.e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-type, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-size, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: rgba(var(--color-sf-outline));
}
.e-bigger.e-small .e-upload .e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 65px;
  min-height: 65px;
}
.e-bigger.e-small .e-upload .e-bigger .e-content-placeholder.e-upload.e-placeholder-upload,
.e-bigger.e-small .e-upload .e-bigger.e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 80px;
  min-height: 80px;
}