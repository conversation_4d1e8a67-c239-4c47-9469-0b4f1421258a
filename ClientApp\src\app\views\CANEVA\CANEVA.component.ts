import { Component } from '@angular/core';
import { InnoCollapseComponent } from 'app/component/inno-collapse/inno-collapse.component';
import { InnoFileComponent } from 'app/component/inno-file/inno-file.component';
import { InnoPageContentWrapperComponent } from 'app/component/inno-page-content-wrapper/inno-page-content-wrapper.component';
import { SharedModule } from 'app/module/shared.module';
import { CanevasDTO } from '../../dto/interface/createCanevasDto';
import { CanevasService } from '../../service/canevas.service';
import { AddDocumentDialog } from '../../service/dialog/add-document.dialog';
import { DocumentType } from '../../enum/documentType';
import { ModifyCanevasDialog } from '../../service/dialog/modify-canevas.dialog';
import { ConfirmDialogService } from '../../service/dialog/confirm-dialog.service';
import { SpinnerService } from '../../service/spinner.service';
import { ToastService } from '../../service/toast.service';
import { getImageURL } from '../../helpers/common.helper';
import { InnoImageEditorComponent } from '../../component/inno-image-editor/inno-image-editor.component';
import { ModifyCanevasNoteDialog } from '../../service/dialog/modify-canevas-note.dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { DocumentService } from '../../service/document.service';
import { CreateDocumentDTO, DocumentDTO, UpdateDocumentDTO } from '../../dto/interface/document/documentDto';
import { InnoRedLineThroughComponent } from '../../component/inno-red-line-through/inno-red-line-through.component';
import { CapturePictureDialog } from 'app/service/dialog/capture-picture.dialog';
import { AudioRecorderDialog } from 'app/service/dialog/audio-recorder.dialog';

@Component({
  selector: 'app-CANEVA',
  templateUrl: './CANEVA.component.html',
  styleUrls: ['./CANEVA.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoPageContentWrapperComponent,
    InnoCollapseComponent,
    InnoFileComponent,
    InnoImageEditorComponent,
    InnoRedLineThroughComponent
  ]
})
export class CANEVAComponent {
  public canevasList: CanevasDTO[] = [];
  public selectedCanevas?: CanevasDTO;
  public documentType = DocumentType;
  public getImageURL = getImageURL;
  public editorVisible = false;
  public imageToEdit: DocumentDTO;
  private itemSelected: CanevasDTO;

  constructor(
    private canevasService: CanevasService,
    private addDocumentDialog: AddDocumentDialog,
    private modifyCanevasDialog: ModifyCanevasDialog,
    private modifyCanevasNoteDialog: ModifyCanevasNoteDialog,
    private confirmDialogService: ConfirmDialogService,
    private spinnerService: SpinnerService,
    private toastService: ToastService,
    private sanitizer: DomSanitizer,
    private documentService: DocumentService,
    private capturePictureDialog: CapturePictureDialog,
    private audioRecorderDialog: AudioRecorderDialog,
  ) { }

  ngOnInit(): void {
    this.loadCanevas();
  }

  loadCanevas(): void {
    this.canevasService.getList().subscribe({
      next: (list) => {
        if (list && list.length > 0)
          list[0].isExpanded = true;
        this.canevasList = list
        this.canevasList.forEach(c => {
          if (c.note)
            c.safeHtml = this.sanitizer.bypassSecurityTrustHtml(c.note)
        });
      },
      error: (err) => console.error('Erreur de chargement des canevas', err)
    });
  }

  getCanevas(id: string): void {
    this.canevasService.getById(id).subscribe({
      next: (canevas) => this.selectedCanevas = canevas,
      error: (err) => console.error('Erreur lors de la récupération du canevas', err)
    });
  }

  createCanevas(): void {
    this.modifyCanevasDialog.open({  }).then((dialogRef: any) => {
      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          this.loadCanevas();
        }
      });
    });
  }
  strikethrough(e: any, dto: CanevasDTO) {
    e.stopPropagation();
    dto.strikethrough = !dto.strikethrough;
    this.updateCanevas(dto.id, dto);
  }

  updateCanevas(id: string, dto: CanevasDTO): void {
    this.canevasService.update(id, dto).subscribe({
      next: (updated) => {
        const index = this.canevasList.findIndex(c => c.id === id);
        if (index === -1) return;

        const wasExpanded = this.canevasList[index].isExpanded;

        this.canevasList[index] = {
          ...updated,
          isExpanded: wasExpanded
        };
      },
      error: (err) => console.error('Erreur de mise à jour', err)
    });
  }

  deleteCanevas(id: string): void {
    this.confirmDialogService.open({
      title: 'Supprimer le canevas',
      message: 'Êtes-vous sûr de vouloir supprimer ce canevas ?',
      confirmText: 'Supprimer',
      cancelText: 'Annuler'
    }).then((dialogRef: any) => {
      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          this.spinnerService.show();
          this.canevasService.delete(id).subscribe({
            next: () => {
              this.toastService.showSuccess('Canevas supprimé avec succès');
              this.loadCanevas();
            },
            error: (error: any) => {
              console.error('Erreur suppression canevas:', error);
              this.toastService.showError('Erreur lors de la suppression du canevas');
            },
            complete: () => {
              this.spinnerService.hide();
            }
          });
        }
      });
    });
  }

  uploadDocument(type: DocumentType, canevasId: string) {
    this.addDocumentDialog.open({ canevasId: canevasId, type: type, defaultName: "" }).then((dialogRef: any) => {
      dialogRef.afterClosed().subscribe((result: any) => {
        if (result)
          this.loadCanevas();
      });
    });
  }

  openEditor(image: DocumentDTO, item: CanevasDTO) {
    this.imageToEdit = image;
    this.itemSelected = item;
  }

  onImageSaved(blob: File) {
    let dto: UpdateDocumentDTO = {
      name: this.imageToEdit.name,
      fileName: this.imageToEdit.fileName,
      canevasId: this.itemSelected.id,
      documentId: this.imageToEdit.id,
      document: blob,
      strikethrough: this.imageToEdit.strikethrough
    }

    this.imageToEdit = null;
    this.itemSelected = null;
    this.documentService.UpdateDocument(dto).subscribe({
      next: (val) => {
        this.loadCanevas();
      }
    })
  }

  addNote(item: CanevasDTO) {
    this.modifyCanevasNoteDialog.open({data: item}).then((dialogRef: any) => {
      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          this.loadCanevas();
        }
      });
    });

  }

  handleUploadCaptureImage(canevasId: string) {
    this.capturePictureDialog.open().then((dialogRef: any) => {
      dialogRef.afterClosed().subscribe((newCaptureImage: any) => {
        if(!newCaptureImage) return

          const payload: CreateDocumentDTO = {
          name: newCaptureImage.name,
          document: newCaptureImage,
          fileName: newCaptureImage.name,
          type: DocumentType.Image,
          canevasId: canevasId
        };

        this.spinnerService.show();
        this.documentService.addDocument(payload).subscribe({
          next: (res: any) => {
            if (!res) return;
            this.loadCanevas();
            this.toastService.showSuccess("Document ajouté avec succès")
          },
          error: (error: any) => {
            this.toastService.showError("Erreur lors de l'ajout du document");
          },
          complete: () => {
            this.spinnerService.hide();
          }
        });
    })})
  }

  handleAudioRecording(canevasId: string) {
    this.audioRecorderDialog.open({ canevasId: canevasId }).then((dialogRef: any) => {
      dialogRef.afterClosed().subscribe((result: any) => {
        // The audio recorder now automatically uploads the file
        // If result is true, it means the upload was successful
        if (result === true) {
          this.loadCanevas(); // Refresh the canevas list to show the new audio
        }
        // If result is a File object, it means manual save was used (fallback)
        else if (result instanceof File) {
          const payload: CreateDocumentDTO = {
            name: result.name,
            document: result,
            fileName: result.name,
            type: DocumentType.Audio,
            canevasId: canevasId
          };

          this.spinnerService.show();
          this.documentService.addDocument(payload).subscribe({
            next: (res: any) => {
              if (!res) return;
              this.loadCanevas();
              this.toastService.showSuccess("Enregistrement audio ajouté avec succès");
            },
            error: (error: any) => {
              console.error('Error uploading audio:', error);
              this.toastService.showError("Erreur lors de l'ajout de l'enregistrement audio");
            },
            complete: () => {
              this.spinnerService.hide();
            }
          });
        }
      });
    });
  }

  async share(id: string) {
    const url = window.location.href;
    const title = document.title;
    if (navigator.share) {
      try {
        await navigator.share({ title , url });
        console.log('Page partagée avec succès');
      } catch (err) {
        console.error('Erreur de partage :', err);
      }
    } else {
      this.copyToClipboard(url);
      alert('URL copiée dans le presse-papier !');
    }
  }

  private copyToClipboard(text: string) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
  }
}
