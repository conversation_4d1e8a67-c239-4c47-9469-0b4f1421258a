$tbar-skin: $skin-name !default;
$border-size: 0;
$border-type: solid;
$tbar-icons-bgr-font-size: 22px;
$tbar-separator-bgr-mrgn: 9px 5px;
$tbar-separator-vertical-bgr-mrgn: 5px 10px;
$tbar-radius: 0;
$tbar-separator-nrml-mrgn: 8px 4px;
$tbar-pop-radius: 4px;
$tbar-separator-vertical-nrml-mrgn: 3px 7px;
$tbar-border-size: 0;
$tbar-separator-border-type: $border-type;
$tbar-pop-box-shadow: $shadow-lg;
$tbar-hover-border-color: $icon-color;
$tbar-pressed-border: $tbar-hover-border-color;
$tbar-zero-value: 0 !default;
$tbar-border-radius: 4px !default;
$tbar-tab-highlight-color:  rgba(0, 0, 0, 0) !default;
$tbar-border-nav-type: solid !default;
$tbar-btn-box-shadow: none !default;
$tbar-default-bg: $content-bg-color-alt1 !default;
$tbar-items-default-bg: $tbar-default-bg !default;
$tbar-default-font: $content-text-color !default;
$tbar-active-bg: $content-bg-color-alt3 !default;
$tbar-active-icon-color: $icon-color !default;
$tbar-press-bg: $content-bg-color-pressed !default;
$tbar-btn-press-bg: $tbar-press-bg !default;
$tbar-press-border-color: $border-light !default;
$tbar-hover-bg:  $content-bg-color-hover !default;
$tbar-hover-font: $toolbar-icon-color !default;
$tbar-default-icon-color: $icon-color !default;
$tbar-pressed-bg: $content-bg-color-alt2 !default;
$tbar-pressed-font: $content-text-color-alt2 !default;
$tbar-select-font: $content-text-color-alt2 !default;
$tbar-default-icon-overlay: $icon-color-disabled !default;
$tbar-separator-border: $border-light !default;
$tbar-default-border: $border-light !default;
$tbar-hover-border-color: $icon-color !default;
$tbar-focus-border-color: $tbar-hover-border-color !default;
$tbar-focus-bg: $content-bg-color-focus !default;
$tbar-press-font: $tbar-hover-font !default;
$tbar-default-font-overlay: $tbar-default-icon-overlay !default;
$tbar-active-font-color: $icon-color !default;
$tbar-border-nav-type: $border-light !default;
$tbar-border-nav-active-type: $border-light !default;
$tbar-btn-border: none !default;
$tbar-box-shadow: none !default;
$tbar-border-type: $border-type !default;
$tbar-separator-size: 1px !default;
$tba-horizontal-separator: 0 $tbar-separator-size 0 0 !default;
$tba-vertical-separator: 0 0 $tbar-separator-size 0 !default;
$tbar-popup-border-width: 0 !default;
$tbar-popup-rtl-border-width: 0 !default;
$tbar-popup-vertical-border-width: $border-size 0 0 0 !default;
$tbar-popup-vertical-rtl-border-width: 0 0 $border-size 0 !default;
$tbar-nrml-size: 40px !default;
$tbar-bgr-size: 48px !default;
$tbar-nrml-items-size: 32px !default;
$tbar-bgr-items-size: 40px !default;
$tbar-nrml-item-size: 32px !default;
$tbar-item-height: 32px !default;
$tbar-item-nrml-minwidth: 32px !default;
$tbar-bgr-item-size: 40px !default;
$tbar-btn-font-size: 16px !default;
$tbar-btn-txt-font-size: $text-sm !default;
$tbar-item-pop-bg-color: $transparent !default;
$tbar-item-bgr-padding: 4px 0 !default;
$tbar-item-nrml-padding: 4px 0 !default;
$tbar-btn-nrml-padding: 0 8px !default;
$tbar-btn-bgr-padding: $tbar-btn-nrml-padding !default;
$tbar-btn-bgr-focus-padding: $tbar-btn-nrml-padding !default;
$tbar-btn-icn-nrml-padding: 8px 4px !default;
$tbar-btn-icn-bgr-padding: 11px 4px !default;
$tbar-rtl-btn-icn-nrml-padding: $tbar-btn-icn-nrml-padding !default;
$tbar-rtl-btn-icn-bgr-padding: $tbar-btn-icn-bgr-padding !default;
$tbar-btn-icn-right-bgr-padding: $tbar-btn-icn-bgr-padding !default;
$tbar-btn-icn-right-nrml-padding: $tbar-btn-icn-nrml-padding !default;
$tbar-rtl-btn-icn-right-nrml-padding: $tbar-btn-icn-nrml-padding !default;
$tbar-rtl-btn-icn-right-bgr-padding: $tbar-btn-icn-bgr-padding !default;
$btn-txt-nrml-padding: 6px 4px !default;
$btn-txt-bgr-padding: 8px 4px 10px 4px !default;
$btn-rtl-txt-nrml-padding: 8px 4px !default;
$btn-rtl-txt-bgr-padding: 8px 4px !default;
$tbar-pop-bg: $tbar-default-bg !default;
$tbar-item-pop-nrml-padding: 0 !default;
$tbar-item-pop-bgr-padding: 0 !default;
$tbar-pop-btn-bgr-padding: 8px 18px !default;
$tbar-pop-btn-nrml-padding: 6px 12px !default;
$tbar-pop-icon-bgr-padding: 0 6px 0 0 !default;
$tbar-pop-icon-nrml-padding: 0 4px 0 0 !default;
$tbar-pop-btn-txt-nrml-pad: 0 0 0 4px !default;
$tbar-pop-btn-txt-bgr-pad: 0 0 0 6px !default;
$tbar-nav-press-border: 0 !default;
$tbar-nav-hover-border: 1px solid $tbar-default-border !default;
$tbar-nav-focus-border: 0 !default;
$tbar-nav-pressed-box-shadow: none !default;
$tbar-btn-border-radius: 4px !default;
$tbar-btn-pressed-box-shadow: none !default;
$tbar-btn-nrml-mrgn: 0 !default;
$tbar-popup-padding: 0 !default;
$tbar-btn-nrml-minheight: 32px !default;
$tbar-btn-nrml-line-height: 20px !default;
$tbar-btn-icon-nrml-line-height: 16px !default;
$tbar-btn-bgr-minheight: 40px !default;
$tbar-btn-bgr-line-height: 22px !default;
$tbar-btn-icon-bgr-line-height: 18px !default;
$tbar-btn-nrml-minwidth: 32px !default;
$tbar-btn-weight: $font-weight-normal !default;
$tbar-btn-bgr-minwidth: 40px !default;
$tbar-separator-nrml-height: 24px !default;
$tbar-separator-bgr-height: 30px !default;
$tbar-separator-nrml-minheight: $tbar-separator-nrml-height !default;
$tbar-separator-bgr-minheight: $tbar-separator-bgr-height !default;
$tbar-btn-icon-nrml-width: 12px !default;
$tbar-btn-icon-nrml-height: 16px !default;
$tbar-right-item-line-height: 1 !default;
$tbar-btn-icon-bgr-width: 13px !default;
$tbar-nav-nrml-width: 28px !default;
$tbar-nav-bgr-width: 36px !default;
$tbar-item-nrml-mrgn: 8px !default;
$tbar-item-bgr-mrgn: 10px !default;
$tbar-btn-pop-nrml-minheight: 38px !default;
$tbar-btn-pop-bgr-minheight: 48px !default;
$tbar-multirow-items-mrgn-bigger: 12.5px !default;
$tbar-multirow-items-mrgn-small: 10px !default;
$tbar-multirow-item-top-btm-mrgn-bigger: 0 !default;
$tbar-multirow-item-top-btm-mrgn-small: 0 !default;
$tbar-bgr-btn-text-font-size: $text-base !default;
$tbar-bgr-btn-icon-font-size: 18px !default;
$tbar-bgr-btn-focus-padding: 0 8px !default;
$tbar-nrml-btn-border-radius: 4px !default;
$tbar-nrml-btn-focus-padding: 0 8px !default;
$tbar-nrml-btn-focus-outline: 0 !default;
$tbar-btn-icons-focus-color: $toolbar-icon-color !default;
$tbar-btn-text-focus-color: $toolbar-icon-color !default;
$tbar-btn-focus-border-color: $tbar-focus-border-color !default;
$tbar-btn-hover-border-size: $border-size !default;
$tbar-btn-hover-active-icons-color: $toolbar-icon-color !default;
$tbar-btn-hover-active-text-color: $toolbar-icon-color !default;
$tbar-btn-overlay-opacity: inherit !default;
$tbar-btn-active-bg: $tbar-press-bg !default;
$tbar-btn-active-icons-color: $toolbar-icon-color !default;
$tbar-btn-active-text-color: $toolbar-icon-color !default;
$tbar-btn-text-color: $content-text-color !default;
$tbar-btn-pressed-text-color: $secondary-text-color-pressed !default;
$tbar-btn-pressed-focus-box-shadow: $shadow-sm !default;
$tbar-btn-pressed-bg: $secondary-bg-color-pressed !default;
$tbar-flat-btn-active-box-shadow: $shadow-sm !default;
$tbar-ext-btn-focus-padding: 0 8px !default;
$tbar-ext-btn-icon-padding: 0 !default;
$tbar-ext-btn-icon-font-size: $text-base !default;
$tbar-ext-btn-focus-box-shadow: $keyboard-focus !default;
$tbar-ext-btn-hover-border-color: $tbar-hover-border-color !default;
$tbar-ext-btn-border: none !default;
$tbar-popup-icon-font-size: 16px !default;
$tbar-popup-text-btn-icon-padding: 0 !default;
$tbar-popup-bgr-text-btn-icon-padding: 0 !default;
$tbar-popup-btn-border: none !default;
$tbar-popup-btn-border-radius: 4px !default;
$tbar-popup-bgr-height: 38px !default;
$tbar-popup-bgr-btn-icon-font-size: $text-base !default;
$tbar-popup-bgr-btn-text-font-size: $text-base !default;
$tbar-popup-nav-active-border-bottom-right-radius: 0 !default;
$tbar-popup-nav-active-bg: $content-bg-color-pressed !default;
$tbar-popup-nav-active-icons-color: $icon-color-pressed !default;
$tbar-popup-nav-hover-bg: $tbar-hover-bg !default;
$tbar-popup-nav-hover-color: $icon-color-hover !default;
$tbar-popup-nav-hover-icons-color: $icon-color-hover !default;
$tbar-popup-nav-hover-border-color: $tbar-default-border !default;
$tbar-popup-nav-hover-border-size: 0 !default;
$tbar-popup-nav-hover-active-bg: $content-bg-color-pressed !default;
$tbar-popup-nav-hover-active-border-color: $tbar-default-border !default;
$tbar-popup-nav-hover-active-border-size: 0 !default;
$tbar-popup-nav-focus-bg: $tbar-focus-bg !default;
$tbar-popup-nav-focus-color: $toolbar-icon-color !default;
$tbar-popup-nav-focus-border-color: $icon-color !default;
$tbar-popup-nav-focus-border-size: 0 !default;
$tbar-popup-btn-bg: $transparent !default;
$tbar-popup-btn-hover-bg: $flyout-bg-color-hover !default;
$tbar-popup-btn-hover-box-shadow: none !default;
$tbar-popup-btn-active-bg: $flyout-bg-color-pressed !default;
$tbar-popup-btn-active-box-shadow: none !default;
$tbar-popup-btn-focus-bg: $flyout-bg-color-focus !default;
$tbar-popup-btn-focus-box-shadow: $tbar-ext-btn-focus-box-shadow !default;
$tbar-popup-btn-text-focus-color: $flyout-text-color-focus !default;
$tbar-popup-nav-pressed-icons-active-color: $tbar-active-font-color !default;
$tbar-popup-btn-focus-outline: 0 !default;
$tbar-popup-nav-pressed-border-color: $tbar-pressed-bg !default;
$tbar-popup-nav-pressed-border-size: 0 !default;
$tbar-popup-nav-pressed-focus-border-color: $tbar-pressed-bg !default;
$tbar-popup-nav-pressed-focus-border-size: 0 !default;
$tbar-popup-btn-hover-border-size: $tbar-zero-value !default;
@mixin tbar-btn-animation {
  content: '';
}
@mixin tbar-btn-animation-after {
  content: '';
}
