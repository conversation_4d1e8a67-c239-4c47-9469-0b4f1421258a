{"name": "@types/node-forge", "version": "1.3.11", "description": "TypeScript definitions for node-forge", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-forge", "license": "MIT", "contributors": [{"name": "<PERSON>      ", "githubUsername": "westy92", "url": "https://github.com/westy92"}, {"name": "<PERSON>       ", "githubUsername": "flynetworks", "url": "https://github.com/flynetworks"}, {"name": "<PERSON><PERSON><PERSON>      ", "githubUsername": "a-k-g", "url": "https://github.com/a-k-g"}, {"name": "Rafal2228          ", "githubUsername": "rafal2228", "url": "https://github.com/rafal2228"}, {"name": "<PERSON><PERSON>         ", "githubUsername": "beenotung", "url": "https://github.com/beenotung"}, {"name": "<PERSON>        ", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/joeflateau"}, {"name": "timhwang21         ", "githubUsername": "timhwang21", "url": "https://github.com/timhwang21"}, {"name": "<PERSON>     ", "githubUsername": "andersk", "url": "https://github.com/andersk"}, {"name": "<PERSON><PERSON><PERSON>    ", "githubUsername": "saschazar21", "url": "https://github.com/saschazar21"}, {"name": "<PERSON><PERSON><PERSON>    ", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rogierschouten"}, {"name": "<PERSON>         ", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/aseevia"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "wiktor-k", "url": "https://github.com/wiktor-k"}, {"name": "Ligia Frangello    ", "githubUsername": "frangello", "url": "https://github.com/frangello"}, {"name": "<PERSON>      ", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/avezov"}, {"name": "<PERSON>       ", "githubUsername": "j-fuentes", "url": "https://github.com/j-fuentes"}, {"name": "<PERSON>         ", "githubUsername": "darkade", "url": "https://github.com/darkade"}, {"name": "<PERSON><PERSON><PERSON><PERSON>      ", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-forge"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "1f94eadd96d1ed1e3b8513ff0af0af616821310570eb410699b93f6b3e3f3903", "typeScriptVersion": "4.6"}