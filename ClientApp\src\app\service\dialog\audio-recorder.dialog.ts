import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';
import { IAudioRecorderDialog } from 'app/component/inno-audio-recorder/inno-audio-recorder.component';

@Injectable({ providedIn: 'root' })
export class AudioRecorderDialog extends AsyncDialog<any> {
  async open(data?: IAudioRecorderDialog): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../component/inno-audio-recorder/inno-audio-recorder.component'
    );

    return this.matDialog.open(
      importedModuleFile.InnoAudioRecorderComponent.getComponent(),
      {
        width: '600px',
        maxWidth: '90vw',
        panelClass: 'custom_dialog',
        backdropClass: 'custom_backdrop',
        data: data,
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
