// Audio Recorder Component Styles

.container-dialog {
  min-height: 400px;
  max-width: 500px;
  margin: 0 auto;
}

// Recording button animations
.recording-button {
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// Pulse animation for recording indicator
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.recording-indicator {
  animation: pulse 1s infinite;
}

// Custom range slider styles
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;

  &::-webkit-slider-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
  }

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #3b82f6;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #2563eb;
      transform: scale(1.1);
    }
  }

  &::-moz-range-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
    border: none;
  }

  &::-moz-range-thumb {
    background: #3b82f6;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #2563eb;
      transform: scale(1.1);
    }
  }
}

// Visualizer canvas styles
canvas {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

// Button styles
.button-outline-modal {
  @apply px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors;
}

// Status indicators
.status-recording {
  color: #dc2626;
  font-weight: 600;
}

.status-paused {
  color: #f59e0b;
  font-weight: 600;
}

// Timer display
.timer-display {
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  font-weight: bold;
  color: #374151;
  letter-spacing: 0.05em;
}

// Control buttons
.control-button {
  @apply rounded-full p-3 transition-all duration-200 ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.record-button {
  @apply bg-red-500 hover:bg-red-600 text-white;
}

.pause-button {
  @apply bg-yellow-500 hover:bg-yellow-600 text-white;
}

.stop-button {
  @apply bg-gray-600 hover:bg-gray-700 text-white;
}

.play-button {
  @apply bg-blue-500 hover:bg-blue-600 text-white;
}

// Action buttons
.action-button {
  @apply px-6 py-2 rounded-lg font-medium transition-all duration-200;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.delete-button {
  @apply bg-red-500 hover:bg-red-600 text-white;
}

.save-button {
  @apply bg-green-500 hover:bg-green-600 text-white;
}

// Playback section
.playback-section {
  @apply bg-gray-50 rounded-lg p-4;
  border: 1px solid #e5e7eb;
}

// Error states
.error-container {
  @apply bg-red-50 border border-red-200 rounded-lg p-4;
}

.error-icon {
  @apply h-8 w-8 text-red-500;
}

.error-title {
  @apply text-red-600 font-medium;
}

.error-message {
  @apply text-red-500 text-sm;
}

// Loading spinner
.loading-spinner {
  @apply animate-spin rounded-full border-b-2 border-blue-600;
}

// Responsive adjustments
@media (max-width: 640px) {
  .container-dialog {
    padding: 1rem;
  }
  
  .control-button {
    @apply p-2;
  }
  
  .timer-display {
    font-size: 1.25rem;
  }
  
  canvas {
    height: 60px;
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .container-dialog {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  canvas {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #6b7280;
  }
  
  .playback-section {
    background-color: #374151;
    border-color: #6b7280;
  }
}
