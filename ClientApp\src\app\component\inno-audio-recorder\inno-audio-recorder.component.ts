import { Component, ElementRef, ViewChild, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoModalWrapperComponent } from '../inno-modal-wrapper/inno-modal-wrapper.component';
import { ToastService } from 'app/service/toast.service';
import { DocumentService } from 'app/service/document.service';
import { SpinnerService } from 'app/service/spinner.service';
import { CreateDocumentDTO } from 'app/dto/interface/document/documentDto';
import { DocumentType } from 'app/enum/documentType';
import { Subject } from 'rxjs';

export interface IAudioRecorderDialog {
  canevasId?: string;
  projectId?: string;
  moduleId?: string;
}

@Component({
  selector: 'app-inno-audio-recorder',
  templateUrl: './inno-audio-recorder.component.html',
  styleUrls: ['./inno-audio-recorder.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    InnoModalWrapperComponent
  ]
})
export class InnoAudioRecorderComponent implements OnDestroy {
  @ViewChild('visualizer') visualizerRef!: ElementRef<HTMLCanvasElement>;

  public isRecording = false;
  public isPaused = false;
  public isPlaying = false;
  public hasRecording = false;
  public recordingTime = 0;
  public playbackTime = 0;
  public duration = 0;
  public isMicrophoneAllowed = true;
  public isInitializing = true;
  public isUploading = false;
  public audioFormat: string = '.wav';
  public uploadingSubject: Subject<boolean> = null;

  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recordedBlob: Blob | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private animationId: number | null = null;
  private recordingTimer: any = null;
  private playbackTimer: any = null;
  private stream: MediaStream | null = null;
  private rawAudioData: Float32Array[] = [];
  private sampleRate: number = 44100;

  constructor(
    private dialogRef: MatDialogRef<InnoAudioRecorderComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: IAudioRecorderDialog,
    private toastService: ToastService,
    private documentService: DocumentService,
    private spinnerService: SpinnerService
  ) {
    this.initializeAudio();
  }

  static getComponent(): typeof InnoAudioRecorderComponent {
    return InnoAudioRecorderComponent;
  }

  async initializeAudio(): Promise<void> {
    try {
      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported');
      }

      // Request microphone permission
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });

      // Initialize audio context for visualization
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Handle audio context state
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;

      this.microphone = this.audioContext.createMediaStreamSource(this.stream);
      this.microphone.connect(this.analyser);

      // Store the sample rate from the audio context
      this.sampleRate = this.audioContext.sampleRate;

      // Check MediaRecorder support
      if (!window.MediaRecorder) {
        throw new Error('MediaRecorder not supported');
      }

      // Initialize MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: this.getSupportedMimeType()
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = async () => {
        const webmBlob = new Blob(this.audioChunks, {
          type: this.mediaRecorder?.mimeType || 'audio/webm'
        });
        try {
          this.recordedBlob = await this.convertToWav(webmBlob);
        } catch (error) {
          console.error('Error converting to WAV:', error);
          this.toastService.showError('Erreur lors de la conversion en WAV');
          this.recordedBlob = webmBlob; // Fallback to original
        }
        this.hasRecording = true;
        this.isUploading = false;
        // Automatically upload the recording
        this.uploadRecording();
      };

      this.mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        this.toastService.showError('Erreur lors de l\'enregistrement');
      };

      this.isMicrophoneAllowed = true;
      this.isInitializing = false;
      this.startVisualization();

    } catch (error: any) {
      console.error('Error accessing microphone:', error);
      this.isMicrophoneAllowed = false;
      this.isInitializing = false;

      let errorMessage = 'Impossible d\'accéder au microphone';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Accès au microphone refusé. Veuillez autoriser l\'accès dans les paramètres du navigateur.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';
      } else if (error.name === 'NotSupportedError') {
        errorMessage = 'Enregistrement audio non supporté par ce navigateur.';
      }

      this.toastService.showError(errorMessage);
    }
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // fallback
  }

  private async convertToWav(webmBlob: Blob): Promise<Blob> {
    try {
      // Create audio context for conversion
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Convert blob to array buffer
      const arrayBuffer = await webmBlob.arrayBuffer();

      // Decode audio data
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      // Convert to WAV format
      const wavBuffer = this.audioBufferToWav(audioBuffer);

      // Create WAV blob
      return new Blob([wavBuffer], { type: 'audio/wav' });
    } catch (error) {
      console.error('WAV conversion error:', error);
      throw error;
    }
  }

  private audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
    const length = buffer.length;
    const numberOfChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const bytesPerSample = 2; // 16-bit
    const blockAlign = numberOfChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = length * blockAlign;
    const bufferSize = 44 + dataSize;

    const arrayBuffer = new ArrayBuffer(bufferSize);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, bufferSize - 8, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true); // PCM format
    view.setUint16(20, 1, true); // PCM
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, 16, true); // 16-bit
    writeString(36, 'data');
    view.setUint32(40, dataSize, true);

    // Convert audio data
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return arrayBuffer;
  }

  startRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'inactive') {
      return;
    }

    this.uploadingSubject = new Subject<boolean>();
    this.audioChunks = [];
    this.recordingTime = 0;
    this.isRecording = true;
    this.isPaused = false;
    this.hasRecording = false;

    this.mediaRecorder.start(100); // Collect data every 100ms

    this.recordingTimer = setInterval(() => {
      this.recordingTime += 0.1;
    }, 100);
  }

  pauseRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'recording') {
      return;
    }

    this.mediaRecorder.pause();
    this.isPaused = true;

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
  }

  resumeRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'paused') {
      return;
    }

    this.mediaRecorder.resume();
    this.isPaused = false;

    this.recordingTimer = setInterval(() => {
      this.recordingTime += 0.1;
    }, 100);
  }

  stopRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
      return;
    }

    this.mediaRecorder.stop();
    this.isRecording = false;
    this.isPaused = false;

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
  }

  private uploadRecording(): void {
    if (!this.recordedBlob) {
      this.toastService.showError('Aucun enregistrement à sauvegarder');
      return;
    }
    // Upload the file automatically
    this.isUploading = true;
    this.spinnerService.show();

    // Create a File object from the blob
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.audioFormat;
    const fileName = `recording-${timestamp}.${extension}`;
    const file = new File([this.recordedBlob], fileName, {
      type: this.recordedBlob.type
    });

    const payload: CreateDocumentDTO = {
      name: fileName,
      document: file,
      fileName: fileName,
      type: DocumentType.Audio,
      canevasId: this.dialogData.canevasId,
      projectId: this.dialogData.projectId,
      moduleId: this.dialogData.moduleId
    };

    this.documentService.addDocument(payload).subscribe({
      next: (res: any) => {
        if (res) {
          this.toastService.showSuccess("Enregistrement audio ajouté avec succès");
          this.uploadingSubject.next(true);
          this.uploadingSubject = null;
          this.dialogRef.close(true); // Return true to indicate successful upload
        }
      },
      error: (error: any) => {
        console.error('Error uploading audio:', error);
        this.toastService.showError("Erreur lors de l'ajout de l'enregistrement audio");
        this.isUploading = false;
        this.spinnerService.hide();
      },
      complete: () => {
        this.isUploading = false;
        this.spinnerService.hide();
      }
    });
  }

  saveRecording(): void {
    if (!this.recordedBlob) {
      this.toastService.showError('Aucun enregistrement à sauvegarder');
      return;
    }

    // Create a File object from the blob
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.audioFormat;
    const fileName = `recording-${timestamp}.${extension}`;
    const file = new File([this.recordedBlob], fileName, {
      type: this.recordedBlob.type
    });

    this.dialogRef.close(file);
  }

  handleClose(): void {
    if (this.uploadingSubject) {
      this.stopRecording();
      console.log("Wait");
      this.uploadingSubject.subscribe(v => {
      console.log("Wait1");
        this.dialogRef.close();    
      });
    } else {
      console.log("Close");
      this.dialogRef.close();
    }
  }

  async requestMicrophoneAgain(): Promise<void> {
    try {
      const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      console.log('Microphone permission state:', permission.state);

      await this.initializeAudio();
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
      this.toastService.showError('Impossible d\'accéder au microphone. Veuillez vérifier les permissions.');
    }
  }

  private startVisualization(): void {
    
    console.log(this.analyser);
    console.log(this.visualizerRef);
    if (!this.analyser || !this.visualizerRef) return;

    console.log("Start draw");
    const canvas = this.visualizerRef.nativeElement;
    const canvasContext = canvas.getContext('2d');
    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      this.animationId = requestAnimationFrame(draw);

      this.analyser!.getByteFrequencyData(dataArray);

      canvasContext!.fillStyle = 'red';
      canvasContext!.fillRect(0, 0, canvas.width, canvas.height);

      const barWidth = (canvas.width / bufferLength) * 2.5;
      let barHeight;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        barHeight = (dataArray[i] / 255) * canvas.height * 0.8;

        const gradient = canvasContext!.createLinearGradient(0, canvas.height - barHeight, 0, canvas.height);
        gradient.addColorStop(0, '#4F46E5');
        gradient.addColorStop(1, '#7C3AED');

        canvasContext!.fillStyle = gradient;
        canvasContext!.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

        x += barWidth + 1;
         console.log("canvasContext");
      }
    };

    draw();
  }

  formatTime(time: number): string {
    if (isNaN(time) || time === 0) return '0:00';

    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }

  ngOnDestroy(): void {
    // Clean up resources
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    if (this.playbackTimer) {
      clearInterval(this.playbackTimer);
    }

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
    }

    if (this.audioContext) {
      this.audioContext.close();
    }
  }
}
