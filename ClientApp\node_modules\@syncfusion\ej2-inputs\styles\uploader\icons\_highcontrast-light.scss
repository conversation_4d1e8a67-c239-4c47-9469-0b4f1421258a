@include export-module('uploader-highcontrast-icons') {
  .e-upload .e-upload-files .e-file-remove-btn.e-icons::before {
    content: '\e953';
  }

  .e-upload .e-upload-files .e-icons.e-file-pause-btn::before {
    content: '\e325';
  }

  .e-upload .e-upload-files .e-icons.e-file-reload-btn::before {
    content: '\e837';
  }

  .e-upload .e-upload-files .e-icons.e-file-play-btn::before {
    content: '\e327';
  }

  .e-upload .e-upload-files .e-file-delete-btn.e-icons::before {
    content: '\e965';
  }

  .e-upload .e-upload-files .e-file-abort-btn.e-icons::before {
    content: '\e202';
  }

  .e-upload .e-upload-files .e-icons.e-msie::before {
    position: relative;
    right: $ie-icons-position-value;
  }

  .e-upload .e-upload-files .e-icons.e-file-abort-icon.e-msie::before {
    right: $ie-abort-icon-position-value;
  }
}
