// Audio Recorder Component Styles

.container-dialog {
  min-height: 400px;
  max-width: 500px;
  margin: 0 auto;
}

// Recording button animations
.recording-button {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

// Pulse animation for recording indicator
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.recording-indicator {
  animation: pulse 1s infinite;
}

// Custom range slider styles
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;

  &::-webkit-slider-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
  }

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #3b82f6;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #2563eb;
      transform: scale(1.1);
    }
  }

  &::-moz-range-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
    border: none;
  }

  &::-moz-range-thumb {
    background: #3b82f6;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #2563eb;
      transform: scale(1.1);
    }
  }
}

// Visualizer canvas styles
canvas {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

// Button styles
.button-outline-modal {
  padding: 0.5rem 1rem;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  border-radius: 0.5rem;
  background-color: transparent;
  transition: all 0.2s ease;

  &:hover {
    background-color: #eff6ff;
  }
}

// Status indicators
.status-recording {
  color: #dc2626;
  font-weight: 600;
}

.status-paused {
  color: #f59e0b;
  font-weight: 600;
}

// Timer display
.timer-display {
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--blue-2);
  letter-spacing: 0.05em;
}

// Control buttons
.control-button {
  border-radius: 50%;
  padding: 0.75rem;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

.record-button {
  background-color: #ef4444;
  color: white;

  &:hover {
    background-color: #dc2626;
  }
}

.pause-button {
  background-color: #f59e0b;
  color: white;

  &:hover {
    background-color: #d97706;
  }
}

.stop-button {
  background-color: #6b7280;
  color: white;

  &:hover {
    background-color: #4b5563;
  }
}

.play-button {
  background-color: #3b82f6;
  color: white;

  &:hover {
    background-color: #2563eb;
  }
}

// Action buttons
.action-button {
  padding: 0.5rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.delete-button {
  background-color: #ef4444;
  color: white;

  &:hover {
    background-color: #dc2626;
  }
}

.save-button {
  background-color: #10b981;
  color: white;

  &:hover {
    background-color: #059669;
  }
}

// Playback section
.playback-section {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

// Error states
.error-container {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  padding: 1rem;
}

.error-icon {
  height: 2rem;
  width: 2rem;
  color: #ef4444;
}

.error-title {
  color: #dc2626;
  font-weight: 500;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
}

// Loading spinner
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive adjustments
@media (max-width: 640px) {
  .container-dialog {
    padding: 1rem;
  }

  .control-button {
    padding: 0.5rem;
  }

  .timer-display {
    font-size: 1.25rem;
  }

  canvas {
    height: 60px;
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .container-dialog {
    background-color: var(--blue-2);
    color: #f9fafb;
  }

  canvas {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #6b7280;
  }

  .playback-section {
    background-color: #374151;
    border-color: #6b7280;
  }
}
