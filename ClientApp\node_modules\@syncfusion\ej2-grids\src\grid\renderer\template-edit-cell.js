/**
 * `TemplateEditCell` is used to handle template cell.
 *
 * @hidden
 */
var TemplateEditCell = /** @class */ (function () {
    function TemplateEditCell(parent) {
        this.parent = parent;
    }
    TemplateEditCell.prototype.read = function (element, value) {
        return value;
    };
    TemplateEditCell.prototype.write = function () {
        //
    };
    TemplateEditCell.prototype.destroy = function () {
        //
    };
    return TemplateEditCell;
}());
export { TemplateEditCell };
