/* stylelint-disable property-no-vendor-prefix */
/*! TreeView's bootstrap5 theme wise override definitions and variables */
/* stylelint-disable */
/*! TreeView icons */
.e-treeview .e-list-item div.e-icons:not(.e-icons-spinner).e-icon-expandable::before,
.e-treeview .e-list-item div.e-icons:not(.e-icons-spinner).e-icon-collapsible::before {
  content: "\e75c";
}
.e-treeview .e-sibling::before {
  content: "";
}
.e-treeview .e-popup .e-icons::before {
  content: "\e76a";
}
.e-treeview.e-drag-item .e-icons.e-drop-in::before {
  content: "\e768";
}
.e-treeview.e-drag-item .e-icons.e-drop-out::before {
  content: "\e839";
}
.e-treeview.e-drag-item .e-icons.e-drop-next::before {
  content: "\e736";
}
.e-treeview.e-drag-item .e-icons.e-no-drop::before {
  content: "\e839";
}

/* stylelint-disable property-no-vendor-prefix */
@-webkit-keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
.e-treeview {
  display: block;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
}
.e-treeview.e-virtualization {
  overflow: auto;
}
.e-treeview.e-virtualization .e-virtual-mask {
  display: block;
  margin-bottom: 20px;
}
.e-treeview.e-virtualization .e-ul {
  overflow: unset;
}
.e-treeview > .e-ul {
  -webkit-overflow-scrolling: touch;
  overflow: auto;
}
.e-treeview.e-text-wrap .e-list-text {
  white-space: normal;
  word-break: break-word;
}
.e-treeview.e-text-wrap.e-ie-wrap .e-list-text {
  word-break: break-all;
}
.e-treeview.e-text-wrap .e-editing .e-list-text,
.e-treeview.e-text-wrap .e-editing .e-list-text .e-input-group {
  max-width: calc(100% - 2px);
}
.e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-text {
  max-width: calc(100% - 27px);
}
.e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-icon + .e-list-text, .e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-img + .e-list-text {
  max-width: calc(100% - 60px);
}
.e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-icon + .e-list-img + .e-list-text {
  max-width: calc(100% - 88px);
}
.e-treeview.e-text-wrap .e-list-icon + .e-list-text,
.e-treeview.e-text-wrap .e-list-img + .e-list-text {
  max-width: calc(100% - 32px);
}
.e-treeview.e-text-wrap .e-list-icon + .e-list-img + .e-list-text {
  max-width: calc(100% - 60px);
}
.e-treeview .e-ul {
  margin: 0;
  padding: 0 0 0 12px;
}
.e-treeview .e-node-collapsed .e-list-item .e-fullrow,
.e-treeview .e-display-none {
  display: none;
}
.e-treeview .e-list-item {
  list-style: none;
  padding: 2px 0;
}
.e-treeview .e-list-item .e-ul {
  margin: 2px 0 -2px;
  padding: 0 0 0 20px;
}
.e-treeview .e-list-item.e-disable > .e-text-content,
.e-treeview .e-list-item.e-disable > .e-fullrow {
  -ms-touch-action: none;
  opacity: 0.5;
  pointer-events: none;
  touch-action: none;
}
.e-treeview .e-list-item div.e-icons.interaction {
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  border-radius: 15px;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}
.e-treeview .e-list-item .e-icons.e-icon-collapsible {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.e-treeview .e-list-item .e-icons.e-icons-spinner::before {
  content: none;
}
.e-treeview .e-icons .e-spinner-pane {
  position: relative;
}
.e-treeview .e-icons .e-treeview-spinner {
  position: absolute;
}
.e-treeview .e-icons-spinner {
  position: relative;
}
.e-treeview .e-text-content {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  border: 1px solid;
  cursor: pointer;
  margin: 0;
  padding: 0 12px 0 20px;
}
.e-treeview .e-text-content + .e-sibling {
  margin-top: 1px;
}
.e-treeview .e-fullrow {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  border: 1px solid;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  height: 38px;
  left: 0;
  overflow: hidden;
  position: absolute;
  width: 100%;
}
@supports (-webkit-overflow-scrolling: touch) {
  .e-treeview .e-fullrow {
    z-index: -1;
  }
}
.e-treeview .e-checkbox-wrapper {
  margin: 0 0 0 12px;
  pointer-events: all;
  position: relative;
}
.e-treeview .e-checkbox-wrapper + .e-list-icon, .e-treeview .e-checkbox-wrapper + .e-list-img {
  margin: 0 0 0 12px;
}
.e-treeview .e-checkbox-wrapper + .e-list-text {
  padding: 0 12px;
}
.e-treeview .e-checkbox-wrapper .e-ripple-container {
  bottom: -7px;
  height: 32px;
  left: -7px;
  right: -7px;
  top: -7px;
  width: 32px;
}
.e-treeview .e-list-text {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: inline-block;
  line-height: 36px;
  margin: 0;
  min-height: 36px;
  padding: 0 12px;
  text-decoration: none;
  vertical-align: middle;
}
.e-treeview .e-list-text .e-input-group {
  height: 30px;
  margin-top: 3px;
  min-width: 150px;
  vertical-align: bottom;
}
.e-treeview .e-list-text .e-input-group .e-input {
  height: 28px;
}
.e-treeview .e-navigable-text {
  padding: 0 12px;
}
.e-treeview .e-list-icon,
.e-treeview .e-list-img {
  display: inline-block;
  height: 20px;
  margin: 0 0 0 12px;
  vertical-align: middle;
  width: 20px;
}
.e-treeview .e-list-icon + .e-list-icon, .e-treeview .e-list-icon + .e-list-img,
.e-treeview .e-list-img + .e-list-icon,
.e-treeview .e-list-img + .e-list-img {
  margin: 0 0 0 12px;
}
.e-treeview .e-list-icon + .e-list-text,
.e-treeview .e-list-img + .e-list-text {
  padding: 0 12px;
}
.e-treeview .e-list-icon + .e-navigable-text,
.e-treeview .e-list-img + .e-navigable-text {
  padding: 0 12px;
}
.e-treeview .e-icon-collapsible,
.e-treeview .e-icon-expandable {
  display: inline-block;
  height: 20px;
  margin: 0 0 0 -20px;
  vertical-align: middle;
  width: 20px;
}
.e-treeview .e-icon-collapsible::before,
.e-treeview .e-icon-expandable::before {
  display: inline-block;
  padding: 0px;
}
.e-treeview .e-load {
  -webkit-animation: rotation 0.5s infinite linear;
  animation: rotation 0.5s infinite linear;
}
.e-treeview .e-sibling {
  width: 144px;
}
.e-treeview .e-sibling::before {
  left: 0;
  height: 2px;
  top: -1.5px;
}
.e-treeview .e-sibling,
.e-treeview .e-sibling::before {
  position: absolute;
  z-index: 2;
}
.e-treeview .e-popup {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  font-weight: normal;
  position: absolute;
  z-index: 99999;
}
.e-treeview .e-popup .e-content {
  border-radius: 4px;
  border-style: solid;
  border-width: 1px;
  font-size: 14px;
  padding: 4px;
}
.e-treeview .e-popup .e-icons {
  border: 1px solid transparent;
  cursor: pointer;
  display: inline-block;
  height: 26px;
  line-height: 18px;
  padding: 4px;
  width: 26px;
}
.e-treeview .e-popup .e-downtail::before, .e-treeview .e-popup .e-downtail::after {
  border: 10px solid transparent;
  content: "";
  height: 0;
  left: 8px;
  position: absolute;
  width: 0;
}
.e-treeview .e-popup .e-downtail::after {
  bottom: -18px;
}
.e-treeview.e-fullrow-wrap .e-text-content {
  pointer-events: none;
  position: relative;
}
.e-treeview.e-fullrow-wrap .e-icon-collapsible,
.e-treeview.e-fullrow-wrap .e-icon-expandable,
.e-treeview.e-fullrow-wrap .e-input,
.e-treeview.e-fullrow-wrap .e-list-url {
  pointer-events: auto;
}
.e-treeview .e-navigable .e-text-content {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.e-treeview .e-navigable .e-list-url {
  padding: 0;
  width: 100%;
}
.e-treeview .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap {
  padding: 0 0 0 12px;
}
.e-treeview .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon,
.e-treeview .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-img {
  margin: 0 12px 0 0;
}
.e-treeview .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon + .e-list-img {
  margin: 0 12px 0 0;
}
.e-treeview .e-navigable .e-anchor-wrap {
  padding: 0 0 0 12px;
}
.e-treeview .e-navigable .e-nav-wrapper {
  padding: 0;
}
.e-treeview .e-navigable .e-checkbox-wrapper + .e-list-text .e-nav-wrapper:not(:has(.e-list-icon)) {
  padding: 0;
}
.e-treeview .e-navigable .e-list-icon,
.e-treeview .e-navigable .e-list-img {
  margin: 0 12px 0 0;
}
.e-treeview.e-drag-item {
  overflow: visible;
  z-index: 10000;
}
.e-treeview.e-drag-item .e-text-content {
  float: left;
}
.e-treeview.e-drag-item .e-text-content .e-list-text {
  padding: 0px;
}
.e-treeview.e-drag-item .e-icon-collapsible,
.e-treeview.e-drag-item .e-icon-expandable {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  padding-left: 12px;
}
.e-treeview.e-drag-item .e-icon-collapsible::before,
.e-treeview.e-drag-item .e-icon-expandable::before {
  font-size: 12px;
}
.e-treeview.e-drag-item .e-drop-count {
  border: 1px solid;
  border-radius: 15px;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  font-size: 12px;
  line-height: normal;
  min-width: 12px;
  padding: 1px 3px 2px;
  margin-left: -12px;
  position: absolute;
  text-align: center;
  top: -10px;
}
.e-treeview.e-dragging .e-text-content,
.e-treeview.e-dragging .e-fullrow {
  cursor: default;
}
.e-treeview.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap {
  padding: 0 12px 0 0;
}
.e-treeview.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon,
.e-treeview.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-img {
  margin: 0 0 0 12px;
}
.e-treeview.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon + .e-list-img {
  margin: 0 0 0 12px;
}
.e-treeview.e-rtl .e-navigable .e-anchor-wrap {
  padding: 0 12px 0 0;
}
.e-treeview.e-rtl .e-navigable .e-nav-wrapper {
  padding: 0;
}
.e-treeview.e-rtl .e-navigable .e-list-icon,
.e-treeview.e-rtl .e-navigable .e-list-img,
.e-treeview.e-rtl .e-navigable .e-list-icon + .e-list-img {
  margin: 0 0 0 12px;
}
.e-treeview.e-rtl .e-ul {
  padding: 0 12px 0 0;
}
.e-treeview.e-rtl .e-list-item .e-ul {
  padding: 0 20px 0 0;
}
.e-treeview.e-rtl .e-text-content {
  padding: 0 20px 0 0;
}
.e-treeview.e-rtl .e-checkbox-wrapper {
  margin: 0 12px 0 0;
}
.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-icon, .e-treeview.e-rtl .e-checkbox-wrapper + .e-list-img {
  margin: 0 12px 0 0;
}
.e-treeview.e-rtl .e-list-icon,
.e-treeview.e-rtl .e-list-img {
  margin: 0 12px 0 0;
}
.e-treeview.e-rtl .e-list-icon + .e-list-icon, .e-treeview.e-rtl .e-list-icon + .e-list-img,
.e-treeview.e-rtl .e-list-img + .e-list-icon,
.e-treeview.e-rtl .e-list-img + .e-list-img {
  margin: 0 12px 0 0;
}
.e-treeview.e-rtl .e-icon-collapsible,
.e-treeview.e-rtl .e-icon-expandable {
  margin: 0 -20px 0 0;
}
.e-treeview.e-rtl .e-sibling::before {
  right: 0;
}
.e-treeview.e-rtl.e-drag-item .e-icons.e-drop-next {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.e-treeview.e-rtl.e-drag-item .e-text-content {
  float: right;
}
.e-treeview.e-rtl.e-drag-item .e-drop-count {
  margin-right: -12px;
}
.e-treeview.e-rtl div.e-icons {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.e-treeview.e-disabled .e-fullrow,
.e-treeview.e-disabled .e-icons,
.e-treeview.e-disabled .e-text-content,
.e-treeview.e-disabled .e-list-img,
.e-treeview.e-disabled .e-list-icon {
  cursor: auto;
}
.e-treeview.e-disabled .e-list-url {
  cursor: default;
  pointer-events: none;
}
.e-treeview.e-interaction.e-fullrow-wrap .e-text-content {
  pointer-events: auto;
}

/* stylelint-enable property-no-vendor-prefix */
/* stylelint-disable property-no-vendor-prefix */
.e-treeview {
  -webkit-tap-highlight-color: transparent;
  /* stylelint-enable property-no-vendor-prefix */
}
.e-treeview .e-text-content,
.e-treeview .e-fullrow {
  border-color: transparent;
}
.e-treeview .e-list-text {
  color: #201f1e;
  font-size: 14px;
}
.e-treeview .e-list-icon,
.e-treeview .e-list-img {
  font-size: 20px;
}
.e-treeview .e-icon-collapsible,
.e-treeview .e-icon-expandable {
  color: #605e5c;
}
.e-treeview .e-icon-collapsible::before,
.e-treeview .e-icon-expandable::before {
  font-size: 20px;
}
.e-treeview .e-list-item.e-hover, .e-treeview .e-list-item.e-node-focus {
  background: transparent;
}
.e-treeview .e-list-item.e-hover > .e-fullrow, .e-treeview .e-list-item.e-node-focus > .e-fullrow {
  background-color: #f3f2f1;
  border-color: #f3f2f1;
}
.e-treeview .e-list-item.e-hover > .e-text-content, .e-treeview .e-list-item.e-node-focus > .e-text-content {
  color: #201f1e;
}
.e-treeview .e-list-item.e-hover > .e-text-content .e-list-text, .e-treeview .e-list-item.e-node-focus > .e-text-content .e-list-text {
  color: #201f1e;
}
.e-treeview .e-list-item.e-hover > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-hover > .e-text-content .e-icon-expandable, .e-treeview .e-list-item.e-node-focus > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-node-focus > .e-text-content .e-icon-expandable {
  color: #323130;
}
.e-treeview .e-list-item.e-active {
  background: transparent;
}
.e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #edebe9;
  border-color: #edebe9;
}
.e-treeview .e-list-item.e-active.e-animation-active > .e-fullrow {
  background-color: transparent;
  border-color: transparent;
}
.e-treeview .e-list-item.e-active.e-animation-active > .e-text-content {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active.e-animation-active > .e-text-content .e-list-text {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active > .e-text-content {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-active > .e-text-content .e-icon-expandable {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active > .e-text-content .e-check {
  border-color: #201f1e;
}
.e-treeview .e-list-item.e-active > .e-text-content .e-stop {
  border-color: #201f1e;
}
.e-treeview .e-list-item.e-active.e-hover > .e-fullrow, .e-treeview .e-list-item.e-active.e-node-focus > .e-fullrow {
  background-color: rgb(225.525, 222.25, 218.975);
  border-color: #edebe9;
}
.e-treeview .e-list-item.e-active.e-hover > .e-text-content, .e-treeview .e-list-item.e-active.e-node-focus > .e-text-content {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-list-text, .e-treeview .e-list-item.e-active.e-node-focus > .e-text-content .e-list-text {
  color: #201f1e;
}
.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-icon-expandable, .e-treeview .e-list-item.e-active.e-node-focus > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-active.e-node-focus > .e-text-content .e-icon-expandable {
  color: #201f1e;
}
.e-treeview .e-list-item.e-editing.e-active > .e-fullrow, .e-treeview .e-list-item.e-editing.e-hover > .e-fullrow, .e-treeview .e-list-item.e-editing.e-node-focus > .e-fullrow {
  background-color: transparent;
  border-color: transparent;
}
.e-treeview .e-list-item.e-editing.e-active > .e-text-content, .e-treeview .e-list-item.e-editing.e-hover > .e-text-content, .e-treeview .e-list-item.e-editing.e-node-focus > .e-text-content {
  color: #201f1e;
}
.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-list-text, .e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-list-text, .e-treeview .e-list-item.e-editing.e-node-focus > .e-text-content .e-list-text {
  color: #201f1e;
}
.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-icon-expandable, .e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-icon-expandable, .e-treeview .e-list-item.e-editing.e-node-focus > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-editing.e-node-focus > .e-text-content .e-icon-expandable {
  color: #605e5c;
}
.e-treeview .e-list-item.e-disable > .e-text-content,
.e-treeview .e-list-item.e-disable > .e-fullrow {
  color: #a19f9d;
}
.e-treeview .e-list-item.e-disable > .e-text-content .e-list-text,
.e-treeview .e-list-item.e-disable > .e-fullrow .e-list-text {
  color: #a19f9d;
}
.e-treeview .e-list-item.e-disable > .e-text-content > .e-icon-collapsible,
.e-treeview .e-list-item.e-disable > .e-text-content > .e-icon-expandable {
  color: #a19f9d;
}
.e-treeview .e-sibling {
  border-top: 2px solid #0078d4;
}
.e-treeview .e-sibling::before {
  background: #0078d4;
}
.e-treeview .e-popup .e-content {
  background-color: #f3f2f1;
  border-color: #f3f2f1;
}
.e-treeview .e-popup.e-select .e-icons {
  border-color: #f3f2f1;
}
.e-treeview .e-popup .e-downtail::before {
  border-top-color: #f3f2f1;
}
.e-treeview .e-popup .e-downtail::after {
  border-top-color: #f3f2f1;
}
.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-hover > .e-text-content, .e-treeview:not(.e-fullrow-wrap) .e-list-item.e-node-focus > .e-text-content {
  background-color: #f3f2f1;
  border-color: #f3f2f1;
}
.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active > .e-text-content {
  background-color: #edebe9;
  border-color: #edebe9;
}
.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active.e-hover > .e-text-content, .e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active.e-node-focus > .e-text-content {
  background-color: rgb(225.525, 222.25, 218.975);
  border-color: #edebe9;
}
.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-active > .e-text-content, .e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-hover > .e-text-content, .e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-node-focus > .e-text-content {
  background-color: transparent;
  border-color: transparent;
}
.e-treeview.e-fullrow-wrap .e-text-content {
  border-color: transparent;
}
.e-treeview.e-drag-item {
  background-color: #f3f2f1;
  font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Helvetica Neue", sans-serif;
}
.e-treeview.e-drag-item .e-icon-collapsible::before,
.e-treeview.e-drag-item .e-icon-expandable::before {
  font-size: 12px;
}
.e-treeview.e-drag-item .e-list-text {
  color: #323130;
}
.e-treeview.e-drag-item .e-icons {
  color: #323130;
}
.e-treeview.e-drag-item .e-drop-count {
  background-color: #0078d4;
  border-color: #fff;
  color: #fff;
}
.e-treeview.e-drag-item.e-rtl .e-sibling {
  border: 1px solid #0078d4;
}

.e-bigger .e-treeview .e-ul,
.e-treeview.e-bigger .e-ul {
  padding: 0 0 0 16px;
}
.e-bigger .e-treeview .e-list-item .e-ul,
.e-treeview.e-bigger .e-list-item .e-ul {
  padding: 0 0 0 24px;
}
.e-bigger .e-treeview .e-list-item .e-text-content,
.e-treeview.e-bigger .e-list-item .e-text-content {
  padding: 0 0 0 24px;
}
.e-bigger .e-treeview .e-list-item .e-list-icon,
.e-bigger .e-treeview .e-list-item .e-list-img,
.e-treeview.e-bigger .e-list-item .e-list-icon,
.e-treeview.e-bigger .e-list-item .e-list-img {
  height: 24px;
  width: 24px;
}
.e-bigger .e-treeview .e-list-item .e-small.e-css.e-checkbox-wrapper .e-frame,
.e-treeview.e-bigger .e-list-item .e-small.e-css.e-checkbox-wrapper .e-frame {
  height: 20px;
  line-height: 17px;
  width: 20px;
}
.e-bigger .e-treeview .e-list-item .e-small.e-css.e-checkbox-wrapper .e-check, .e-bigger .e-treeview .e-list-item .e-small.e-css.e-checkbox-wrapper .e-stop,
.e-treeview.e-bigger .e-list-item .e-small.e-css.e-checkbox-wrapper .e-check,
.e-treeview.e-bigger .e-list-item .e-small.e-css.e-checkbox-wrapper .e-stop {
  font-size: 12px;
}
.e-bigger .e-treeview .e-list-item .e-small.e-css.e-checkbox-wrapper .e-stop,
.e-treeview.e-bigger .e-list-item .e-small.e-css.e-checkbox-wrapper .e-stop {
  line-height: 17px;
}
.e-bigger .e-treeview .e-list-item .e-checkbox-wrapper + .e-list-text,
.e-treeview.e-bigger .e-list-item .e-checkbox-wrapper + .e-list-text {
  padding: 0 16px;
}
.e-bigger .e-treeview .e-icon-collapsible,
.e-bigger .e-treeview .e-icon-expandable,
.e-treeview.e-bigger .e-icon-collapsible,
.e-treeview.e-bigger .e-icon-expandable {
  height: 24px;
  width: 24px;
  margin: 0 0 0 -24px;
}
.e-bigger .e-treeview.e-drag-item .e-icon-expandable,
.e-bigger .e-treeview.e-drag-item .e-icon-collapsible,
.e-treeview.e-bigger.e-drag-item .e-icon-expandable,
.e-treeview.e-bigger.e-drag-item .e-icon-collapsible {
  padding-left: 24px;
  padding-top: 10px;
}
.e-bigger .e-treeview.e-drag-item .e-drop-count,
.e-treeview.e-bigger.e-drag-item .e-drop-count {
  padding: 3px 5px 4px;
}
.e-bigger .e-treeview .e-navigable .e-list-text,
.e-treeview.e-bigger .e-navigable .e-list-text {
  padding: 0;
  width: 100%;
}
.e-bigger .e-treeview .e-navigable .e-checkbox-wrapper + .e-list-text,
.e-treeview.e-bigger .e-navigable .e-checkbox-wrapper + .e-list-text {
  padding: 0;
}
.e-bigger .e-treeview .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon,
.e-bigger .e-treeview .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-img,
.e-treeview.e-bigger .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon,
.e-treeview.e-bigger .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-img {
  margin: 0 16px 0 12px;
}
.e-bigger .e-treeview .e-navigable .e-anchor-wrap,
.e-treeview.e-bigger .e-navigable .e-anchor-wrap {
  padding: 0 0 0 12px;
}
.e-bigger .e-treeview .e-navigable .e-list-icon,
.e-bigger .e-treeview .e-navigable .e-list-img,
.e-bigger .e-treeview .e-navigable .e-list-icon + .e-list-img,
.e-treeview.e-bigger .e-navigable .e-list-icon,
.e-treeview.e-bigger .e-navigable .e-list-img,
.e-treeview.e-bigger .e-navigable .e-list-icon + .e-list-img {
  margin: 0 12px 0 0;
}
.e-bigger .e-treeview .e-fullrow,
.e-treeview.e-bigger .e-fullrow {
  height: 44px;
}
.e-bigger .e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-checkbox-wrapper + .e-list-text {
  max-width: calc(100% - 33px);
}
.e-bigger .e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-icon + .e-list-text, .e-bigger .e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-img + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-checkbox-wrapper + .e-list-icon + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-checkbox-wrapper + .e-list-img + .e-list-text {
  max-width: calc(100% - 70px);
}
.e-bigger .e-treeview.e-text-wrap .e-checkbox-wrapper + .e-list-icon + .e-list-img + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-checkbox-wrapper + .e-list-icon + .e-list-img + .e-list-text {
  max-width: calc(100% - 102px);
}
.e-bigger .e-treeview.e-text-wrap .e-list-icon + .e-list-text,
.e-bigger .e-treeview.e-text-wrap .e-list-img + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-list-icon + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-list-img + .e-list-text {
  max-width: calc(100% - 36px);
}
.e-bigger .e-treeview.e-text-wrap .e-list-icon + .e-list-img + .e-list-text,
.e-treeview.e-bigger.e-text-wrap .e-list-icon + .e-list-img + .e-list-text {
  max-width: calc(100% - 69px);
}
.e-bigger .e-treeview .e-list-text,
.e-treeview.e-bigger .e-list-text {
  line-height: 40px;
  min-height: 40px;
}
.e-bigger .e-treeview .e-list-text .e-input-group,
.e-treeview.e-bigger .e-list-text .e-input-group {
  height: 40px;
}
.e-bigger .e-treeview .e-list-text .e-input-group .e-input,
.e-treeview.e-bigger .e-list-text .e-input-group .e-input {
  height: 36px;
}
.e-bigger .e-treeview .e-checkbox-wrapper,
.e-treeview.e-bigger .e-checkbox-wrapper {
  margin: 0 0 0 16px;
}
.e-bigger .e-treeview .e-checkbox-wrapper + .e-list-icon, .e-bigger .e-treeview .e-checkbox-wrapper + .e-list-img,
.e-treeview.e-bigger .e-checkbox-wrapper + .e-list-icon,
.e-treeview.e-bigger .e-checkbox-wrapper + .e-list-img {
  margin: 0 0 0 16px;
}
.e-bigger .e-treeview .e-list-icon,
.e-bigger .e-treeview .e-list-img,
.e-treeview.e-bigger .e-list-icon,
.e-treeview.e-bigger .e-list-img {
  margin: 0 0 0 16px;
}
.e-bigger .e-treeview .e-list-icon + .e-list-text,
.e-bigger .e-treeview .e-list-img + .e-list-text,
.e-treeview.e-bigger .e-list-icon + .e-list-text,
.e-treeview.e-bigger .e-list-img + .e-list-text {
  padding: 0 16px;
}
.e-bigger .e-treeview .e-list-icon + .e-list-icon, .e-bigger .e-treeview .e-list-icon + .e-list-img,
.e-bigger .e-treeview .e-list-img + .e-list-icon,
.e-bigger .e-treeview .e-list-img + .e-list-img,
.e-treeview.e-bigger .e-list-icon + .e-list-icon,
.e-treeview.e-bigger .e-list-icon + .e-list-img,
.e-treeview.e-bigger .e-list-img + .e-list-icon,
.e-treeview.e-bigger .e-list-img + .e-list-img {
  margin: 0 0 0 16px;
}
.e-bigger .e-treeview .e-icon-collapsible::before,
.e-bigger .e-treeview .e-icon-expandable::before,
.e-treeview.e-bigger .e-icon-collapsible::before,
.e-treeview.e-bigger .e-icon-expandable::before {
  padding: 0px;
}
.e-bigger .e-treeview.e-drag-item .e-text-content,
.e-treeview.e-bigger.e-drag-item .e-text-content {
  padding-left: 16px;
}
.e-bigger .e-treeview.e-drag-item .e-icon-collapsible,
.e-bigger .e-treeview.e-drag-item .e-icon-expandable,
.e-treeview.e-bigger.e-drag-item .e-icon-collapsible,
.e-treeview.e-bigger.e-drag-item .e-icon-expandable {
  font-size: 14px;
}
.e-bigger .e-treeview.e-drag-item .e-icon-collapsible::before,
.e-bigger .e-treeview.e-drag-item .e-icon-expandable::before,
.e-treeview.e-bigger.e-drag-item .e-icon-collapsible::before,
.e-treeview.e-bigger.e-drag-item .e-icon-expandable::before {
  font-size: 14px;
}
.e-bigger .e-treeview.e-rtl .e-ul,
.e-treeview.e-bigger.e-rtl .e-ul {
  padding: 0 16px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-list-item .e-ul,
.e-treeview.e-bigger.e-rtl .e-list-item .e-ul {
  padding: 0 24px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-list-item .e-text-content,
.e-treeview.e-bigger.e-rtl .e-list-item .e-text-content {
  padding: 0 24px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-icon-collapsible,
.e-bigger .e-treeview.e-rtl .e-icon-expandable,
.e-treeview.e-bigger.e-rtl .e-icon-collapsible,
.e-treeview.e-bigger.e-rtl .e-icon-expandable {
  margin: 0 -24px 0 0;
}
.e-bigger .e-treeview.e-rtl.e-drag-item .e-text-content,
.e-treeview.e-bigger.e-rtl.e-drag-item .e-text-content {
  padding-left: 0;
  padding-right: 16px;
}
.e-bigger .e-treeview.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-img,
.e-treeview.e-bigger.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-navigable .e-checkbox-wrapper + .e-list-url .e-anchor-wrap .e-list-img {
  margin: 0 10px 0 14px;
}
.e-bigger .e-treeview.e-rtl .e-navigable .e-anchor-wrap,
.e-treeview.e-bigger.e-rtl .e-navigable .e-anchor-wrap {
  padding: 0 12px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-navigable .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-navigable .e-list-img,
.e-bigger .e-treeview.e-rtl .e-navigable .e-list-icon + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-navigable .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-navigable .e-list-img,
.e-treeview.e-bigger.e-rtl .e-navigable .e-list-icon + .e-list-img {
  margin: 0 0 0 12px;
}
.e-bigger .e-treeview.e-rtl .e-checkbox-wrapper,
.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper {
  margin: 0 16px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-checkbox-wrapper + .e-list-icon, .e-bigger .e-treeview.e-rtl .e-checkbox-wrapper + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper + .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper + .e-list-img {
  margin: 0 16px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-list-img,
.e-treeview.e-bigger.e-rtl .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-list-img {
  margin: 0 16px 0 0;
}
.e-bigger .e-treeview.e-rtl .e-list-icon + .e-list-icon, .e-bigger .e-treeview.e-rtl .e-list-icon + .e-list-img,
.e-bigger .e-treeview.e-rtl .e-list-img + .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-list-img + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-list-icon + .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-list-icon + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-list-img + .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-list-img + .e-list-img {
  margin: 0 16px 0 0;
}

.e-bigger .e-treeview .e-list-text,
.e-treeview.e-bigger .e-list-text {
  font-size: 16px;
}
.e-bigger .e-treeview .e-icon-collapsible::before,
.e-bigger .e-treeview .e-icon-expandable::before,
.e-treeview.e-bigger .e-icon-collapsible::before,
.e-treeview.e-bigger .e-icon-expandable::before {
  font-size: 24px;
}