.e-tab .e-tab-header .e-close-icon::before {
  content: "\e7fc";
  position: relative;
}
.e-tab .e-tab-header .e-popup-up-icon::before {
  content: "\e988";
}
.e-tab .e-tab-header .e-popup-down-icon::before {
  content: "\e968";
}
@media screen and (max-width: 480px) {
  .e-tab.e-vertical-icon .e-tab-header .e-popup-up-icon::before {
    content: "\e918";
  }
  .e-tab.e-vertical-icon .e-tab-header .e-popup-down-icon::before {
    content: "\e968";
  }
}
.e-tab.e-vertical-tab .e-tab-header .e-popup-up-icon::before {
  content: "More";
}
.e-tab.e-vertical-tab .e-tab-header .e-popup-up-icon::after {
  content: "\eb04";
}
.e-tab.e-vertical-tab .e-tab-header .e-popup-down-icon::before {
  content: "More";
}
.e-tab.e-vertical-tab .e-tab-header .e-popup-down-icon::after {
  content: "\e84f";
}
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-up-icon::before {
  content: "";
}
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-up-icon::after {
  content: "\ebb5";
}
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-down-icon::before {
  content: "";
}
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-down-icon::after {
  content: "\ebb5";
}

.e-tab-clone-element .e-close-icon::before {
  content: "\e7fc";
  position: relative;
}

.e-tab {
  display: block;
  position: relative;
}
.e-tab.e-hidden {
  display: none;
}
.e-tab.e-fill-mode .e-content, .e-tab.e-fill-mode .e-content .e-item, .e-tab.e-fill-mode .e-content .e-item > div, .e-tab.e-fill-mode .e-content .e-item > div > .e-blazor-template {
  height: 100%;
}
.e-tab .e-tab-header {
  height: 40px;
  min-height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab .e-tab-header::before {
  content: "";
  position: absolute;
  display: block;
}
.e-tab .e-tab-header:not(.e-vertical)::before {
  bottom: 0;
  top: 0;
  width: 100%;
}
.e-tab .e-tab-header .e-toolbar-items {
  height: auto;
  margin: 0;
  min-height: 40px;
  position: relative;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child, .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}
.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px 0 0;
  }
}
.e-tab .e-tab-header .e-toolbar-items .e-toolbar-item.e-active {
  position: unset;
}
.e-tab .e-tab-header .e-toolbar-items .e-toolbar-item.e-active:hover::before {
  left: unset;
  right: unset;
}
.e-tab .e-tab-header .e-toolbar-items .e-toolbar-item.e-active::before {
  background-color: unset;
  bottom: unset;
  content: unset;
  height: unset;
  left: unset;
  position: unset;
  right: unset;
  -webkit-transition: unset;
  transition: unset;
}
.e-tab .e-tab-header .e-toolbar-items .e-toolbar-item.e-active::after {
  color: unset;
  content: unset;
  display: unset;
  font-weight: unset;
  height: unset;
  overflow: unset;
  visibility: unset;
}
.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-scroll-device {
  padding: 0;
}
.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-scroll-device .e-scroll-right-nav {
  display: none;
}
.e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-hscroll-bar {
  padding: 0;
}
.e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav {
  width: 40px;
  border-radius: 0;
}
.e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 2px 0 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px 0 0;
  }
}
.e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0 2px 0 0;
}
.e-tab .e-tab-header .e-hscroll-bar {
  overflow: hidden;
}
.e-tab .e-tab-header .e-indicator {
  display: none;
  position: absolute;
}
.e-tab .e-tab-header .e-indicator.e-hidden {
  display: none;
}
.e-tab .e-tab-header:not(.e-vertical) .e-indicator {
  bottom: 0;
  height: 2px;
  left: 0;
  right: 0;
  border-radius: 0;
  -webkit-transition: left 0.125s cubic-bezier(0.35, 0, 0.25, 1), right 0.25s cubic-bezier(0.35, 0, 0.25, 1);
  transition: left 0.125s cubic-bezier(0.35, 0, 0.25, 1), right 0.25s cubic-bezier(0.35, 0, 0.25, 1);
}
.e-tab .e-tab-header .e-toolbar-item.e-hidden {
  display: none;
}
.e-tab .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 40px;
  margin: 0 2px 0 0;
  min-height: 40px;
  min-width: auto;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 15px;
  width: 100%;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 15px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-text-wrap {
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-text {
  display: inherit;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
  -ms-flex-item-align: auto;
      align-self: auto;
  margin-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-text {
    font-size: 15px;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-active {
  margin: 0 2px 0 0;
  padding-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active {
    margin: 0 2px 0 0;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
  margin-top: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 400;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-icon::before {
  top: 2px;
  left: 2px;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-icon-top.e-tab-icon::before,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-icon-bottom.e-tab-icon::before {
  left: 0;
}
.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 40px;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
  display: none;
  margin: 0 0 0 8px;
  min-width: 12px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-close-icon,
.e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
  font-size: 12px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon,
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
    font-size: 12px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
  top: 1px;
}
.e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 20px;
  min-width: 20px;
  width: 20px;
  line-height: 1;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
    height: 20px;
    width: 20px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
  font-size: 16px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
  .e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
    font-size: 20px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
  position: relative;
  top: 2px;
  left: 2px;
}
.e-tab .e-tab-header .e-toolbar-item .e-icon-top.e-tab-icon::before,
.e-tab .e-tab-header .e-toolbar-item .e-icon-bottom.e-tab-icon::before {
  left: 0;
}
.e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 15px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
    padding: 0 20px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-icon-right {
  margin: 0 0 0 8px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icon-right {
    margin: 0;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
  margin: 0 0 0 8px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
    margin: 0 0 0 10px;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-item.e-ibottom {
  height: 62px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-item.e-ibottom {
    height: 62px;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 62px;
  position: relative;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 62px;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab .e-tab-header .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  display: block;
  height: auto;
}
.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap .e-text-wrap {
  height: auto;
}
.e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
  position: absolute;
  right: 15px;
  top: calc(50% - 6px);
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
    right: 12px;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
  margin: 8px 0 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
    margin: 10px 0 0;
  }
}
.e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0 0 8px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0 0 10px;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon.e-icon-top,
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon.e-icon-bottom {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: auto;
}
.e-tab .e-tab-header .e-toolbar-pop {
  overflow-y: auto;
  padding: 5px 0;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item {
  height: 26px;
  min-height: 26px;
  min-width: auto;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  margin: 0;
  padding: 0;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
  height: 26px;
  padding: 0 20px;
  text-align: initial;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 24px;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
  height: 26px;
  width: 100%;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  margin: 0 0 0 8px;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before {
  top: 0;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style="display:block"] {
  padding-right: 10px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style="display:block"] {
    padding-right: 0;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom {
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom {
    height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  min-height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
    min-height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 50px;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-text-wrap {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: auto;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  display: block;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 0 0 10px;
  padding-left: 0;
  padding-right: 10px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin: 0 0 0 12px;
    padding-left: 0;
    padding-right: 0;
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  right: 20px;
  top: initial;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-icon.e-icon-top,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-icon.e-icon-bottom {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.e-tab .e-tab-header.e-close-show .e-icons.e-close-icon {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-item-align: center;
      align-self: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-close-show .e-icons.e-close-icon {
    display: none;
  }
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-active .e-close-icon {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}
.e-tab .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 20px;
}
.e-tab .e-tab-header .e-scroll-nav {
  height: 40px;
  min-height: 40px;
  min-width: auto;
  width: auto;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab .e-tab-header .e-scroll-nav.e-scroll-left-nav {
  padding: 0;
}
.e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav {
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav {
    padding: 0;
  }
}
.e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  font-size: 16px;
  line-height: 34px;
  position: relative;
  top: 0;
  vertical-align: initial;
}
.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow {
  font-size: 16px;
  height: 34px;
  line-height: 34px;
  width: 34px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}
.e-tab .e-tab-header .e-hor-nav {
  height: 40px;
  min-height: 40px;
  min-width: auto;
  padding: 0;
  width: auto;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav {
    height: 50px;
    min-height: 50px;
    padding: 0;
  }
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
  font-size: 16px;
  height: 34px;
  line-height: 34px;
  width: 34px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before {
  font-size: 16px;
  line-height: 34px;
  position: relative;
  top: 0;
  vertical-align: initial;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 16px;
  }
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  line-height: 34px;
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
  line-height: 34px;
  top: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
    line-height: 43px;
  }
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  line-height: 32px;
}
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
  line-height: 32px;
  top: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
  .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
    line-height: 43px;
  }
}
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover {
  line-height: 32px;
  top: -0.5px;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items .e-toolbar-item.e-active {
  position: unset;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items .e-toolbar-item.e-active:hover::before {
  left: unset;
  right: unset;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items .e-toolbar-item.e-active::before {
  background-color: unset;
  content: unset;
  height: unset;
  left: unset;
  position: unset;
  right: unset;
  top: unset;
  -webkit-transition: unset;
  transition: unset;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items .e-toolbar-item.e-active::after {
  color: unset;
  content: unset;
  display: unset;
  font-weight: unset;
  height: unset;
  overflow: unset;
  visibility: unset;
}
.e-tab .e-tab-header.e-horizontal-bottom::before {
  bottom: auto;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-hscroll-bar {
  margin-top: -1px;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 2px 0 0;
  padding-bottom: 0;
  padding-top: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px 0 0;
  }
}
.e-tab .e-tab-header.e-horizontal-bottom .e-indicator {
  bottom: auto;
  top: 0;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0 2px 0 0;
  padding-bottom: 0;
  padding-top: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
    margin: 0 2px 0 0;
  }
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap::before, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:not(.e-separator):last-child .e-text-wrap::before {
  top: unset;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-tab-wrap:focus .e-close-icon::before {
  top: 0;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: initial;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap {
  height: initial;
  padding: 0;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap::before, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap::before {
  bottom: auto;
  top: 0;
}
.e-tab .e-tab-header.e-reorder-active-item .e-toolbar-pop .e-toolbar-item.e-active .e-text-wrap::before {
  display: none;
}
.e-tab .e-tab-header.e-vertical {
  max-width: 150px;
  z-index: 1;
}
.e-tab .e-tab-header.e-vertical::before {
  bottom: 0;
  height: 100%;
  left: 0;
  top: 0;
}
.e-tab .e-tab-header.e-vertical[style*="overflow: hidden"]::before {
  bottom: 15px;
  height: auto;
  top: 15px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-vertical[style*="overflow: hidden"]::before {
    bottom: 0;
    top: 0;
  }
}
.e-tab .e-tab-header.e-vertical .e-indicator {
  display: none;
  -webkit-transition: top 0.125s cubic-bezier(0.35, 0, 0.25, 1), bottom 0.25s cubic-bezier(0.35, 0, 0.25, 1);
  transition: top 0.125s cubic-bezier(0.35, 0, 0.25, 1), bottom 0.25s cubic-bezier(0.35, 0, 0.25, 1);
  width: 2px;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: normal;
      -ms-flex-align: normal;
          align-items: normal;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device) {
  padding: 16px 0;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item:last-child {
  margin: 0;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-text-wrap {
  position: relative;
  width: 100%;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-text,
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-icon::before {
  text-align: center;
  width: 100%;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-active .e-text-wrap::before {
  display: none;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon {
  min-height: 40px;
  min-width: 33px;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon .e-tab-icon {
  margin: 0;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  right: -15px;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-pop {
  top: initial !important; /* stylelint-disable-line declaration-no-important */
}
.e-tab .e-tab-header.e-vertical.e-vertical-left {
  float: left;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left::before {
  right: 0;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left .e-indicator {
  left: auto;
  right: 0;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-items .e-toolbar-item.e-active:hover::before {
  bottom: unset;
  height: unset;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-items .e-toolbar-item.e-active::before {
  background-color: unset;
  bottom: unset;
  content: unset;
  height: unset;
  left: unset;
  position: unset;
  right: unset;
  -webkit-transition: unset;
  transition: unset;
  width: unset;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-items .e-toolbar-item.e-active::after {
  color: unset;
  content: unset;
  display: unset;
  font-weight: unset;
  height: unset;
  overflow: unset;
  visibility: unset;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right {
  float: right;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right::before {
  right: auto;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-indicator {
  left: 0;
  right: auto;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-tab-wrap {
  text-align: right;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-pop .e-tab-text {
  width: auto;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-items .e-toolbar-item.e-active:hover::before {
  bottom: unset;
  height: unset;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-items .e-toolbar-item.e-active::before {
  background-color: unset;
  bottom: unset;
  content: unset;
  height: unset;
  left: unset;
  position: unset;
  right: unset;
  -webkit-transition: unset;
  transition: unset;
  width: unset;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-items .e-toolbar-item.e-active::after {
  color: unset;
  content: unset;
  display: unset;
  font-weight: unset;
  height: unset;
  overflow: unset;
  visibility: unset;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav {
  height: 16px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 16px;
  width: 100%;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  margin: 0 auto;
  width: 16px;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 15px;
  width: 100%;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  height: 100%;
  -webkit-transform: none;
          transform: none;
  -webkit-transition: none;
  transition: none;
  width: 100%;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  float: left;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  line-height: 36px;
  text-align: left;
  text-transform: capitalize;
  -webkit-transform: none;
          transform: none;
  -webkit-transition: none;
  transition: none;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  float: left;
  font-size: 12px;
  line-height: 36px;
  margin: 0 0 0 8px;
  vertical-align: initial;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
  .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
    font-size: 15px;
  }
}
.e-tab .e-tab-header.e-vertical .e-scroll-device .e-scroll-nav,
.e-tab .e-tab-header.e-vertical .e-scroll-device .e-scroll-overlay {
  display: none;
}
.e-tab .e-tab-header.e-vertical.e-toolpop .e-toolbar-items {
  height: auto;
}
.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator).e-itop .e-close-icon, .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: -15px;
}
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 15px;
}
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  float: right;
}
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  float: right;
  margin: 0 8px 0 0;
}
.e-tab .e-content {
  position: relative;
}
.e-tab .e-content .e-item.e-view {
  bottom: 0;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
}
.e-tab .e-content > .e-item {
  display: none;
}
.e-tab .e-content > .e-item.e-active {
  background: inherit;
  display: block;
}
.e-tab .e-content.e-progress {
  overflow: hidden;
}
.e-tab.e-vertical-tab .e-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.e-tab.e-vertical-tab .e-content .e-item.e-active {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
}
.e-tab.e-vertical-tab .e-content .e-item,
.e-tab.e-vertical-tab .e-content .e-item > :first-child {
  width: 100%;
}
.e-tab.e-vertical-icon > .e-tab-header {
  height: 62px;
  min-height: 62px;
}
.e-tab.e-vertical-icon > .e-tab-header.e-reorder-active-item .e-toolbar-pop .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon > .e-tab-header.e-reorder-active-item .e-toolbar-pop .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  display: none;
}
.e-tab.e-vertical-icon > .e-tab-header > .e-toolbar-items {
  height: 62px;
}
.e-tab.e-vertical-icon > .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-vertical-icon > .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap {
  position: unset;
}
.e-tab.e-vertical-icon > .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before,
.e-tab.e-vertical-icon > .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap::before {
  border: unset;
  content: unset;
  display: none;
  position: unset;
  top: unset;
  width: unset;
}
.e-tab.e-vertical-icon > .e-tab-header .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon > .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  content: unset;
  display: unset;
  position: unset;
  top: unset;
  width: unset;
  border: unset;
}
.e-tab.e-vertical-icon > .e-tab-header .e-scroll-nav {
  height: 62px;
}
.e-tab.e-vertical-icon > .e-tab-header .e-hor-nav {
  height: 62px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-vertical-icon > .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-vertical-icon > .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 12px;
  }
}
.e-tab.e-vertical-icon > .e-tab-header.e-vertical .e-indicator {
  width: 2px;
}
.e-tab.e-vertical-icon > .e-tab-header.e-vertical .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon > .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  display: none;
}
.e-tab.e-vertical-icon > .e-tab-header.e-vertical .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-vertical-icon > .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap {
  position: relative;
}
.e-tab.e-vertical-icon > .e-tab-header.e-vertical .e-scroll-nav {
  height: 16px;
}
.e-tab.e-vertical-icon > .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon > .e-tab-header.e-horizontal-bottom .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  top: unset;
}
.e-tab.e-vertical-icon.e-rtl .e-tab-header .e-toolbar-item.e-active {
  margin: 0 0 0 2px;
}
.e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}
.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}
.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}
.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 62px;
}
.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus {
  height: 26px;
}
.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 26px;
}
.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 40px;
}
.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
  padding: 0;
}
.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab.e-template .e-tab-header .e-toolbar-items .e-toolbar-item .e-tab-wrap .e-text-wrap .e-tab-text > div,
.e-tab.e-template .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-text-wrap .e-tab-text > div {
  display: inherit;
}
.e-tab.e-template .e-content > .e-item {
  display: none;
}
.e-tab.e-template .e-content > .e-item.e-active {
  background: inherit;
  display: block;
}
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  display: block;
  line-height: 15px;
}
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  display: block;
  line-height: 14px;
}
.e-tab.e-ie .e-tab-header .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-popup-down-icon {
  -webkit-transform: none;
          transform: none;
  -webkit-transition: none;
  transition: none;
}
.e-tab.e-ie .e-tab-header .e-popup-up-icon::before {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab.e-ie .e-tab-header .e-popup-up-icon::before {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab.e-ie .e-tab-header .e-popup-down-icon::before {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab.e-ie .e-tab-header .e-popup-down-icon::before {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  display: block;
  line-height: 14px;
  position: relative;
}
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  display: block;
  line-height: 11px;
  position: relative;
}
.e-tab.e-edge .e-tab-header .e-popup-up-icon {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-up-icon {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab.e-edge .e-tab-header .e-popup-down-icon {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
@media screen and (max-width: 480px) and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab.e-edge .e-tab-header .e-popup-up-icon::before {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-up-icon::before {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab.e-edge .e-tab-header .e-popup-down-icon::before {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon::before {
    -webkit-transform: none;
            transform: none;
    -webkit-transition: none;
    transition: none;
  }
}
.e-tab.e-safari .e-tab-header .e-close-icon::before {
  top: -1px;
}
.e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before {
  top: 0;
}
.e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  top: 0;
}
.e-tab.e-disable {
  pointer-events: none;
}
.e-tab.e-fill .e-tab-header {
  height: 40px;
  min-height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab.e-fill .e-tab-header .e-indicator {
  display: none;
}
.e-tab.e-fill .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}
.e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child, .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 40px;
  margin: 0 2px 0 0;
  min-height: 40px;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 62px;
  min-height: 62px;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 15px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 15px;
  }
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 400;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 62px;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-text-wrap, .e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  height: auto;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-active, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  padding: 0;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item.e-itop, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-itop, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-itop {
  padding-top: 0;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin-right: 0;
  padding: 0;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: 44px;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}
.e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}
.e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 62px;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
@media screen and (max-width: 480px) {
  .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
    height: auto;
  }
}
.e-tab.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}
.e-tab.e-fill.e-vertical-icon > .e-tab-header {
  height: 60px;
  min-height: 60px;
}
.e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
  padding: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}
@media screen and (max-width: 480px) {
  .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 15px;
  }
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 400;
}
.e-tab.e-background .e-tab-header.e-vertical .e-indicator {
  display: none;
}
.e-tab.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 15px;
}
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-active,
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  padding: 0;
}
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0;
  padding: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item.e-active .e-text-wrap {
  height: 26px;
}
.e-tab.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-right {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
  margin: 0 8px 0 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
  margin: 0 8px 0 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon, .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: 15px;
  right: auto;
}
.e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-left: 20px;
  margin-right: 0;
}
.e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  padding-left: 10px;
  padding-right: 0;
}
@media screen and (max-width: 480px) {
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
    padding-left: 0;
  }
}
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon {
  margin: 0;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  left: 16px;
  right: auto;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  left: 24px;
  right: auto;
}
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 10px;
  padding-right: 0;
}
@media screen and (max-width: 480px) {
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 10px;
    padding-right: 0;
  }
}
.e-tab.e-rtl .e-tab-header .e-hor-nav,
.e-tab.e-rtl .e-tab-header .e-scroll-right-nav {
  padding: 0;
}
.e-tab.e-rtl .e-tab-header .e-scroll-left-nav {
  padding: 0;
}
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  display: none;
}
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  margin: 0;
  width: 100%;
}

.e-tab-clone-element {
  overflow: visible;
  z-index: 10000;
}
.e-tab-clone-element:not(.e-separator) {
  height: 40px;
  margin: 0 2px 0 0;
  min-height: 40px;
  min-width: auto;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}
.e-tab-clone-element .e-tab-wrap {
  height: 40px;
  padding: 0 15px;
  width: 100%;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-tab-wrap {
    height: 50px;
    padding: 0 15px;
  }
}
.e-tab-clone-element .e-text-wrap {
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 40px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-text-wrap {
    height: 50px;
  }
}
.e-tab-clone-element .e-tab-text {
  display: inherit;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
  -ms-flex-item-align: auto;
      align-self: auto;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-tab-text {
    font-size: 15px;
  }
}
.e-tab-clone-element .e-icons.e-close-icon {
  display: none;
}
.e-tab-clone-element .e-close-icon,
.e-tab-clone-element .e-close-icon::before {
  font-size: 12px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-close-icon,
  .e-tab-clone-element .e-close-icon::before {
    font-size: 12px;
  }
}
.e-tab-clone-element .e-close-icon::before {
  top: 1px;
}
.e-tab-clone-element .e-icons.e-tab-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 20px;
  min-width: 20px;
  width: 20px;
  line-height: 1;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-icons.e-tab-icon {
    height: 20px;
    width: 20px;
  }
}
.e-tab-clone-element .e-tab-icon,
.e-tab-clone-element .e-tab-icon::before {
  font-size: 16px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-tab-icon,
  .e-tab-clone-element .e-tab-icon::before {
    font-size: 20px;
  }
}
.e-tab-clone-element.e-icon .e-tab-wrap {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 15px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-icon .e-tab-wrap {
    padding: 0 20px;
  }
}
.e-tab-clone-element .e-icon-right {
  margin: 0 0 0 8px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-icon-right {
    margin: 0;
  }
}
.e-tab-clone-element .e-icon-left + .e-tab-text {
  margin: 0 0 0 8px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element .e-icon-left + .e-tab-text {
    margin: 0 0 0 10px;
  }
}
.e-tab-clone-element.e-itop, .e-tab-clone-element.e-ibottom {
  height: 62px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-itop, .e-tab-clone-element.e-ibottom {
    height: 62px;
  }
}
.e-tab-clone-element.e-itop .e-tab-wrap, .e-tab-clone-element.e-ibottom .e-tab-wrap {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 62px;
  position: relative;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-itop .e-tab-wrap, .e-tab-clone-element.e-ibottom .e-tab-wrap {
    height: 62px;
  }
}
.e-tab-clone-element.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab-clone-element.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-tab-clone-element.e-itop .e-text-wrap, .e-tab-clone-element.e-ibottom .e-text-wrap {
  display: block;
  height: auto;
}
.e-tab-clone-element.e-itop .e-close-icon, .e-tab-clone-element.e-ibottom .e-close-icon {
  position: absolute;
  right: 15px;
  top: calc(50% - 6px);
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-itop .e-close-icon, .e-tab-clone-element.e-ibottom .e-close-icon {
    right: 12px;
  }
}
.e-tab-clone-element.e-itop .e-tab-text {
  margin: 8px 0 0;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-itop .e-tab-text {
    margin: 10px 0 0;
  }
}
.e-tab-clone-element.e-ibottom .e-tab-text {
  margin: 0 0 8px;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-ibottom .e-tab-text {
    margin: 0 0 10px;
  }
}
.e-tab-clone-element .e-tab-icon.e-icon-top,
.e-tab-clone-element .e-tab-icon.e-icon-bottom {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: auto;
}
.e-tab-clone-element.e-close-show .e-icons.e-close-icon {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-item-align: center;
      align-self: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
@media screen and (max-width: 480px) {
  .e-tab-clone-element.e-close-show .e-icons.e-close-icon {
    display: none;
  }
}
.e-tab-clone-element.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab-clone-element.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 20px;
}

.e-tab {
  background: transparent;
  border: none;
}
.e-tab .e-tab-header {
  background: inherit;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-weight: normal;
  border-bottom: 0;
}
.e-tab .e-tab-header::before {
  border-color: #ddd;
  border-style: solid;
}
.e-tab .e-tab-header:not(.e-vertical)::before {
  border-width: 0 0 1px;
}
.e-tab .e-tab-header:not(.e-vertical) .e-toolbar-item.e-active {
  border-bottom: 1px solid #fff;
  margin-bottom: 0;
}
.e-tab .e-tab-header .e-toolbar-items {
  background: inherit;
}
.e-tab .e-tab-header .e-indicator {
  background: #555;
}
.e-tab .e-tab-header .e-toolbar-item {
  background: inherit;
  border: 1px solid transparent;
}
.e-tab .e-tab-header .e-toolbar-item:hover {
  background: inherit;
}
.e-tab .e-tab-header .e-toolbar-item.e-active:hover {
  background: inherit;
}
.e-tab .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(85, 85, 85, 0);
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
  border-radius: 4px;
  color: #317ab9;
  border: 0;
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #317ab9;
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
  background: #eee;
  border: 0;
  border-radius: 4px;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
    background: initial;
  }
}
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #1f496e;
}
.e-tab .e-tab-header .e-toolbar-item.e-active {
  border: 1px solid #ddd;
  border-radius: 4px 4px 0 0;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  border: 0;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #555;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  position: unset;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before {
  border: unset;
  content: unset;
  display: unset;
  position: unset;
  width: unset;
  top: unset;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #555;
}
.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-icon, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-icon {
  color: #555;
}
.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-text, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-text {
  color: #777;
}
.e-tab .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: #777;
}
.e-tab .e-tab-header .e-toolbar-item .e-close-icon {
  color: #777;
}
.e-tab .e-tab-header .e-toolbar-item .e-close-icon:hover {
  color: #333;
}
.e-tab .e-tab-header .e-toolbar-item .e-close-icon:active {
  color: #333;
}
.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay {
  background: inherit;
  opacity: 1;
  pointer-events: none;
}
.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay .e-tab-icon {
  color: #777;
}
.e-tab .e-tab-header.e-reorder-active-item:not(.e-vertical) .e-toolbar-pop .e-toolbar-item.e-active {
  border: 1px solid #fff;
}
.e-tab .e-tab-header .e-toolbar-pop {
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  overflow-y: auto;
}
.e-tab .e-tab-header .e-toolbar-pop[e-animate=true] {
  overflow-y: hidden;
}
@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop {
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  }
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #333;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover {
  background: #f5f5f5;
  border: 0;
  border-radius: 4px;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-icon,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #1f496e;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active {
  background: #f5f5f5;
  border-color: unset;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-icon {
  color: #333;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon {
  color: #555;
}
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-disable.e-overlay .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-disable.e-overlay .e-tab-icon {
  color: #777;
}
.e-tab .e-tab-header .e-scroll-nav,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav {
  background: inherit;
  border: 0;
}
.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow {
  color: #777;
  border: 0;
}
.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:hover {
  background: initial;
  color: #333;
  border: 0;
}
.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:active {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
}
.e-tab .e-tab-header .e-scroll-nav:focus,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: inherit;
}
.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow {
  background: inherit;
  color: #333;
  border: 0;
}
.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:hover {
  background: initial;
  color: #333;
}
.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:active {
  background: inherit;
  border-color: unset;
  color: #333;
}
.e-tab .e-tab-header .e-scroll-nav:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:active {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: inherit;
  border: 0;
}
.e-tab .e-tab-header .e-scroll-nav.e-overlay .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav.e-overlay .e-nav-arrow {
  color: rgba(119, 119, 119, 0.65);
}
.e-tab .e-tab-header .e-scroll-nav.e-overlay .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav.e-overlay .e-nav-arrow:hover {
  color: rgba(119, 119, 119, 0.65);
  background: initial;
  border-color: unset;
}
.e-tab .e-tab-header .e-hor-nav,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) {
  background: transparent;
  border: 0;
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon {
  color: #333;
  border: 0;
}
.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover {
  background: #e6e6e6;
  border: 1px solid #adadad;
  border-radius: 4px;
}
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover {
  color: #333;
}
.e-tab .e-tab-header .e-hor-nav:focus,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus {
  background: transparent;
}
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon {
  background: inherit;
  border: 1px solid #8c8c8c;
  border-radius: 4px;
  color: #333;
}
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:hover {
  background: #e6e6e6;
  border: 1px solid #adadad;
  border-radius: 4px;
}
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:active {
  background: #e6e6e6;
  color: #333;
  border: 1px solid #adadad;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.e-tab .e-tab-header .e-hor-nav:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):hover {
  border: 0;
  background: transparent;
}
.e-tab .e-tab-header .e-hor-nav:hover:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):hover:active {
  background: transparent;
}
.e-tab .e-tab-header .e-hor-nav:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):active {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: transparent;
}
.e-tab .e-tab-header .e-hor-nav.e-nav-active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active {
  background: inherit;
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 0 4px 0 0;
}
.e-tab .e-tab-header .e-hor-nav.e-nav-active .e-icons,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active .e-icons {
  color: #333;
}
.e-tab .e-tab-header .e-hor-nav.e-nav-active:focus,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active:focus {
  border: 0;
}
.e-tab .e-tab-header.e-horizontal-bottom {
  border-color: unset;
  border-style: solid;
  border-width: 0;
}
.e-tab .e-tab-header.e-horizontal-bottom[style*="overflow: hidden"] {
  border-color: unset;
  border-style: solid;
  border-width: 0;
}
.e-tab .e-tab-header.e-horizontal-bottom[style*="overflow: hidden"] .e-toolbar-items {
  border-color: #ddd;
  border-style: solid;
  border-width: 1px 0 0;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item .e-tab-wrap:hover {
  border-radius: 0 0 4px 4px;
}
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  border-color: #fff #ddd #ddd;
  border-style: solid;
  border-width: 1px;
  margin-top: 0;
  border-radius: 0 0 4px 4px;
}
.e-tab .e-tab-header.e-vertical {
  border-bottom: 0;
}
.e-tab .e-tab-header.e-vertical::before {
  border-width: 0 1px 0 0;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-active {
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-item .e-tab-wrap:hover {
  border-radius: 4px 0 0 4px;
}
.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-item.e-active {
  border-radius: 4px 0 0 4px;
  border-right: 1px solid #fff;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-item .e-tab-wrap:hover {
  border-radius: 0 4px 4px 0;
}
.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-item.e-active {
  border-left: 1px solid #fff;
  border-radius: 0 4px 4px 0;
}
.e-tab .e-tab-header.e-vertical .e-toolbar-pop {
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav {
  background: inherit;
  border: 1px solid transparent;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav.e-scroll-up-nav {
  border-bottom-color: #ddd;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav.e-scroll-down-nav {
  border-top-color: #ddd;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  border: 0;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow:hover {
  background: none;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover {
  background: #eee;
  border-color: #ddd transparent transparent;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover:active:focus {
  background: #eee;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow {
  color: #333;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow:hover {
  background: none;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus {
  background: #eee;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus:hover:active .e-nav-arrow {
  color: none;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  background: none;
}
.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow:hover {
  background: none;
  border: 0;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  border: 0;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  border: 0;
  color: #333;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  background: inherit;
  border: 0;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:hover, .e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active, .e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active:hover {
  background: inherit;
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active {
  color: #333;
}
.e-tab .e-tab-header.e-vertical .e-hor-nav:hover .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav:hover .e-popup-down-icon {
  border: 0;
}
.e-tab.e-focused > .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  background: #eee;
  border: 0;
}
.e-tab.e-focused > .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-focused > .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text {
  color: #1f496e;
}
.e-tab.e-focused > .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon {
  color: #1f496e;
}
.e-tab.e-focused > .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #333;
}
.e-tab.e-focused > .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-focused > .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #333;
  background: inherit;
  border: 1px solid #8c8c8c;
}
.e-tab.e-focused > .e-tab-header.e-vertical .e-scroll-nav:focus {
  border-color: #ddd transparent transparent;
}
.e-tab.e-focused > .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  border: 0;
}
.e-tab.e-focused > .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab.e-focused > .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  border: 0;
}
.e-tab.e-focused > .e-tab-header.e-vertical .e-hor-nav:focus {
  outline: 0;
}
.e-tab.e-disable {
  pointer-events: none;
}
.e-tab.e-disable .e-tab-header .e-indicator {
  background: rgba(85, 85, 85, 0.38);
}
.e-tab.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #777;
}
.e-tab.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #777;
}
.e-tab.e-disable .e-content {
  opacity: 0.38;
}
.e-tab.e-fill .e-tab-header {
  border-bottom: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-items.e-hscroll {
  border: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item {
  border: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(85, 85, 85, 0);
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #317ab9;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text, .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #1f496e;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #1f496e;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  margin-top: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active {
  border: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-disable.e-overlay .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-disable.e-overlay .e-tab-icon {
  color: #777;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  background: #317ab9;
  border-radius: 4px;
  border-color: unset;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text, .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  margin-top: 0;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before {
  border: unset;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #fff;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: #f5f5f5;
}
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-close-icon:hover {
  color: #fff;
}
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item:active:hover .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item:active:hover .e-tab-icon {
  color: #333;
}
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-tab-wrap:hover {
  background: #f5f5f5;
}
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-tab-wrap:hover .e-tab-text {
  color: #1f496e;
}
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-tab-wrap:hover .e-tab-icon {
  color: #1f496e;
}
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-tab-wrap:hover .e-close-icon {
  color: #1f496e;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 0;
}
.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  margin-top: 0;
}
.e-tab.e-fill .e-tab-header.e-vertical {
  border-bottom: 0;
}
.e-tab.e-fill .e-tab-header.e-vertical.e-vertical-left {
  border-right: 0;
}
.e-tab.e-fill .e-tab-header.e-vertical.e-vertical-right {
  border-left: 0;
}
.e-tab.e-fill.e-corner .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  border-radius: 3px 3px 0 0;
}
.e-tab.e-fill.e-corner .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap {
  border-radius: 0 0 3px 3px;
}
.e-tab.e-fill.e-disable .e-tab-header {
  border-bottom: 0;
}
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #777;
}
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active {
  opacity: 0.65;
}
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #317ab9;
}
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}
.e-tab.e-fill.e-disable .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 0;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-close-icon {
  color: #1f496e;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #eee;
  border-color: unset;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text {
  color: #1f496e;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon {
  color: #1f496e;
}
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header {
  background: inherit;
  border: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-items {
  background: inherit;
}
.e-tab.e-background .e-tab-header .e-toolbar-items.e-hscroll {
  border: 0;
}
.e-tab.e-background .e-tab-header .e-indicator {
  background: #555;
  bottom: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item {
  border: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(85, 85, 85, 0);
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  background: inherit;
  border-color: unset;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap .e-close-icon {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-text {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap .e-close-icon:hover {
  color: #777;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
  background: #eee;
  background-color: #eee;
  border-color: unset;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text {
  color: #1f496e;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #1f496e;
}
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon:hover, .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon:active {
  color: #1f496e;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active {
  border: 0;
  margin: 0;
  background: inherit;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-disable.e-overlay .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-disable.e-overlay .e-tab-icon {
  color: #777;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  background: #317ab9;
  border-color: unset;
  border-radius: 4px;
  border-style: solid;
  border-width: 0;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-close-icon {
  color: #f5f5f5;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover {
  background: #317ab9;
  border-color: unset;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon:hover {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon:active {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover:focus {
  background: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover:focus .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover:focus .e-tab-text {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-tab-text {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-close-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before {
  border: unset;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: #f5f5f5;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-close-icon:hover,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text:hover {
  color: #fff;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap, .e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap {
  background: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-close-icon {
  color: #777;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item {
  background: inherit;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-close-icon {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover {
  background: #eee;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-text {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-icon {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-close-icon {
  color: #317ab9;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav,
.e-tab.e-background .e-tab-header .e-scroll-nav {
  background: inherit;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow,
.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow {
  color: #777;
  border: 0;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:hover,
.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow:hover {
  background: inherit;
  color: #333;
  border-color: unset;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:active,
.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow:active {
  background: unset;
  color: #777;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow,
.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #333;
  border: 0;
  background: inherit;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:hover,
.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow:hover {
  background: inherit;
  color: #333;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:active,
.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow:active {
  background: inherit;
  border-color: unset;
  color: #333;
}
.e-tab.e-background .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:active::after,
.e-tab.e-background .e-tab-header .e-scroll-nav:active::after {
  -webkit-animation: none;
          animation: none;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav),
.e-tab.e-background .e-tab-header .e-hor-nav {
  background: transparent;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon {
  color: #777;
  border: 0;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  background: #e6e6e6;
  border-color: #adadad;
  color: #333;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon:active:hover,
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:active:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon:active:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon:active:hover {
  background: #e6e6e6;
  color: #333;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #333;
  background: inherit;
  border: 1px solid #8c8c8c;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover {
  background: #e6e6e6;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:active,
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:active,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon:active {
  background: #e6e6e6;
  border-color: #adadad;
  color: #333;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav):active::after,
.e-tab.e-background .e-tab-header .e-hor-nav:active::after {
  -webkit-animation: none;
          animation: none;
}
.e-tab.e-background .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav.e-nav-active .e-popup-up-icon {
  color: #333;
}
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  border: 0;
  margin: 0;
}
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap {
  border-color: unset;
  border-radius: 4px;
  border-style: solid;
  border-width: 0;
}
.e-tab.e-background .e-tab-header.e-vertical.e-vertical-left .e-toolbar-item.e-active {
  border-color: #fff;
  border-style: solid;
  border-width: 0 1px 0 0;
  margin: 0;
}
.e-tab.e-background .e-tab-header.e-vertical.e-vertical-left .e-toolbar-item.e-active .e-tab-wrap {
  border-color: unset;
  border-radius: 4px;
  border-style: solid;
  border-width: 0;
}
.e-tab.e-background .e-tab-header.e-vertical.e-vertical-right .e-toolbar-item.e-active {
  border-color: #fff;
  border-style: solid;
  border-width: 0 0 0 1px;
  margin: 0;
}
.e-tab.e-background .e-tab-header.e-vertical.e-vertical-right .e-toolbar-item.e-active .e-tab-wrap {
  border-color: unset;
  border-radius: 4px;
  border-style: solid;
  border-width: 0;
}
.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  border: 0;
}
.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  border: 0;
}
.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow:hover {
  background: none;
}
.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow {
  color: #333;
}
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  background: inherit;
  border-color: transparent;
}
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:hover {
  background: inherit;
  border-color: transparent;
}
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  border-color: transparent;
}
.e-tab.e-background.e-disable .e-tab-header .e-indicator {
  background: rgba(85, 85, 85, 0.38);
}
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #777;
}
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #777;
}
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon {
  color: #777;
}
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active {
  opacity: 0.65;
}
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #317ab9;
  color: #fff;
}
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}
.e-tab.e-background.e-accent .e-tab-header .e-indicator {
  background: #555;
}
.e-tab.e-background.e-accent .e-tab-header.e-disable .e-indicator {
  background: rgba(85, 85, 85, 0.38);
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  background: #eee;
  border-color: unset;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-close-icon {
  color: #317ab9;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #eee;
  border-color: unset;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover {
  background: #317ab9;
  border: 0;
  border-radius: 4px;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text {
  color: #1f496e;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon {
  color: #1f496e;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text {
  color: #1f496e;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon {
  color: #1f496e;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-tab-text {
  color: #1f496e;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus:hover .e-close-icon {
  color: #1f496e;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap, .e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap {
  background: #317ab9;
}
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap:focus, .e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap:focus {
  background: #eee;
}
.e-tab.e-background.e-focused .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #333;
}
.e-tab.e-background.e-focused .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background.e-focused .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #333;
}
.e-tab.e-rtl .e-tab-header .e-hscroll.e-rtl .e-scroll-nav {
  border: 0;
}
.e-tab .e-content {
  background: inherit;
}
.e-tab .e-content .e-item {
  background: inherit;
  color: #000;
  font-size: 14px;
}

.e-tab-clone-element {
  background: #fff;
  color: #317ab9;
}
.e-tab-clone-element .e-tab-wrap {
  border-radius: 4px;
  color: #317ab9;
  border: 0;
}
.e-tab-clone-element .e-tab-wrap .e-tab-icon {
  color: #317ab9;
}

.e-bigger .e-tab .e-tab-header,
.e-tab.e-bigger .e-tab-header {
  height: 50px;
  min-height: 50px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items,
.e-tab.e-bigger .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: auto;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child, .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child, .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  padding-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items .e-indicator + .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-items .e-indicator + .e-toolbar-item:last-child.e-active {
  margin: 1px 2px 0 0;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 2px 0 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0 2px 0 0;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav {
  width: 50px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 50px;
  margin: 0 2px 0 0;
  min-height: 50px;
  min-width: auto;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator) {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator).e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 72px;
  min-height: 72px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 50px;
  padding: 0 20px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 15px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-text {
  font-size: 15px;
  margin-bottom: 0;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ileft:not(.e-icon) .e-tab-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ileft:not(.e-icon) .e-tab-icon::before {
  position: relative;
  top: 2px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active {
  margin: 0 2px 0 0;
  padding-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft:not(.e-icon) .e-tab-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft:not(.e-icon) .e-tab-icon::before {
  position: relative;
  top: 2px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
  margin-top: 0;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
  cursor: pointer;
  margin: 0 0 0 10px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-close-icon::before {
  font-size: 12px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-close-icon::before {
    font-size: 12px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
  height: 24px;
  min-width: 24px;
  width: 24px;
  line-height: 1;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-icon::before {
  font-size: 20px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
  padding: 0 20px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-right,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-right {
  margin: 0 0 0 12px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-right,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-right {
    margin: 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
  margin: 0 0 0 10px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
    margin: 0 0 0 10px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom {
  height: 72px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 72px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap {
  height: 72px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
  right: 20px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
  margin: 10px 0 0;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0 0 10px;
}
.e-bigger .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 22px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item {
  height: 40px;
  min-height: 40px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 24px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
    padding: 0 24px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
  height: 40px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style="display:block"],
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style="display:block"] {
  padding-right: 12px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style="display:block"],
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style="display:block"] {
    padding-right: 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  margin: 0 0 0 8px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before {
  top: 0;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  right: 24px;
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  min-height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
    min-height: 50px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 50px;
  }
}
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 0;
  padding-right: 12px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-scroll-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav {
  height: 50px;
  min-height: 50px;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-scroll-nav,
  .e-tab.e-bigger .e-tab-header .e-scroll-nav {
    padding: 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-scroll-right-nav {
  padding: 0;
}
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-scroll-left-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-scroll-left-nav {
  padding: 0;
}
.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-arrow {
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  width: 44px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
  .e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-arrow {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}
.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  font-size: 16px;
  line-height: 44px;
  top: 0;
  vertical-align: initial;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  height: 100%;
  width: 100%;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav,
.e-tab.e-bigger .e-tab-header .e-hor-nav {
  height: 50px;
  min-height: 50px;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav,
  .e-tab.e-bigger .e-tab-header .e-hor-nav {
    padding: 0;
  }
}
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon {
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  width: 44px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon {
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    width: 44px;
  }
}
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon::before {
  font-size: 16px;
  line-height: 44px;
  top: 0;
  vertical-align: initial;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 16px;
  }
}
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  line-height: 42px;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
  line-height: 42px;
  top: 0;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  line-height: 42px;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
  line-height: 42px;
  top: 0;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover {
  line-height: 42px;
  top: -2px;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 15px;
}
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 15px;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0 2px 0 0;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0 2px 0 0;
  padding-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active,
  .e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-text {
  padding-top: 1px;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-close-icon::before {
  top: 0.5px;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-close-icon::before, .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-close-icon::before {
  top: 1.5px;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child {
  margin: 0 2px 0 0;
  padding-bottom: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child,
  .e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-tab-text,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-tab-text {
  padding-top: 0.5px;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-close-icon::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child .e-close-icon::before {
  top: 0;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom {
  padding-bottom: 0;
  padding-top: 0;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active .e-text-wrap {
  margin: 0;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
  padding: 0;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap, .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap {
  height: initial;
}
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active {
  height: 50px;
}
.e-bigger .e-tab .e-tab-header.e-vertical,
.e-tab.e-bigger .e-tab-header.e-vertical {
  max-width: 150px;
}
.e-bigger .e-tab .e-tab-header.e-vertical[style*="overflow: hidden"]::before,
.e-tab.e-bigger .e-tab-header.e-vertical[style*="overflow: hidden"]::before {
  bottom: 23px;
  top: 23px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-vertical[style*="overflow: hidden"]::before,
  .e-tab.e-bigger .e-tab-header.e-vertical[style*="overflow: hidden"]::before {
    bottom: 0;
    top: 0;
  }
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device),
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device) {
  padding: 24px 0;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  right: -20px;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon {
  min-height: 38px;
  min-width: auto;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-scroll-nav,
.e-tab.e-bigger .e-tab-header.e-vertical .e-scroll-nav {
  height: 24px;
  min-height: 24px;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow,
.e-tab.e-bigger .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 20px;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  line-height: 48px;
}
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  font-size: 15px;
  line-height: 48px;
  margin: 0 0 0 8px;
}
.e-bigger .e-tab .e-tab-header.e-vertical.e-toolpop .e-toolbar-items,
.e-tab.e-bigger .e-tab-header.e-vertical.e-toolpop .e-toolbar-items {
  height: auto;
}
.e-bigger .e-tab.e-vertical-icon > .e-tab-header,
.e-tab.e-bigger.e-vertical-icon > .e-tab-header {
  height: 72px;
  min-height: 72px;
}
.e-bigger .e-tab.e-vertical-icon > .e-tab-header > .e-toolbar-items,
.e-tab.e-bigger.e-vertical-icon > .e-tab-header > .e-toolbar-items {
  height: 72px;
}
.e-bigger .e-tab.e-vertical-icon > .e-tab-header .e-scroll-nav,
.e-tab.e-bigger.e-vertical-icon > .e-tab-header .e-scroll-nav {
  height: 72px;
}
.e-bigger .e-tab.e-vertical-icon > .e-tab-header .e-hor-nav,
.e-tab.e-bigger.e-vertical-icon > .e-tab-header .e-hor-nav {
  height: 72px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-vertical-icon > .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-bigger .e-tab.e-vertical-icon > .e-tab-header .e-hor-nav .e-popup-down-icon::before,
  .e-tab.e-bigger.e-vertical-icon > .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-bigger.e-vertical-icon > .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 12px;
  }
}
.e-bigger .e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items,
.e-tab.e-bigger.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}
.e-bigger .e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-scroll-nav,
.e-tab.e-bigger.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-scroll-nav {
  height: 24px;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 50px;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 72px;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
}
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 50px;
}
.e-bigger .e-tab.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
  padding: 0;
}
.e-bigger .e-tab.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ileft,
.e-tab.e-bigger.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ileft {
  height: 50px;
}
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 15px;
}
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 15px;
}
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 15px;
}
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 13px;
}
.e-bigger .e-tab.e-safari .e-tab-header .e-close-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-close-icon::before {
  top: 0;
}
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before {
  top: 0;
}
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  top: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator).e-active,
.e-tab.e-bigger.e-rtl .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:not(.e-separator).e-active {
  margin: 0 0 0 2px;
}
.e-bigger .e-tab.e-fill .e-tab-header,
.e-tab.e-bigger.e-fill .e-tab-header {
  height: 50px;
  min-height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header,
  .e-tab.e-bigger.e-fill .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 50px;
  margin: 0 2px 0 0;
  min-height: 50px;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator),
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 72px;
  min-height: 72px;
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 50px;
  padding: 0 20px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 20px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 50px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 50px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 72px;
}
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  height: auto;
}
.e-bigger .e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 50px;
}
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 72px;
}
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
    height: auto;
  }
}
.e-bigger .e-tab.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}
.e-bigger .e-tab.e-fill.e-vertical-icon > .e-tab-header,
.e-tab.e-bigger.e-fill.e-vertical-icon > .e-tab-header {
  height: 70px;
  min-height: 70px;
}
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item:not(.e-separator) {
  padding: 0;
}
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 20px;
  }
}
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}
.e-bigger .e-tab.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 20px;
}
.e-bigger .e-tab.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 2px;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-right,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-right {
  margin: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
  margin: 0 10px 0 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
    margin: 0 10px 0 0;
  }
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
  margin: 0 10px 0 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
    margin: 0 8px 0 0;
  }
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: 20px;
  right: auto;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-left: 22px;
  margin-right: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item {
  margin: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  padding-left: 12px;
  padding-right: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
    padding-left: 0;
  }
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon {
  margin: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  left: 24px;
  right: auto;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 12px;
  padding-right: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-hor-nav,
.e-bigger .e-tab.e-rtl .e-tab-header .e-scroll-right-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-hor-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-scroll-right-nav {
  padding: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header .e-scroll-left-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-scroll-left-nav {
  padding: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  left: -20px;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 20px;
}
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-popup-up-icon::after,
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-popup-down-icon::after,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-popup-up-icon::after,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-popup-down-icon::after {
  margin: 0 8px 0 0;
}

.e-bigger .e-tab-clone-element:not(.e-separator) {
  height: 50px;
  margin: 0 2px 0 0;
  min-height: 50px;
  min-width: auto;
  padding: 0;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab-clone-element:not(.e-separator) {
    margin: 0 2px 0 0;
  }
}
.e-bigger .e-tab-clone-element:not(.e-separator).e-itop, .e-bigger .e-tab-clone-element:not(.e-separator).e-ibottom {
  height: 72px;
  min-height: 72px;
}
.e-bigger .e-tab-clone-element .e-tab-wrap {
  height: 50px;
  padding: 0 20px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab-clone-element .e-tab-wrap {
    padding: 0 15px;
  }
}
.e-bigger .e-tab-clone-element .e-text-wrap {
  height: 50px;
}
.e-bigger .e-tab-clone-element .e-tab-text {
  font-size: 15px;
  -ms-flex-item-align: auto;
      align-self: auto;
}
.e-bigger .e-tab-clone-element .e-icons.e-close-icon {
  cursor: pointer;
  margin: 0 0 0 10px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab-clone-element .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}
.e-bigger .e-tab-clone-element .e-icons.e-close-icon,
.e-bigger .e-tab-clone-element .e-close-icon::before {
  font-size: 12px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab-clone-element .e-icons.e-close-icon,
  .e-bigger .e-tab-clone-element .e-close-icon::before {
    font-size: 12px;
  }
}
.e-bigger .e-tab-clone-element .e-icons.e-tab-icon {
  height: 24px;
  min-width: 24px;
  width: 24px;
  line-height: 1;
}
.e-bigger .e-tab-clone-element .e-tab-icon,
.e-bigger .e-tab-clone-element .e-tab-icon::before {
  font-size: 20px;
}
.e-bigger .e-tab-clone-element.e-icon .e-tab-wrap {
  padding: 0 20px;
}
.e-bigger .e-tab-clone-element .e-icon-right {
  margin: 0 0 0 12px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab-clone-element .e-icon-right {
    margin: 0;
  }
}
.e-bigger .e-tab-clone-element .e-icon-left + .e-tab-text {
  margin: 0 0 0 10px;
}
@media screen and (max-width: 480px) {
  .e-bigger .e-tab-clone-element .e-icon-left + .e-tab-text {
    margin: 0 0 0 10px;
  }
}
.e-bigger .e-tab-clone-element.e-itop, .e-bigger .e-tab-clone-element.e-ibottom {
  height: 72px;
}
.e-bigger .e-tab-clone-element.e-itop .e-tab-wrap, .e-bigger .e-tab-clone-element.e-ibottom .e-tab-wrap {
  height: 72px;
}
.e-bigger .e-tab-clone-element.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab-clone-element.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab-clone-element.e-itop.e-active .e-tab-wrap, .e-bigger .e-tab-clone-element.e-ibottom.e-active .e-tab-wrap {
  height: 72px;
}
.e-bigger .e-tab-clone-element.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab-clone-element.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}
.e-bigger .e-tab-clone-element.e-itop .e-close-icon, .e-bigger .e-tab-clone-element.e-ibottom .e-close-icon {
  right: 20px;
}
.e-bigger .e-tab-clone-element.e-itop .e-tab-text {
  margin: 10px 0 0;
}
.e-bigger .e-tab-clone-element.e-ibottom .e-tab-text {
  margin: 0 0 10px;
}