@import 'ej2-base/styles/definition/tailwind.scss';
@import 'pager/tailwind-definition.scss';
@import 'pager/icons/tailwind.scss';
@import 'pager/all.scss';
@import 'pager/bigger.scss';
@import 'h-scroll/tailwind-definition.scss';
@import 'h-scroll/icons/tailwind.scss';
@import 'h-scroll/all.scss';
@import 'h-scroll/bigger.scss';
@import 'v-scroll/tailwind-definition.scss';
@import 'v-scroll/icons/tailwind.scss';
@import 'v-scroll/all.scss';
@import 'v-scroll/bigger.scss';
@import 'ej2-buttons/styles/button/tailwind-definition.scss';
@import 'ej2-popups/styles/popup/tailwind-definition.scss';
@import 'toolbar/tailwind-definition.scss';
@import 'toolbar/icons/tailwind.scss';
@import 'toolbar/all.scss';
@import 'toolbar/bigger.scss';
@import 'accordion/tailwind-definition.scss';
@import 'accordion/icons/tailwind.scss';
@import 'accordion/all.scss';
@import 'accordion/bigger.scss';
@import 'carousel/tailwind-definition.scss';
@import 'carousel/icons/tailwind.scss';
@import 'carousel/all.scss';

@import 'context-menu/tailwind-definition.scss';
@import 'context-menu/icons/tailwind.scss';
@import 'context-menu/all.scss';
@import 'context-menu/bigger.scss';
@import 'tab/tailwind-definition.scss';
@import 'tab/icons/tailwind.scss';
@import 'tab/all.scss';
@import 'tab/bigger.scss';
@import 'ej2-inputs/styles/input/tailwind-definition.scss';
@import 'ej2-buttons/styles/check-box/tailwind-definition.scss';
@import 'treeview/tailwind-definition.scss';
@import 'treeview/icons/tailwind.scss';
@import 'treeview/all.scss';
@import 'treeview/bigger.scss';
@import 'sidebar/tailwind-definition.scss';
@import 'sidebar/all.scss';
@import 'menu/tailwind-definition.scss';
@import 'menu/icons/tailwind.scss';
@import 'menu/all.scss';
@import 'menu/bigger.scss';
@import 'breadcrumb/tailwind-definition.scss';
@import 'breadcrumb/icons/tailwind.scss';
@import 'breadcrumb/all.scss';
@import 'breadcrumb/bigger.scss';
@import 'appbar/tailwind-definition.scss';
@import 'appbar/all.scss';
@import 'appbar/bigger.scss';
@import 'ej2-popups/styles/tooltip/tailwind-definition.scss';
@import 'stepper/tailwind-definition.scss';
@import 'stepper/icons/tailwind.scss';
@import 'stepper/all.scss';
@import 'stepper/bigger.scss';