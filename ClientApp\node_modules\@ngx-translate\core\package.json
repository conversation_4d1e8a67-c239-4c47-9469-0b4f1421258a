{"name": "@ngx-translate/core", "version": "16.0.4", "description": "Translation library (i18n) for Angular", "keywords": ["@ngx-translate", "ngx-translate", "angular", "i18n", "translation"], "author": "<PERSON> / CodeAndWeb GmbH, Olivier <PERSON>", "license": "MIT", "peerDependencies": {"@angular/common": ">=16", "@angular/core": ">=16"}, "sideEffects": false, "module": "fesm2022/ngx-translate-core.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/ngx-translate-core.mjs"}}, "dependencies": {"tslib": "^2.3.0"}}