/*! component's theme wise override definitions and variables */
@-webkit-keyframes hscroll-popup-shadow {
  0% {
    border-color: rgba(255, 255, 255, 0.5);
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
  }
  100% {
    -webkit-box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
  }
}
@keyframes hscroll-popup-shadow {
  0% {
    border-color: rgba(255, 255, 255, 0.5);
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
  }
  100% {
    -webkit-box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
  }
}
/*! component's theme wise override definitions and variables */
/* stylelint-disable-line no-empty-source */
/* stylelint-disable */
.e-toolbar .e-popup-down-icon::before {
  content: "\e729";
  line-height: normal;
}
.e-toolbar .e-popup-up-icon::before {
  content: "\e776";
  line-height: normal;
}

.e-toolbar {
  border-radius: 0;
  display: block;
  height: 38px;
  min-height: 38px;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  overflow: hidden;
}
.e-toolbar.e-spacer-toolbar .e-toolbar-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  width: 100%;
}
.e-toolbar.e-spacer-toolbar .e-toolbar-items:not(.e-toolbar-multirow) {
  position: absolute;
}
.e-toolbar.e-spacer-toolbar.e-pop-mode .e-toolbar-items {
  width: calc(100% - 28px);
}
.e-toolbar .e-blazor-toolbar-items {
  position: absolute;
  top: -9999px;
  visibility: hidden;
}
.e-toolbar.e-control[class*=e-toolbar] {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
}
.e-toolbar.e-corner {
  border-radius: 4px;
}
.e-toolbar.e-hidden {
  display: none;
}
.e-toolbar .e-toolbar-items {
  border-radius: 0 0 0 0;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 100%;
  vertical-align: middle;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.e-toolbar .e-toolbar-items.e-toolbar-multirow {
  margin-bottom: 1px;
  margin-left: 10px;
  margin-right: 10px;
  white-space: normal;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.e-toolbar .e-toolbar-items.e-toolbar-multirow .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-toolbar .e-toolbar-items.e-toolbar-multirow .e-toolbar-item.e-separator.e-multirow-separator, .e-toolbar .e-toolbar-items.e-toolbar-multirow .e-toolbar-item.e-separator.e-hidden {
  display: none;
}
.e-toolbar .e-toolbar-items.e-multirow-pos .e-toolbar-left,
.e-toolbar .e-toolbar-items.e-multirow-pos .e-toolbar-center,
.e-toolbar .e-toolbar-items.e-multirow-pos .e-toolbar-right {
  display: inline;
}
.e-toolbar .e-toolbar-items.e-tbar-pos {
  display: block;
}
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-left,
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-center,
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-right {
  display: table;
  height: 100%;
  top: 0;
}
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-right,
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-left {
  position: absolute;
}
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-right {
  right: 0;
}
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-left {
  left: 0;
}
.e-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-center {
  margin: 0 auto;
}
.e-toolbar .e-toolbar-items .e-toolbar-left,
.e-toolbar .e-toolbar-items .e-toolbar-center,
.e-toolbar .e-toolbar-items .e-toolbar-right {
  display: inline-block;
}
.e-toolbar .e-toolbar-items .e-toolbar-left .e-toolbar-item:first-child, .e-toolbar .e-toolbar-items:not(.e-tbar-pos):not(.e-toolbar-multirow) .e-toolbar-item:first-child {
  margin-left: 8px;
}
.e-toolbar .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-item:last-child,
.e-toolbar .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-right .e-toolbar-item:last-child {
  margin-right: 8px;
}
.e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content {
  -ms-touch-action: pan-y pinch-zoom;
      touch-action: pan-y pinch-zoom;
}
.e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content > .e-toolbar-item:last-child {
  margin-right: 8px;
}
.e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-center .e-toolbar-item,
.e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item {
  margin: 0;
}
.e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item:last-child {
  margin-right: 8px;
}
.e-toolbar .e-toolbar-item {
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  min-height: 38px;
  vertical-align: middle;
  width: auto;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.e-toolbar .e-toolbar-item.e-spacer {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.e-toolbar .e-toolbar-item .e-tbar-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  vertical-align: middle;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0;
  min-height: 32px;
  min-width: 32px;
  padding: 0 4px;
  border-radius: 4px;
  line-height: 22px;
  border: none;
  cursor: pointer;
  font-size: 18px;
  font-weight: 400;
  overflow: hidden;
  text-align: center;
  text-decoration: none;
  text-transform: none;
}
.e-toolbar .e-toolbar-item .e-tbar-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 4px;
}
.e-toolbar .e-toolbar-item .e-tbar-btn.e-tbtn-txt .e-icons.e-btn-icon.e-icon-right {
  padding: 4px;
}
.e-toolbar .e-toolbar-item .e-tbar-btn .e-icons.e-btn-icon {
  margin: 0;
  min-width: 12px;
  width: auto;
  font-size: 18px;
  line-height: 14px;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:hover, .e-toolbar .e-toolbar-item .e-tbar-btn:focus, .e-toolbar .e-toolbar-item .e-tbar-btn:active {
  padding: 0 4px;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:focus {
  outline: 0;
}
.e-toolbar .e-toolbar-item .e-tbar-btn div {
  vertical-align: middle;
}
.e-toolbar .e-toolbar-item .e-tbar-btn .e-tbar-btn-text {
  font-size: 14px;
  padding: 4px;
}
.e-toolbar .e-toolbar-item:not(.e-separator):not(.e-spacer) {
  height: inherit;
  min-width: 28px;
  padding: 4px;
}
.e-toolbar .e-toolbar-item.e-separator {
  margin: 5px 10px;
  min-height: 24px;
  min-width: 1px;
  height: 24px;
}
.e-toolbar .e-toolbar-item.e-separator + .e-separator, .e-toolbar .e-toolbar-item.e-separator:last-of-type, .e-toolbar .e-toolbar-item.e-separator:first-of-type {
  display: none;
}
.e-toolbar .e-toolbar-item.e-popup-text .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 4px 4px 5px 4px;
}
.e-toolbar .e-toolbar-item > * {
  text-overflow: ellipsis;
}
.e-toolbar .e-toolbar-item.e-hidden {
  display: none;
}
.e-toolbar .e-toolbar-item input[type=checkbox] {
  height: auto;
}
.e-toolbar.e-vertical {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.e-toolbar.e-vertical .e-toolbar-items .e-vscroll-bar .e-vscroll-content {
  -ms-touch-action: pan-x pinch-zoom;
      touch-action: pan-x pinch-zoom;
}
.e-toolbar.e-vertical .e-toolbar-items.e-tbar-pos .e-toolbar-left,
.e-toolbar.e-vertical .e-toolbar-items.e-tbar-pos .e-toolbar-center,
.e-toolbar.e-vertical .e-toolbar-items.e-tbar-pos .e-toolbar-right {
  height: auto;
}
.e-toolbar.e-vertical .e-toolbar-items.e-tbar-pos .e-toolbar-left {
  left: auto;
  right: auto;
  top: 0;
}
.e-toolbar.e-vertical .e-toolbar-items.e-tbar-pos .e-toolbar-right {
  bottom: 0;
  left: auto;
  right: auto;
}
.e-toolbar.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child {
  margin-left: 0;
}
.e-toolbar.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin-right: 0;
}
.e-toolbar.e-vertical .e-toolbar-items .e-toolbar-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: auto;
}
.e-toolbar.e-vertical .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  min-width: 33px;
}
.e-toolbar.e-vertical .e-toolbar-items .e-toolbar-item.e-separator {
  height: auto;
  margin: 3px 7px;
  min-height: auto;
}
.e-toolbar.e-vertical .e-hor-nav {
  bottom: 0;
  height: auto;
  left: 0;
  min-height: 40px;
  min-width: 50px;
  right: auto;
  top: auto;
  width: auto;
}
.e-toolbar.e-vertical.e-rtl.e-tbar-pos .e-toolbar-left {
  bottom: 0;
  top: auto;
}
.e-toolbar.e-vertical.e-rtl.e-tbar-pos .e-toolbar-right {
  bottom: auto;
  top: 0;
}
.e-toolbar .e-hor-nav {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 0 0 0 0;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  min-height: 38px;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  width: 28px;
}
.e-toolbar .e-hor-nav.e-ie-align {
  display: table;
}
.e-toolbar .e-popup-down-icon.e-icons,
.e-toolbar .e-popup-up-icon.e-icons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  text-align: center;
  vertical-align: middle;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  font-size: 18px;
  color: #adb5bd;
}
.e-toolbar.e-toolpop {
  overflow: visible;
}
.e-toolbar.e-toolpop .e-toolbar-items .e-toolbar-item.e-popup-text .e-tbar-btn-text {
  display: none;
}
.e-toolbar .e-toolbar-pop {
  border-radius: 4px;
  overflow: hidden;
  padding: 0;
  position: absolute;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 38px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 38px;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item.e-toolbar-popup.e-hidden {
  display: none;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn {
  min-height: 38px;
  min-width: 100%;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-icons.e-btn-icon {
  margin: 0;
  padding: 0 4px 0 0;
  width: auto;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 0 0 0 4px;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  height: 38px;
  min-width: 34px;
  padding: 0;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item > * {
  height: 100%;
  min-width: 100%;
  text-overflow: ellipsis;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item.e-tbtn-align .e-btn.e-control .e-icons.e-btn-icon {
  min-width: 100%;
}
.e-toolbar .e-toolbar-pop .e-toolbar-text .e-tbar-btn-text {
  display: none;
}
.e-toolbar .e-toolbar-pop .e-toolbar-popup,
.e-toolbar .e-toolbar-pop .e-toolpopup {
  text-align: center;
}
.e-toolbar.e-extended-toolbar {
  overflow: visible;
}
.e-toolbar.e-extended-toolbar.e-tbar-extended {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended {
  border-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  min-height: 38px;
  padding-bottom: 0;
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 0;
  margin-left: -1px;
  -webkit-box-shadow: none;
          box-shadow: none;
  display: inline;
  white-space: normal;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  min-height: 38px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn,
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control {
  min-height: 32px;
  min-width: 32px;
  padding: 0 4px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-tbar-btn-text {
  padding: 4px;
  font-size: 14px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn .e-icons.e-btn-icon:not(.e-toolbar-pop),
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-icons.e-btn-icon:not(.e-toolbar-pop) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  vertical-align: middle;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 5px 6px;
  font-size: 16px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon:not(.e-toolbar-pop),
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-btn-icon:not(.e-toolbar-pop) {
  padding: 4px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-icon-right.e-btn-icon:not(.e-toolbar-pop),
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-icon-right.e-btn-icon:not(.e-toolbar-pop) {
  padding: 4px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:hover, .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:focus, .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:active,
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:hover,
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:focus,
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:active {
  padding: 0 4px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item:not(.e-separator) {
  min-width: 28px;
  padding: 4px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-separator {
  min-height: 16px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-separator.e-extended-separator {
  display: none;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-toolbar-text .e-tbar-btn-text {
  display: none;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-popup-close {
  display: none;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-popup-open {
  display: inline;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-toolbar-pop {
  width: inherit;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-toolbar-pop .e-toolbar-item .e-tbar-btn {
  cursor: pointer;
  font-size: 18px;
  overflow: hidden;
  padding: 0 4px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-toolbar-pop .e-toolbar-item .e-tbar-btn .e-icons.e-btn-icon {
  font-size: 14px;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  height: auto;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended.e-toolbar-pop .e-toolbar-item > * {
  -ms-flex-item-align: center;
      align-self: center;
  text-overflow: ellipsis;
}
.e-toolbar.e-extended-toolbar .e-toolbar-items .e-toolbar-item.e-popup-text .e-tbar-btn-text {
  display: none;
}
.e-toolbar.e-extended-toolbar .e-toolbar-items .e-toolbar-item.e-separator:last-of-type {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.e-toolbar.e-extended-toolbar .e-hor-nav.e-ie-align {
  display: table;
}
.e-toolbar.e-rtl:not(.e-spacer-toolbar) .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child {
  margin-right: 8px;
}
.e-toolbar.e-rtl .e-toolbar-item .e-tbar-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 4px;
}
.e-toolbar.e-rtl .e-toolbar-item .e-tbar-btn.e-tbtn-txt .e-icons.e-btn-icon.e-icon-right {
  padding: 4px;
}
.e-toolbar.e-rtl .e-toolbar-item .e-tbar-btn .e-tbar-btn-text {
  padding: 4px;
}
.e-toolbar.e-rtl .e-hscroll-bar .e-hscroll-content > .e-toolbar-item:last-child {
  margin-left: 8px;
  margin-right: initial;
}
.e-toolbar.e-rtl .e-hscroll-bar .e-hscroll-content .e-toolbar-center .e-toolbar-item,
.e-toolbar.e-rtl .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item {
  margin: 0;
}
.e-toolbar.e-rtl .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item:last-child {
  margin-right: 8px;
}
.e-toolbar.e-rtl .e-toolbar-items.e-tbar-pos .e-toolbar-left {
  left: auto;
  right: 0;
}
.e-toolbar.e-rtl .e-toolbar-items.e-tbar-pos .e-toolbar-right {
  left: 0;
  right: auto;
}
.e-toolbar.e-rtl .e-toolbar-items .e-toolbar-left .e-toolbar-item:first-child {
  margin-left: 0;
}
.e-toolbar.e-rtl .e-toolbar-items .e-toolbar-left .e-toolbar-item:last-child {
  margin-left: 8px;
}
.e-toolbar.e-rtl .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child {
  margin-left: 0;
}
.e-toolbar.e-rtl .e-toolbar-items:first-child > .e-toolbar-item:last-child:last-child,
.e-toolbar.e-rtl .e-toolbar-items:first-child > .e-toolbar-right .e-toolbar-item:last-child {
  margin-right: 0;
}
.e-toolbar.e-rtl .e-toolbar-items:first-child > .e-toolbar-item:last-child:first-child,
.e-toolbar.e-rtl .e-toolbar-items:first-child > .e-toolbar-right .e-toolbar-item:first-child {
  margin-right: 8px;
}
.e-toolbar.e-rtl .e-hor-nav {
  left: 0;
  right: auto;
  border-radius: 0 0 0 0;
}
.e-toolbar.e-toolpop.e-rtl .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-center .e-toolbar-item,
.e-toolbar.e-toolpop.e-rtl .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-right .e-toolbar-item {
  margin: 0;
}
.e-toolbar.e-toolpop.e-rtl .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-right .e-toolbar-item:last-child {
  margin: 0;
  margin-left: 8px;
}
.e-toolbar.e-extended-toolbar.e-rtl .e-hor-nav {
  left: 0;
  right: auto;
}
.e-toolbar.e-extended-toolbar.e-rtl .e-toolbar-extended {
  padding-right: 8px;
  margin-left: 0;
}
.e-toolbar.e-extended-toolbar.e-rtl .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn .e-icon-left {
  padding-left: 0;
}

/* stylelint-disable property-no-vendor-prefix */
.e-toolbar {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  background: rgb(26.177027027, 29.35, 32.522972973);
  border: 0 solid rgb(68.0034482759, 75.85, 83.6965517241);
  -webkit-box-shadow: none;
          box-shadow: none;
}
.e-toolbar .e-toolbar-items {
  background: rgb(26.177027027, 29.35, 32.522972973);
}
.e-toolbar .e-toolbar-item .e-tbar-btn {
  background: rgb(26.177027027, 29.35, 32.522972973);
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
  border: none;
}
.e-toolbar .e-toolbar-item .e-tbar-btn .e-icons {
  color: #adb5bd;
}
.e-toolbar .e-toolbar-item .e-tbar-btn .e-tbar-btn-text {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn.e-flat.e-active {
  background: rgb(86.4, 93.6, 100);
  -webkit-box-shadow: none;
          box-shadow: none;
}
.e-toolbar .e-toolbar-item .e-tbar-btn.e-flat.e-active .e-tbar-btn-text {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn.e-flat.e-active:focus {
  -webkit-box-shadow: 0 0 0 4px rgba(130.05, 137.7, 144.5, 0.5);
          box-shadow: 0 0 0 4px rgba(130.05, 137.7, 144.5, 0.5);
}
.e-toolbar .e-toolbar-item .e-tbar-btn:focus {
  background: rgb(91.8, 99.45, 106.25);
  border-radius: 4px;
  color: #adb5bd;
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
  -webkit-box-shadow: 0 0 0 4px rgba(130.05, 137.7, 144.5, 0.5);
          box-shadow: 0 0 0 4px rgba(130.05, 137.7, 144.5, 0.5);
}
.e-toolbar .e-toolbar-item .e-tbar-btn:focus .e-icons {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:focus .e-tbar-btn-text {
  color: #fff;
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:hover {
  background: rgb(91.8, 99.45, 106.25);
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
  border-radius: 4px;
  color: #adb5bd;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:hover .e-icons {
  color: #fff;
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:hover .e-tbar-btn-text {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:hover:active .e-icons {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:hover:active .e-tbar-btn-text {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:active {
  background: rgb(86.4, 93.6, 100);
  border-color: rgb(68.0034482759, 75.85, 83.6965517241);
  border-style: solid;
  border-width: 0;
  border-radius: 4px;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #ced4da;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:active .e-icons {
  color: #fff;
}
.e-toolbar .e-toolbar-item .e-tbar-btn:active .e-tbar-btn-text {
  color: #fff;
}
.e-toolbar .e-toolbar-item.e-separator {
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 1px 0 0;
}
.e-toolbar .e-toolbar-item.e-overlay {
  background: rgb(26.177027027, 29.35, 32.522972973);
  opacity: 0.5;
  color: #ced4da;
}
.e-toolbar .e-toolbar-item.e-overlay .e-tbar-btn-text {
  color: #ced4da;
}
.e-toolbar .e-toolbar-item.e-overlay .e-icons {
  color: #ced4da;
}
.e-toolbar.e-vertical .e-hor-nav {
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 0 0 0;
}
.e-toolbar.e-vertical.e-rtl .e-hor-nav {
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 0 0 0;
}
.e-toolbar.e-vertical .e-toolbar-items .e-toolbar-item.e-separator {
  border-width: 0 0 1px 0;
}
.e-toolbar .e-hor-nav {
  background: rgb(26.177027027, 29.35, 32.522972973);
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 0 0 0;
}
.e-toolbar .e-hor-nav::after {
  content: "";
}
.e-toolbar .e-hor-nav.e-nav-active:active, .e-toolbar .e-hor-nav.e-nav-active:focus, .e-toolbar .e-hor-nav.e-nav-active:hover {
  border-bottom-right-radius: 0;
}
.e-toolbar .e-hor-nav:active {
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #ced4da;
  background: rgb(26.177027027, 29.35, 32.522972973);
}
.e-toolbar .e-hor-nav:active .e-icons {
  color: #adb5bd;
}
.e-toolbar .e-hor-nav:active::after {
  content: "";
}
.e-toolbar .e-hor-nav:hover {
  background: rgb(91.8, 99.45, 106.25);
  border-left: 0;
  color: #fff;
}
.e-toolbar .e-hor-nav:hover .e-icons {
  color: inherit;
}
.e-toolbar .e-hor-nav:hover:not(.e-nav-active) {
  border-color: rgb(68.0034482759, 75.85, 83.6965517241);
  border-style: solid;
  border-width: 0;
}
.e-toolbar .e-hor-nav:hover:active {
  background: rgb(91.8, 99.45, 106.25);
  border-color: rgb(68.0034482759, 75.85, 83.6965517241);
  border-style: solid;
  border-width: 0;
}
.e-toolbar .e-hor-nav:focus {
  background: rgb(91.8, 99.45, 106.25);
  color: #fff;
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
}
.e-toolbar .e-hor-nav:focus .e-icons {
  color: inherit;
}
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active,
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active:not(.e-expended-nav) {
  background: rgb(91.8, 99.45, 106.25);
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: rgb(91.8, 99.45, 106.25);
  border-style: solid;
  border-width: 0;
}
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active:focus,
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active:not(.e-expended-nav):focus {
  border-color: rgb(91.8, 99.45, 106.25);
  border-style: solid;
  border-width: 0;
}
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active .e-icons,
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active:not(.e-expended-nav) .e-icons {
  color: #fff;
}
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active .e-icons:active,
.e-toolbar.e-toolpop .e-hor-nav.e-nav-active:not(.e-expended-nav) .e-icons:active {
  color: #fff;
}
.e-toolbar .e-toolbar-pop {
  background: #343a40;
  border: 0 solid rgb(68.0034482759, 75.85, 83.6965517241);
  -webkit-box-shadow: 0 16px 48px rgba(0, 0, 0, 0.175);
          box-shadow: 0 16px 48px rgba(0, 0, 0, 0.175);
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn {
  background: transparent;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn:hover {
  background: rgb(91.8, 99.45, 106.25);
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn:active {
  background: rgb(86.4, 93.6, 100);
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: rgb(68.0034482759, 75.85, 83.6965517241);
  border-style: solid;
  border-width: 0;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn:focus {
  background: rgb(91.8, 99.45, 106.25);
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item .e-btn:focus {
  outline: 0;
}
.e-toolbar .e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  background: transparent;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn {
  background: rgb(26.177027027, 29.35, 32.522972973);
  -webkit-box-shadow: none;
          box-shadow: none;
  border: none;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn:focus {
  background: rgb(91.8, 99.45, 106.25);
  border-radius: 4px;
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
  -webkit-box-shadow: 0 0 0 4px rgba(130.05, 137.7, 144.5, 0.5);
          box-shadow: 0 0 0 4px rgba(130.05, 137.7, 144.5, 0.5);
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn:hover {
  background: rgb(91.8, 99.45, 106.25);
  border-radius: 4px;
  border-color: #adb5bd;
  border-style: solid;
  border-width: 0;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn:active {
  background: rgb(86.4, 93.6, 100);
  border-radius: 4px;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: rgb(68.0034482759, 75.85, 83.6965517241);
  border-style: solid;
  border-width: 0;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-separator {
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 1px 0 0;
}
.e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-overlay {
  background: rgb(26.177027027, 29.35, 32.522972973);
}
.e-toolbar.e-extended-toolbar .e-toolbar-pop {
  background: rgb(26.177027027, 29.35, 32.522972973);
}
.e-toolbar.e-rtl .e-hor-nav {
  background: rgb(26.177027027, 29.35, 32.522972973);
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 0 0 0;
}
.e-toolbar.e-rtl .e-hor-nav:not(.e-nav-active):hover {
  background: rgb(91.8, 99.45, 106.25);
  color: #adb5bd;
  border: solid rgb(68.0034482759, 75.85, 83.6965517241);
  border-width: 0 0 0 0;
}

.e-bigger .e-toolbar,
.e-toolbar.e-bigger {
  height: 46px;
  min-height: 46px;
}
.e-bigger .e-toolbar .e-toolbar-items:not(.e-tbar-pos):not(.e-toolbar-multirow) .e-toolbar-item:first-child,
.e-bigger .e-toolbar .e-toolbar-items .e-toolbar-left .e-toolbar-item:first-child,
.e-toolbar.e-bigger .e-toolbar-items:not(.e-tbar-pos):not(.e-toolbar-multirow) .e-toolbar-item:first-child,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-left .e-toolbar-item:first-child {
  margin-left: 12px;
}
.e-bigger .e-toolbar .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-item:last-child,
.e-bigger .e-toolbar .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-right .e-toolbar-item:last-child,
.e-toolbar.e-bigger .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-item:last-child,
.e-toolbar.e-bigger .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-right .e-toolbar-item:last-child {
  margin-right: 12px;
}
.e-bigger .e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content > .e-toolbar-item:last-child,
.e-toolbar.e-bigger .e-toolbar-items .e-hscroll-bar .e-hscroll-content > .e-toolbar-item:last-child {
  margin-right: 12px;
}
.e-bigger .e-toolbar .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item:last-child,
.e-toolbar.e-bigger .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item:last-child {
  margin-right: 12px;
}
.e-bigger .e-toolbar .e-toolbar-items.e-toolbar-multirow,
.e-toolbar.e-bigger .e-toolbar-items.e-toolbar-multirow {
  margin-left: 12.5px;
  margin-right: 12.5px;
}
.e-bigger .e-toolbar .e-toolbar-items.e-toolbar-multirow .e-toolbar-item:not(.e-separator),
.e-toolbar.e-bigger .e-toolbar-items.e-toolbar-multirow .e-toolbar-item:not(.e-separator) {
  margin: 0;
}
.e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus, .e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover, .e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active,
.e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:focus,
.e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:hover,
.e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:active,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:focus,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:hover,
.e-toolbar.e-bigger .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:active {
  padding: 0 8px;
}
.e-bigger .e-toolbar .e-toolbar-item,
.e-toolbar.e-bigger .e-toolbar-item {
  min-height: 46px;
}
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn,
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-control,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-control {
  min-height: 38px;
  min-width: 38px;
  padding: 0 8px;
  line-height: 24px;
}
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-tbar-btn-text,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-tbar-btn-text {
  padding: 4px;
  font-size: 16px;
}
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn .e-icons,
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-icons,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn .e-icons,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-icons {
  font-size: 20px;
}
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn .e-icons.e-btn-icon,
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-icons.e-btn-icon {
  font-size: 18px;
  line-height: 14px;
}
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 4px 6px;
}
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-icon-right.e-btn-icon,
.e-bigger .e-toolbar .e-toolbar-item .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-icon-right.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-icon-right.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-icon-right.e-btn-icon {
  padding: 4px 6px;
}
.e-bigger .e-toolbar .e-toolbar-item:not(.e-separator):not(.e-spacer),
.e-toolbar.e-bigger .e-toolbar-item:not(.e-separator):not(.e-spacer) {
  min-width: 49px;
  padding: 4px;
}
.e-bigger .e-toolbar .e-toolbar-item.e-separator,
.e-toolbar.e-bigger .e-toolbar-item.e-separator {
  height: 30px;
  margin: 8px;
  min-height: 30px;
}
.e-bigger .e-toolbar .e-toolbar-item.e-popup-text .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-bigger .e-toolbar .e-toolbar-item.e-popup-text .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item.e-popup-text .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-item.e-popup-text .e-tbar-btn.e-btn.e-control.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 4px 6px 3px 6px;
}
.e-bigger .e-toolbar .e-hor-nav,
.e-toolbar.e-bigger .e-hor-nav {
  min-height: 46px;
  min-width: 36px;
}
.e-bigger .e-toolbar.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-toolbar.e-bigger.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child {
  margin-left: 0;
}
.e-bigger .e-toolbar.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-toolbar.e-bigger.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin-right: 0;
}
.e-bigger .e-toolbar.e-vertical .e-toolbar-item:not(.e-separator),
.e-toolbar.e-bigger.e-vertical .e-toolbar-item:not(.e-separator) {
  min-height: 38px;
}
.e-bigger .e-toolbar.e-vertical .e-toolbar-item.e-separator,
.e-toolbar.e-bigger.e-vertical .e-toolbar-item.e-separator {
  height: auto;
  margin: 5px 10px;
  min-height: auto;
}
.e-bigger .e-toolbar.e-vertical .e-hor-nav,
.e-toolbar.e-bigger.e-vertical .e-hor-nav {
  min-height: 40px;
  min-width: 50px;
}
.e-bigger .e-toolbar .e-toolbar-pop .e-toolbar-item,
.e-toolbar.e-bigger .e-toolbar-pop .e-toolbar-item {
  height: 38px;
}
.e-bigger .e-toolbar .e-toolbar-pop .e-toolbar-item:not(.e-separator),
.e-toolbar.e-bigger .e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  min-width: 48px;
  padding: 0;
  min-height: 46px;
}
.e-bigger .e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn,
.e-toolbar.e-bigger .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn {
  min-height: 46px;
  padding: 8px 18px;
  min-width: 100%;
}
.e-bigger .e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-icons.e-btn-icon,
.e-toolbar.e-bigger .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-icons.e-btn-icon {
  margin-left: 0;
  padding: 0 6px 0 0;
  font-size: 16px;
}
.e-bigger .e-toolbar .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-toolbar.e-bigger .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 0 0 0 6px;
  font-size: 16px;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended {
  min-height: 46px;
  padding-bottom: 0;
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 0;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item {
  min-height: 46px;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn,
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control {
  min-height: 38px;
  min-width: 38px;
  padding: 0 8px;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-tbar-btn-text,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control .e-tbar-btn-text {
  padding: 4px;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:hover, .e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:focus, .e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:active,
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:hover,
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:focus,
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:active,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:hover,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:focus,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn:active,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:hover,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:focus,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item .e-tbar-btn.e-btn.e-control:active {
  padding: 0 8px;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item:not(.e-separator),
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item:not(.e-separator) {
  min-width: 49px;
  padding: 4px;
}
.e-bigger .e-toolbar.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-separator,
.e-toolbar.e-bigger.e-extended-toolbar .e-toolbar-extended .e-toolbar-item.e-separator {
  height: 30px;
  margin: 8px;
  min-height: 30px;
}
.e-bigger .e-toolbar.e-extended-toolbar.e-tbar-extended,
.e-toolbar.e-bigger.e-extended-toolbar.e-tbar-extended {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.e-bigger .e-toolbar.e-extended-toolbar.e-tbar-extended .e-toolbar-extended,
.e-toolbar.e-bigger.e-extended-toolbar.e-tbar-extended .e-toolbar-extended {
  border-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-toolbar.e-bigger.e-rtl .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 4px 6px;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon.e-icon-right,
.e-toolbar.e-bigger.e-rtl .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon.e-icon-right {
  padding: 4px 6px;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text,
.e-toolbar.e-bigger.e-rtl .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 4px;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content > .e-toolbar-item:last-child,
.e-toolbar.e-bigger.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content > .e-toolbar-item:last-child {
  margin-left: 12px;
  margin-right: initial;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-center .e-toolbar-item,
.e-bigger .e-toolbar.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item,
.e-toolbar.e-bigger.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-center .e-toolbar-item,
.e-toolbar.e-bigger.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item {
  margin: 0;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item:last-child,
.e-toolbar.e-bigger.e-rtl .e-toolbar-items .e-hscroll-bar .e-hscroll-content .e-toolbar-right .e-toolbar-item:last-child {
  margin-left: 12px;
  margin-right: 0;
}
.e-bigger .e-toolbar.e-rtl .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-toolbar.e-bigger.e-rtl .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child {
  margin-left: 0;
  margin-right: 12px;
}
.e-bigger .e-toolbar.e-extended-toolbar.e-rtl .e-toolbar-extended,
.e-toolbar.e-bigger.e-extended-toolbar.e-rtl .e-toolbar-extended {
  padding-right: 12px;
}