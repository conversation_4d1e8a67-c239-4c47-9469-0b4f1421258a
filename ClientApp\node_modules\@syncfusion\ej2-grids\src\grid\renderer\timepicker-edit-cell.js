var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
import { extend } from '@syncfusion/ej2-base';
import { TimePicker } from '@syncfusion/ej2-calendars';
import { isEditable, getObject, parentsUntil, isCellHaveWidth } from '../base/util';
import { EditCellBase } from './edit-cell-base';
/**
 * `TimePickerEditCell` is used to handle Timepicker cell type editing.
 *
 * @hidden
 */
var TimePickerEditCell = /** @class */ (function (_super) {
    __extends(TimePickerEditCell, _super);
    function TimePickerEditCell() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TimePickerEditCell.prototype.write = function (args) {
        var isInlineEdit = this.parent.editSettings.mode !== 'Dialog';
        var rowDataValue = getObject(args.column.field, args.rowData);
        rowDataValue = rowDataValue ? new Date(rowDataValue) : null;
        this.obj = new TimePicker(extend({
            floatLabelType: isInlineEdit ? 'Never' : 'Always',
            value: rowDataValue,
            placeholder: isInlineEdit ?
                '' : args.column.headerText, enableRtl: this.parent.enableRtl,
            enabled: isEditable(args.column, args.requestType, args.element) && isCellHaveWidth(parentsUntil(args.element, 'e-rowcell')),
            cssClass: this.parent.cssClass ? this.parent.cssClass : null
        }, args.column.edit.params));
        this.obj.appendTo(args.element);
    };
    return TimePickerEditCell;
}(EditCellBase));
export { TimePickerEditCell };
