<ng-template #bottomTitleTemplate>
  <p class="text-18px !mt-[10px] text-center">
      Centre d'Audio, de Notes et d'Ébauches Visuelles Agrégés
  </p>
</ng-template>

<app-inno-page-content-wrapper
  title="C.A.N.E.V.A."
  [bottomTitleTemplate]="bottomTitleTemplate"
  >
  <div class="container-full">
    <div class="flex justify-center">
      <button class="button-outline w-[10dvw] h-[10dvw] md:w-[50px] md:h-[50px] flex justify-center items-center" (click)="createCanevas()">
        <img class="h-[25px] object-contain" src="assets/img/icon/ic_plus.svg" alt="Icon">
      </button>
    </div>

    <div class="w-full flex flex-col gap-[20px] mt-[5dvw] md:mt-[25px]">
      @for(item of canevasList; track item.id; let i = $index)
      {
      <div class="w-full pt-[4dvw] md:pt-[20px] border-t border-gray-1">
        <app-inno-collapse [customTargetTemplate]="templateCollapse" [isExpanded]="item['isExpanded']">
          <ng-template #templateCollapse let-data>
            <div class="w-full">
              @if(data.isExpanded) {
              <div class="w-full flex justify-center gap-[8dvw] md:gap-[36px] mb-[20px]">
                <button (click)="addNote(item)">
                  <img class="h-[6.6dvw] md:h-[30px] object-contain" src="assets/img/icon/ic_add_document.svg" alt="Icon">
                </button>
                <button (click)="handleUploadCaptureImage(item.id)" >
                  <img class="w-[5.5dvw] md:w-[29px] object-contain" src="assets/img/icon/ic_add_picture.svg" alt="Icon">
                </button>
                <button (click)="handleAudioRecording(item.id)">
                  <img class="h-[6.6dvw] md:h-[30px] object-contain" src="assets/img/icon/ic_mic.svg" alt="Icon">
                </button>
                <button (click)="uploadDocument(documentType.File, item.id)">
                  <img class="w-[5dvw] md:w-[25px] object-contain" src="assets/img/icon/ic_upload.svg" alt="Icon">
                </button>
              </div>
              }
              <div class="w-full flex gap-[15px] xs:gap-[20px] items-start">

                <div class="flex shrink-0 pt-[3px] gap-[5.5dvw] md:gap-[40px]">
                  <button (click)="data?.toggle()">
                    @if(data.isExpanded) {
                    <img class="w-[3dvw] md:w-[14px] object-contain" src="assets/img/icon/ic_collapse.svg" alt="Icon">
                    } @else {
                    <img class="w-[3dvw] md:w-[14px] object-contain" src="assets/img/icon/ic_expand.svg" alt="Icon">
                    }
                  </button>
                  <button (click)="share(item.id)">
                    <img class="h-[5.2dvw] md:h-[27px] object-contain" src="assets/img/icon/ic_share.svg" alt="Icon">
                  </button>
                  <button (click)="strikethrough($event, item)">
                    <img class="h-[5dvw] md:h-[25px] object-contain" src="assets/img/icon/ic_plan.svg" alt="Icon">
                  </button>
                  <button (click)="deleteCanevas(item.id)">
                    <img class="w-[4dvw] md:w-[25px] object-contain" src="assets/img/icon/ic_trash.svg" alt="Icon">
                  </button>
                </div>
                <app-inno-red-line-through [isShow]="item.strikethrough">
                  <div class="w-full flex">
                    <p class="text-26px w-[30px] shrink-0">
                      {{ i + 1 }}.
                    </p>
                    <div class="w-full">
                      <p class="text-26px">
                        {{item.title}}
                      </p>
                      <div class="flex flex-col w-full gap-[1px] mt-[8px]">
                        <p class="text-17px text-gray-1">
                          Par : {{item.createdBy }} {{item.createdAt | date:'dd-MM-yyyy - HH:mm' }}
                        </p>
                      </div>
                    </div>
                  </div>
                </app-inno-red-line-through>
              </div>
            </div>
          </ng-template>

          <div class="w-full grid gap-[15px] pt-[5dvw] md:pt-[22px]">
            @if (item.safeHtml){
            <div class="rowLayout">
              <p class="text-21px">Notes:</p>
              <div class="w-full">
                <div class="text-21px" [innerHtml]="item.safeHtml">
                </div>
                <p class="text-17px text-gray-1 !mt-[8px]">
                  Crée : {{item.noteCreatedBy}} - {{ item.noteCreatedAt | date:'dd-MM-yyyy - HH:mm' }}
                </p>
              </div>
            </div>
            }
            <div class="rowLayout">
              @for(image of item.images; track image.id){
              <p class="txtContent">Photo:</p>
              <div class="w-full">
                <div class="w-full h-[150px] xs:h-[250px] border-[1.5px] !border-blue-1 rounded-[16px]">
                  <img class="w-full h-full object-cover rounded-[16px]" [src]="image ? getImageURL(image.path) : 'assets/img/image_default.svg'" alt="Demo"
                       (click)="openEditor(image, item)">
                </div>
                <p class="text-17px text-gray-1 !mt-[8px]">

                  Par : {{image.createdBy }} {{image.createdAt | date:'dd-MM-yyyy - HH:mm' }}
                </p>
              </div>
              }
            </div>
            @for(doc of item.audio; track doc.id){
            <div class="rowLayout">
              <p class="text-21px">Audio:</p>
              <div class="w-full grid gap-[2px]">
                <app-inno-file fileName="{{doc.fileName}}" id="{{doc.id}}" createdBy="{{doc.createdBy}}" createdAt="{{doc.createdAt}}" />
              </div>
            </div>
            }
            @for(doc of item.document; track doc.id){
            <div class="rowLayout">
              <p class="text-21px">Doc:</p>
              <div class="w-full grid gap-[2px]">
                <app-inno-file fileName="{{doc.fileName}}" id="{{doc.id}}" createdBy="{{doc.createdBy}}" createdAt="{{doc.createdAt}}" />
              </div>
            </div>
            }
          </div>
        </app-inno-collapse>
      </div>
      }
    </div>
  </div>
</app-inno-page-content-wrapper>
@if(imageToEdit){
<app-inno-image-editor [imageUrl]="getImageURL(imageToEdit?.path)"
                       [fileName]="imageToEdit?.fileName"
                       (imageSaved)="onImageSaved($event)"
                       (close)="imageToEdit = null"></app-inno-image-editor>
}
