@import 'ej2-base/styles/definition/tailwind-dark.scss';
@import 'signature/tailwind-dark-definition.scss';
@import 'signature/all.scss';

@import 'input/tailwind-dark-definition.scss';
@import 'input/icons/tailwind-dark.scss';
@import 'input/all.scss';
@import 'input/bigger.scss';
@import 'numerictextbox/tailwind-dark-definition.scss';
@import 'numerictextbox/icons/tailwind-dark.scss';
@import 'numerictextbox/all.scss';
@import 'numerictextbox/bigger.scss';
@import 'maskedtextbox/tailwind-dark-definition.scss';
@import 'maskedtextbox/all.scss';
@import 'maskedtextbox/bigger.scss';
@import 'ej2-popups/styles/popup/tailwind-dark-definition.scss';
@import 'ej2-popups/styles/tooltip/tailwind-dark-definition.scss';
@import 'slider/tailwind-dark-definition.scss';
@import 'slider/all.scss';
@import 'slider/bigger.scss';
@import 'textbox/tailwind-dark-definition.scss';
@import 'textbox/all.scss';
@import 'textbox/bigger.scss';
@import 'textarea/tailwind-dark-definition.scss';
@import 'textarea/all.scss';
@import 'ej2-buttons/styles/button/tailwind-dark-definition.scss';
@import 'ej2-popups/styles/spinner/tailwind-dark-definition.scss';
@import 'uploader/tailwind-dark-definition.scss';
@import 'uploader/icons/tailwind-dark.scss';
@import 'uploader/all.scss';
@import 'uploader/bigger.scss';
@import 'ej2-splitbuttons/styles/split-button/tailwind-dark-definition.scss';
@import 'color-picker/tailwind-dark-definition.scss';
@import 'color-picker/icons/tailwind-dark.scss';
@import 'color-picker/all.scss';
@import 'color-picker/bigger.scss';
@import 'rating/tailwind-dark-definition.scss';
@import 'rating/all.scss';
@import 'rating/bigger.scss';
@import 'data-form/tailwind-dark-definition.scss';
@import 'data-form/all.scss';
@import 'otp-input/tailwind-dark-definition.scss';
@import 'otp-input/all.scss';
@import 'otp-input/bigger.scss';
@import 'speech-to-text/tailwind-dark-definition.scss';
@import 'speech-to-text/icons/tailwind-dark.scss';
@import 'speech-to-text/all.scss';
@import 'speech-to-text/bigger.scss';
@import 'smart-textarea/tailwind-dark-definition.scss';
@import 'smart-textarea/all.scss';