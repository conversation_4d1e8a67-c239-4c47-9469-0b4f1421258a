@import 'ej2-base/styles/definition/tailwind-dark.scss';
@import 'pager/tailwind-dark-definition.scss';
@import 'pager/icons/tailwind-dark.scss';
@import 'pager/all.scss';
@import 'h-scroll/tailwind-dark-definition.scss';
@import 'h-scroll/icons/tailwind-dark.scss';
@import 'h-scroll/all.scss';
@import 'v-scroll/tailwind-dark-definition.scss';
@import 'v-scroll/icons/tailwind-dark.scss';
@import 'v-scroll/all.scss';
@import 'ej2-buttons/styles/button/tailwind-dark-definition.scss';
@import 'ej2-popups/styles/popup/tailwind-dark-definition.scss';
@import 'toolbar/tailwind-dark-definition.scss';
@import 'toolbar/icons/tailwind-dark.scss';
@import 'toolbar/all.scss';
@import 'accordion/tailwind-dark-definition.scss';
@import 'accordion/icons/tailwind-dark.scss';
@import 'accordion/all.scss';
@import 'carousel/tailwind-dark-definition.scss';
@import 'carousel/icons/tailwind-dark.scss';
@import 'carousel/all.scss';

@import 'context-menu/tailwind-dark-definition.scss';
@import 'context-menu/icons/tailwind-dark.scss';
@import 'context-menu/all.scss';
@import 'tab/tailwind-dark-definition.scss';
@import 'tab/icons/tailwind-dark.scss';
@import 'tab/all.scss';
@import 'ej2-inputs/styles/input/tailwind-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/tailwind-dark-definition.scss';
@import 'treeview/tailwind-dark-definition.scss';
@import 'treeview/icons/tailwind-dark.scss';
@import 'treeview/all.scss';
@import 'sidebar/tailwind-dark-definition.scss';
@import 'sidebar/all.scss';
@import 'menu/tailwind-dark-definition.scss';
@import 'menu/icons/tailwind-dark.scss';
@import 'menu/all.scss';
@import 'breadcrumb/tailwind-dark-definition.scss';
@import 'breadcrumb/icons/tailwind-dark.scss';
@import 'breadcrumb/all.scss';
@import 'appbar/tailwind-dark-definition.scss';
@import 'appbar/all.scss';
@import 'ej2-popups/styles/tooltip/tailwind-dark-definition.scss';
@import 'stepper/tailwind-dark-definition.scss';
@import 'stepper/icons/tailwind-dark.scss';
@import 'stepper/all.scss';