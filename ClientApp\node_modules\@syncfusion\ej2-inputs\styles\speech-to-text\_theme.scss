@include export-module('speechtotext-theme') {
  // Speechtotext component styles
  .e-speech-to-text {
    box-shadow: $stt-box-shadow;

    &:hover:not(:focus),
    &:active,
    &.e-active,
    &:disabled {
      box-shadow: $stt-box-shadow;
    }

    &:focus {
      @if ($skin-name != 'tailwind' and $skin-name != 'tailwind-dark' and $skin-name != 'tailwind3' and $skin-name != 'bootstrap4' and $skin-name != 'bootstrap5' and $skin-name != 'bootstrap5.3' and $skin-name != 'bootstrap5-dark') {
        box-shadow: $stt-box-shadow;
      }
      @if $skin-name == 'tailwind' {
        box-shadow: $stt-focus-box-shadow;
      }
    }
    @if $skin-name == 'tailwind3' {
      &:focus-visible {
        box-shadow: $stt-focus-box-shadow !important; /* stylelint-disable-line declaration-no-important */
      }
    }
    &.e-flat {
      box-shadow: none;
    }
  }
}
