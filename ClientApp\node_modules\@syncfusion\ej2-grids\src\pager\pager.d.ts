import { Component, ModuleD<PERSON>laration, L10n, EmitType, KeyboardEventArgs } from '@syncfusion/ej2-base';
import { INotifyPropertyChanged } from '@syncfusion/ej2-base';
import { PagerModel } from './pager-model';
import { PagerDropDown } from './pager-dropdown';
import { NumericContainer } from './numeric-container';
import { PagerMessage } from './pager-message';
import { ExternalMessage } from './external-message';
/** @hidden */
export interface IRender {
    render(): void;
    refresh(): void;
}
/**
 * @hidden
 */
export interface keyPressHandlerKeyboardEventArgs extends KeyboardEvent {
    cancel?: boolean;
}
/**
 * Represents the `Pager` component.
 * ```html
 * <div id="pager"/>
 * ```
 * ```typescript
 * <script>
 *   var pagerObj = new Pager({ totalRecordsCount: 50, pageSize:10 });
 *   pagerObj.appendTo("#pager");
 * </script>
 * ```
 */
export declare class Pager extends Component<HTMLElement> implements INotifyPropertyChanged {
    /*** @hidden */
    totalPages: number;
    /** @hidden */
    templateFn: Function;
    /** @hidden */
    hasParent: boolean;
    /*** @hidden */
    previousPageNo: number;
    /** @hidden */
    isAllPage: boolean;
    checkAll: boolean;
    /** @hidden */
    isPagerResized: boolean;
    /** @hidden */
    keyAction: string;
    /** @hidden */
    avgNumItems: number;
    private averageDetailWidth;
    private defaultConstants;
    private pageRefresh;
    private parent;
    private firstPagerFocus;
    /** @hidden */
    isCancel: boolean;
    /** @hidden */
    isInteracted: boolean;
    /*** @hidden */
    localeObj: L10n;
    /**
     * `containerModule` is used to manipulate numeric container behavior of Pager.
     */
    containerModule: NumericContainer;
    /**
     * `pagerMessageModule` is used to manipulate pager message of Pager.
     */
    pagerMessageModule: PagerMessage;
    /**
     * `externalMessageModule` is used to manipulate external message of Pager.
     */
    externalMessageModule: ExternalMessage;
    /**
     * @hidden
     * `pagerdropdownModule` is used to manipulate pageSizes of Pager.
     */
    pagerdropdownModule: PagerDropDown;
    /**
     * If `enableQueryString` set to true,
     * then it pass current page information as a query string along with the URL while navigating to other page.
     *
     * @default false
     */
    enableQueryString: boolean;
    /**
     * If `enableExternalMessage` set to true, then it adds the message to Pager.
     *
     * @default false
     */
    enableExternalMessage: boolean;
    /**
     * If `enablePagerMessage` set to true, then it adds the pager information.
     *
     * @default true
     */
    enablePagerMessage: boolean;
    /**
     * Defines the records count of visible page.
     *
     * @default 12
     */
    pageSize: number;
    /**
     * Defines the number of pages to display in pager container.
     *
     * @default 10
     */
    pageCount: number;
    /**
     * Defines the current page number of pager.
     *
     * @default 1
     */
    currentPage: number;
    /**
     * Gets or Sets the total records count which is used to render numeric container.
     *
     * @default null
     */
    totalRecordsCount: number;
    /**
     * Defines the external message of Pager.
     *
     * @default null
     */
    externalMessage: string;
    /**
     * If `pageSizes` set to true or Array of values,
     * It renders DropDownList in the pager which allow us to select pageSize from DropDownList.
     *
     * @default false
     */
    pageSizes: boolean | (number | string)[];
    /**
     *  Defines the template as string or HTML element ID which renders customized elements in pager instead of default elements.
     *
     * @default null
     * @aspType string
     */
    template: string | Function;
    /**
     * Defines the customized text to append with numeric items.
     *
     * @default null
     */
    customText: string;
    /**
     * Triggers when click on the numeric items.
     *
     * @default null
     */
    click: EmitType<Object>;
    /**
     * Defines the own class for the pager element.
     *
     * @default ''
     */
    cssClass: string;
    /**
     * Triggers after pageSize is selected in DropDownList.
     *
     * @default null
     */
    dropDownChanged: EmitType<Object>;
    /**
     * Triggers when Pager is created.
     *
     * @default null
     */
    created: EmitType<Object>;
    /**
     * Constructor for creating the component.
     *
     * @param {PagerModel} options - specifies the options
     * @param {string} element - specifies the element
     * @param {string} parent - specifies the pager parent
     * @hidden
     */
    constructor(options?: PagerModel, element?: string | HTMLElement, parent?: object);
    /**
     * To provide the array of modules needed for component rendering
     *
     * @returns {ModuleDeclaration[]} returns the modules declaration
     * @hidden
     */
    protected requiredModules(): ModuleDeclaration[];
    /**
     * Initialize the event handler
     *
     * @returns {void}
     * @hidden
     */
    protected preRender(): void;
    /**
     * To Initialize the component rendering
     *
     * @returns {void}
     */
    protected render(): void;
    /**
     * Get the properties to be maintained in the persisted state.
     *
     * @returns {string} returns the persist data
     * @hidden
     */
    getPersistData(): string;
    /**
     * To destroy the Pager component.
     *
     * @method destroy
     * @returns {void}
     */
    destroy(): void;
    /**
     * Destroys the given template reference.
     *
     * @param {string[]} propertyNames - Defines the collection of template name.
     * @param {any} index - Defines the index
     */
    destroyTemplate(propertyNames?: string[], index?: any): void;
    /**
     * For internal use only - Get the module name.
     *
     * @returns {string} returns the module name
     * @private
     */
    protected getModuleName(): string;
    /**
     * Called internally if any of the property value changed.
     *
     * @param {PagerModel} newProp - specifies the new property
     * @param {PagerModel} oldProp - specifies the old propety
     * @returns {void}
     * @hidden
     */
    onPropertyChanged(newProp: PagerModel, oldProp: PagerModel): void;
    private wireEvents;
    private unwireEvents;
    private onFocusIn;
    private onFocusOut;
    private keyDownHandler;
    private keyPressHandler;
    private addListener;
    private removeListener;
    private onKeyPress;
    /**
     * @returns {boolean} - Return the true value if pager has focus
     * @hidden */
    checkPagerHasFocus(): boolean;
    /**
     * @returns {void}
     * @hidden */
    setPagerContainerFocus(): void;
    /**
     * @returns {void}
     * @hidden */
    setPagerFocus(): void;
    private setPagerFocusForActiveElement;
    private setTabIndexForFocusLastElement;
    /**
     * @param {KeyboardEventArgs} e - Keyboard Event Args
     * @returns {void}
     * @hidden */
    changePagerFocus(e: KeyboardEventArgs): void;
    private getFocusedTabindexElement;
    private changeFocusByTab;
    private changeFocusByShiftTab;
    /**
     * @returns {void}
     * @hidden */
    checkFirstPagerFocus(): boolean;
    private navigateToPageByEnterOrSpace;
    private navigateToPageByKey;
    private checkFocusInAdaptiveMode;
    private changeFocusInAdaptiveMode;
    private removeTabindexLastElements;
    private getActiveElement;
    /**
     * @returns {Element} - Returns DropDown Page
     * @hidden */
    getDropDownPage(): Element;
    private getFocusedElement;
    private getClass;
    private getElementByClass;
    /**
     * @param {Element} element - Pager element
     * @param {Element[]} previousElements - Iterating pager element
     * @returns {Element[]} - Returns focusable pager element
     * @hidden */
    getFocusablePagerElements(element: Element, previousElements: Element[]): Element[];
    private addFocus;
    private removeFocus;
    /**
     * Gets the localized label by locale keyword.
     *
     * @param  {string} key - specifies the key
     * @returns {string} returns the localized label
     */
    getLocalizedLabel(key: string): string;
    /**
     * Navigate to target page by given number.
     *
     * @param  {number} pageNo - Defines page number.
     * @returns {void}
     */
    goToPage(pageNo: number): void;
    /**
     * @param {number} pageSize - specifies the pagesize
     * @returns {void}
     * @hidden
     */
    setPageSize(pageSize: number): void;
    private checkpagesizes;
    private checkGoToPage;
    private currentPageChanged;
    private pagerTemplate;
    /**
     * @returns {void}
     * @hidden
     */
    updateTotalPages(): void;
    /**
     * @returns {Function} returns the function
     * @hidden
     */
    getPagerTemplate(): Function;
    /**
     * @param {string | Function} template - specifies the template
     * @returns {Function} returns the function
     * @hidden
     */
    compile(template: string | Function): Function;
    /**
     * Refreshes page count, pager information and external message.
     *
     * @returns {void}
     */
    refresh(): void;
    private updateRTL;
    private initLocalization;
    private updateQueryString;
    private getUpdatedURL;
    private renderFirstPrevDivForDevice;
    private renderNextLastDivForDevice;
    private addAriaLabel;
    private isReactTemplate;
    /**
     * Loop through all the inner elements of pager to calculate the required width for pager child elements.
     *
     * @returns {number} returns the actual width occupied by pager elements.
     */
    private calculateActualWidth;
    /**
     * Resize pager component by hiding pager component's numeric items based on total width available for pager.
     *
     * @returns {void}
     */
    private resizePager;
}
