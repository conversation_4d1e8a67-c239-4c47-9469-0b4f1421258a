/*! component's theme wise override definitions and variables */
$vscroll-skin: 'highcontrast' !default;
$vscroll-nav-nrml-height: 40px !default;
$vscroll-hover-font: $hover-font-color !default;
$vscroll-active-font-color: $active-font-color !default;
$vscroll-border-size: $border-size !default;
$vscroll-border-type: solid !default;

$vscroll-default-bg: $bg-base-0 !default;
$vscroll-press-bg: $selection-bg !default;
$vscroll-active-bg: $selection-bg !default;
$vscroll-nav-nrml-minheight: 40px !default;
$vscroll-nav-bgr-minheight: 50px !default;
$vscroll-nav-nrml-width: 40px !default;
$vscroll-nav-bgr-width: 50px !default;
$vscroll-nrml-padding: 0 $vscroll-nav-nrml-width !default;
$vscroll-bgr-padding: $vscroll-nav-bgr-width 0 !default;
$vscroll-border-size: 1px !default;
$vscroll-default-border: $border-fg !default;
$vscroll-box-shadow: none !default;
$vscroll-hover-bg: $hover-bg !default;
$vscroll-hover-border-color: transparent !default;
$vscroll-focus-border: 0 !default;
$vscroll-active-border: 0 !default;
$vscroll-hover-border: 0 !default;
$vscroll-active-box-shadow: none !default;
$vscroll-default-icon-color: $selection-bg !default;
$vscroll-overlay-opacity: .5 !default;
$vscroll-overlay-bg: $bg-base-10 !default;
$vscroll-overlay-start: rgba($vscroll-overlay-bg, 0) !default;
$vscroll-overlay-end: rgba($vscroll-overlay-bg, 1) !default;
$vscroll-right-bg: linear-gradient(-270deg, $vscroll-overlay-start 0%, $vscroll-overlay-end 100%) !default;
$vscroll-left-bg:  linear-gradient(-270deg, $vscroll-overlay-end 0%, $vscroll-overlay-start 100%) !default;

$vscroll-device-arrow-box-shadow: -4px 0 8px 0 rgba($shadow, .06) !default;
$vscroll-device-arrow-rtl-box-shadow: 4px 0 8px 0 rgba($shadow, .06) !default;
$vscroll-device-arrow-bg: $bg-base-15 !default;
$vscroll-device-arrow-border-size: 1px !default;
$vscroll-device-arrow-border-color: $border-fg !default;
$vscroll-device-arrow-color: $selection-bg !default;
$vscroll-device-arrow-size: 14px !default;
$vscroll-device-arrow-width: 52px !default;

@mixin vscroll-btn-animation {
  content: '';
}

@mixin vscroll-btn-animation-after {
  content: '';
}
