<app-inno-modal-wrapper
  title="Enregistrement Audio"
  (onClose)="handleClose()">

  <div class="container-dialog">
    <!-- Microphone Permission Error -->
    @if (!isMicrophoneAllowed) {
      <div class="w-full mb-4 text-center">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div class="flex items-center justify-center mb-2">
            <img class="h-8 w-8 object-contain opacity-50" src="assets/img/icon/ic_mic.svg" alt="Microphone Off">
          </div>
          <p class="text-red-600 font-medium mb-2">Accès au microphone refusé</p>
          <p class="text-red-500 text-sm mb-3">
            Pour enregistrer de l'audio, vous devez autoriser l'accès au microphone.
          </p>
          <button
            (click)="requestMicrophoneAgain()"
            type="button"
            class="button-outline-modal">
            Autoriser le microphone
          </button>
        </div>
      </div>
    }

    <!-- Loading State -->
    @if (isInitializing) {
      <div class="w-full text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">Initialisation du microphone...</p>
      </div>
    }

    <!-- Main Recording Interface -->
    @if (isMicrophoneAllowed && !isInitializing) {
      <div class="w-full">
        <!-- Audio Visualizer -->
        <div class="w-full mb-6">
          <canvas
            #visualizer
            class="w-full h-24 bg-gray-100 rounded-lg border"
            width="400"
            height="100">
          </canvas>
        </div>

        <!-- Recording Status and Timer -->
        <div class="text-center mb-6">
          @if (isRecording) {
            <div class="flex items-center justify-center mb-2">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-2"></div>
              <span class="text-red-600 font-medium">
                @if (isPaused) {
                  PAUSE
                } @else {
                  ENREGISTREMENT
                }
              </span>
            </div>
          }

          <div class="text-2xl font-mono font-bold text-gray-800">
            {{ formatTime(recordingTime) }}
          </div>
        </div>

        <!-- Recording Controls -->
        @if (!hasRecording) {
          <div class="flex justify-center gap-4 mb-6">
            @if (!isRecording) {
              <!-- Start Recording Button -->
              <button
                (click)="startRecording()"
                class="bg-red-500 hover:bg-red-600 text-white rounded-full p-4 transition-colors">
                <img class="h-8 w-8 object-contain filter invert" src="assets/img/icon/ic_mic.svg" alt="Start Recording">
              </button>
            } @else {
              <!-- Pause/Resume Button -->
              <button
                (click)="isPaused ? resumeRecording() : pauseRecording()"
                class="bg-yellow-500 hover:bg-yellow-600 text-white rounded-full p-4 transition-colors">
                @if (isPaused) {
                  <img class="h-8 w-8 object-contain filter invert" src="assets/img/icon/ic_play.svg" alt="Resume">
                } @else {
                  <img class="h-8 w-8 object-contain filter invert" src="assets/img/icon/ic_pause.svg" alt="Pause">
                }
              </button>

              <!-- Stop Recording Button -->
              <button
                (click)="stopRecording()"
                class="bg-gray-600 hover:bg-gray-700 text-white rounded-full p-4 transition-colors">
                <img class="h-8 w-8 object-contain filter invert" src="assets/img/icon/ic_close.svg" alt="Stop">
              </button>
            }
          </div>
        }

        <!-- Upload Status -->
        @if (isUploading) {
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-center">
            <div class="flex items-center justify-center mb-2">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
              <span class="text-blue-600 font-medium">Téléchargement en cours...</span>
            </div>
            <p class="text-blue-500 text-sm">Veuillez patienter pendant l'envoi de votre enregistrement</p>
          </div>
        }

        <!-- Playback Controls (shown after recording) -->
        @if (hasRecording && !isUploading) {
          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-800 mb-4 text-center">Aperçu de l'enregistrement</h3>

            <!-- Audio Element (hidden) -->
            <audio #audioPlayer style="display: none;"></audio>

            <!-- Playback Timer -->
            <div class="text-center mb-4">
              <div class="text-xl font-mono text-gray-700">
                {{ formatTime(playbackTime) }} / {{ formatTime(duration) }}
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="mb-4">
              <input
                type="range"
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                [min]="0"
                [max]="duration"
                [value]="playbackTime"
                (input)="onSeek($event)"
                step="0.1">
            </div>

            <!-- Playback Controls -->
            <div class="flex justify-center gap-4 mb-4">
              <button
                (click)="togglePlayback()"
                class="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 transition-colors">
                @if (isPlaying) {
                  <img class="h-6 w-6 object-contain filter invert" src="assets/img/icon/ic_pause.svg" alt="Pause">
                } @else {
                  <img class="h-6 w-6 object-contain filter invert" src="assets/img/icon/ic_play.svg" alt="Play">
                }
              </button>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-center gap-3">
              <button
                (click)="startNewRecording()"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                Nouvel enregistrement
              </button>
              <button
                (click)="discardRecording()"
                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                Supprimer
              </button>
              <button
                (click)="handleClose()"
                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                Fermer
              </button>
            </div>
          </div>
        }

        <!-- Instructions -->
        @if (!isRecording && !hasRecording) {
          <div class="text-center text-gray-600">
            <p class="mb-2">Cliquez sur le bouton microphone pour commencer l'enregistrement</p>
            <p class="text-sm">Assurez-vous d'être dans un environnement calme pour une meilleure qualité</p>
          </div>
        }
      </div>
    }
  </div>
</app-inno-modal-wrapper>
