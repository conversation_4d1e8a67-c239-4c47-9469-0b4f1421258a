



/* stylelint-disable */
.e-btn.e-primary:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
}

.e-btn.e-success:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(23, 177, 105, 0.24) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(23, 177, 105, 0.24) !important;
}

.e-btn.e-info:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
}

.e-btn.e-warning:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(247, 143, 8, 0.24) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(247, 143, 8, 0.24) !important;
}

.e-btn.e-danger:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(240, 68, 55, 0.24) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(240, 68, 55, 0.24) !important;
}

.e-btn.e-outline.e-info {
  background: transparent !important;
  border-color: var(--color-sf-utility-info-border-color) !important;
  color: var(--color-sf-utility-info-text) !important;
}

.e-btn.e-outline.e-info:hover {
  background: var(--color-sf-utility-info-bg-color-hover) !important;
  border-color: var(--color-sf-utility-info-border-color) !important;
  color: var(--color-sf-utility-info-text-hover) !important;
}

.e-btn.e-outline.e-info:active {
  background: var(--color-sf-utility-info-bg-color-pressed) !important;
  border-color: var(--color-sf-utility-info-border-color) !important;
  color: var(--color-sf-utility-info-text-hover) !important;
}

.e-btn.e-flat.e-info {
  color: var(--color-sf-utility-info-text) !important;
}

.e-bigger .e-btn,
.e-bigger .e-btn,
.e-bigger .e-css.e-btn,
.e-bigger.e-css.e-btn {
  font-size: 14px;
  line-height: 1.572em;
  padding: 8px 15px;
  border-radius: 8px !important;
  font-weight: 600;
}

/* stylelint-disable property-no-vendor-prefix */
@-webkit-keyframes material-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes material-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes fabric-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes fabric-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons::before {
  content: "\e7e7";
}

.e-upload .e-upload-files .e-icons.e-file-pause-btn::before {
  content: "\e77b";
}

.e-upload .e-upload-files .e-icons.e-file-reload-btn::before {
  content: "\e706";
}

.e-upload .e-upload-files .e-icons.e-file-play-btn::before {
  content: "\e70c";
}

.e-upload .e-upload-files .e-file-delete-btn.e-icons::before {
  content: "\e820";
}

.e-upload .e-upload-files .e-file-abort-btn.e-icons::before {
  content: "\e81b";
}

.e-upload .e-upload-files .e-icons.e-msie::before {
  position: relative;
  right: 10px;
}

.e-upload .e-upload-files .e-icons.e-file-abort-icon.e-msie::before {
  right: 12px;
}

.e-upload {
  width: 100%;
}
.e-upload.e-control-wrapper {
  font-family: "Inter";
}
.e-upload .e-hidden-file-input {
  border: 0;
  height: 0;
  margin: 0;
  outline: none;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}
.e-upload .e-file-select-wrap {
  padding: 12px 0 12px 12px;
}
.e-upload .e-file-select-wrap .e-file-select,
.e-upload .e-file-select-wrap .e-file-select .e-uploader {
  display: inline-block;
  width: 0;
}
.e-upload .e-file-select-wrap .e-file-select .e-uploader {
  opacity: 0;
}
.e-upload .e-file-select-wrap .e-file-drop {
  font-family: inherit;
  font-size: 14px;
  margin-left: 12px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: var(--color-sf-utility-danger);
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-text-quarterary);
  display: block;
  font-size: 14px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: var(--color-sf-utility-danger);
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: var(--color-sf-utility-danger);
}
.e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 42px;
}
.e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-upload .e-upload-files {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.e-upload .e-upload-files .e-upload-file-list {
  font-family: inherit;
  font-size: 14px;
  height: 100%;
  line-height: 24px;
  line-height: 22px;
  min-height: 82px;
  position: relative;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container {
  display: block;
  height: 100%;
  margin-left: 12px;
  margin-right: 90px;
  min-height: 35px;
  position: relative;
  top: 0;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: left;
  font-family: inherit;
  font-size: 14px;
  max-width: 85%;
  overflow: hidden;
  padding-top: 8px;
  position: relative;
  text-overflow: ellipsis;
  top: 0;
  white-space: nowrap;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name::before {
  content: attr(data-tail);
  float: right;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-top: 8px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-hidden {
  visibility: hidden;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  display: block;
  font-size: 14px;
  padding: 2px 0;
  font-size: 12px;
  line-height: 18px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-bottom: 8px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-progress, .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information.e-upload-progress {
  display: none;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  display: block;
  height: 10px;
  padding-bottom: 11px;
  padding-top: 6px;
  position: absolute;
  width: 95%;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  border-radius: 4px;
  display: block;
  height: 4px;
  width: 100%;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  border-radius: 4px;
  display: inherit;
  height: 4px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: right;
  font-family: inherit;
  font-size: 14px;
  position: relative;
  right: 0;
  top: -33px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 18px;
  height: 32px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 12px;
  margin-top: -9px;
  padding: 10px;
  position: absolute;
  right: 0;
  top: 50%;
  vertical-align: middle;
  width: 32px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-abort-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-pause-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-play-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-reload-btn.e-icons.e-upload-progress {
  cursor: default;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons {
  padding: 18px 13px 18px 23px;
}
.e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  right: 36px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):hover, .e-upload .e-upload-files .e-clear-icon-focus {
  background-color: var(--color-sf-bg-secondary-hover);
  border-color: transparent;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-upload .e-upload-files .e-clear-icon-focus {
  border-radius: 50%;
}
.e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 18px;
  opacity: 1;
}
.e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 18px;
  opacity: 1;
}
.e-upload .e-file-select-wrap .e-btn, .e-upload .e-upload-actions .e-btn {
  font-family: inherit;
}
.e-upload .e-upload-actions {
  position: relative;
  text-align: right;
}
.e-upload .e-upload-actions .e-file-upload-btn {
  margin: 8px;
}
.e-upload .e-upload-actions .e-file-clear-btn {
  margin: 8px;
}
.e-upload.e-rtl .e-file-select-wrap {
  padding: 16px 12px 16px 0;
}
.e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 0;
}
.e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-upload.e-rtl .e-upload-actions {
  text-align: left;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  height: 100%;
  margin-left: 60px;
  margin-right: 11px;
  position: relative;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-rtl-container {
  direction: ltr;
  float: right;
  width: 100%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  float: right;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: right;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  direction: ltr;
  float: right;
  position: relative;
  text-align: right;
  width: 100%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  float: right;
  position: initial;
  top: 23px;
  width: 86%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: left;
  right: 0;
  top: -32px;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons {
  left: 0;
  margin-left: 11px;
  margin-right: 11px;
  right: auto;
  top: 50%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons {
  left: 36px;
  right: auto;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 36px;
}
.e-upload.e-disabled .e-file-drop {
  color: var(--color-sf-fg-disabled);
}
.e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-type, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-size, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-fg-disabled);
}
.e-upload .e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 65px;
  min-height: 65px;
}

.e-small .e-upload .e-file-select-wrap {
  padding: 12px 0 12px 12px;
}
.e-small .e-upload .e-file-select-wrap .e-file-drop {
  font-size: 12px;
  margin-left: 10px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list {
  min-height: 79px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  font-size: 12px;
  padding-top: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  font-size: 12px;
  padding: 6px 0;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  font-size: 12px;
  padding-top: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  padding-bottom: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  padding-bottom: 2px;
  padding-top: 2px;
}
.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  font-size: 10px;
  height: 24px;
  padding: 12px;
  width: 24px;
}
.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-small .e-upload.e-rtl .e-file-select-wrap {
  padding: 12px 12px 12px 0;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: var(--color-sf-utility-danger);
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-text-quarterary);
  display: block;
  font-size: 14px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: var(--color-sf-utility-danger);
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: var(--color-sf-utility-danger);
}
.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 42px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.e-upload {
  border: 1px dashed var(--color-sf-border-primary-alt);
  border-radius: 6px;
}
.e-upload .e-file-drop {
  color: var(--color-sf-text-tertiary);
  vertical-align: middle;
}
.e-upload .e-upload-files {
  border-top: 1px solid var(--color-sf-border-secondary);
}
.e-upload .e-upload-files .e-upload-file-list {
  border-bottom: 1px solid var(--color-sf-border-secondary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  color: var(--color-sf-text-primary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  color: var(--color-sf-text-primary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  color: var(--color-sf-text-quarterary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-text-primary);
  color: var(--color-sf-text-tertiary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-success {
  color: var(--color-sf-utility-success);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-fails {
  color: var(--color-sf-utility-danger);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-validation-fails {
  color: var(--color-sf-utility-danger);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap {
  background-color: var(--color-sf-bg-tertiary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-progress {
  background: var(--color-sf-brand-primary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-success {
  background: var(--color-sf-utility-success);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-failed {
  background: var(--color-sf-utility-danger);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-bar-text {
  color: var(--color-sf-text-primary);
  color: var(--color-sf-text-quarterary);
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons {
  color: var(--color-sf-fg-secondary);
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:hover, .e-upload .e-upload-files .e-file-delete-btn.e-icons:hover, .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-upload .e-upload-files .e-file-abort-btn.e-icons:hover, .e-upload .e-upload-files .e-file-pause-btn.e-icons:hover, .e-upload .e-upload-files .e-file-play-btn.e-icons:hover {
  color: var(--color-sf-fg-secondary-hover);
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-upload .e-upload-files .e-clear-icon-focus {
  background-color: var(--color-sf-bg-secondary-hover);
  border-color: transparent;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}

.e-upload-drag-hover {
  border-color: transparent;
}

.e-upload .e-upload-actions .e-file-clear-btn.e-flat:not(:hover) {
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-tap-highlight-color: transparent;
  background-color: var(--color-sf-utility-secondary-bg-color);
  border-color: var(--color-sf-utility-secondary-border-color);
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
  color: var(--color-sf-utility-secondary-text-color);
}

.e-upload .e-upload-actions .e-file-upload-btn.e-flat.e-primary:not(:hover) {
  -webkit-tap-highlight-color: transparent;
  background-color: var(--color-sf-brand-solid-primary);
  border-color: var(--color-sf-brand-solid-primary);
  color: var(--color-sf-on-brand-solid-primary);
}

.e-bigger .e-upload {
  width: 100%;
}
.e-bigger .e-upload .e-file-select-wrap {
  padding: 16px 0 16px 16px;
}
.e-bigger .e-upload .e-file-select-wrap .e-file-drop {
  font-size: 16px;
  margin-left: 16px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list {
  font-size: 14px;
  line-height: 24px;
  min-height: 96px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container {
  margin-left: 12px;
  top: 0;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  font-size: 16px;
  padding-top: 12px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  font-size: 16px;
  font-size: 14px;
  line-height: 22px;
  padding: 2px 0;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  font-size: 16px;
  padding-top: 12px;
  top: initial;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  font-size: 16px;
  padding-bottom: 12px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  height: 4px;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  height: 4px;
  width: 95%;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  height: 90%;
}
.e-bigger .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  font-size: 14px;
  right: 16px;
  top: -36px;
}
.e-bigger .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-size: 22px;
  height: 40px;
  margin: 16px;
  margin-top: -24px;
  padding: 24px;
  top: 50%;
  width: 40px;
}
.e-bigger .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger .e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-bigger .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons {
  padding: 20px 17px 20px 26px;
}
.e-bigger .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  right: 45px;
}
.e-bigger .e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 22px;
}
.e-bigger .e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 22px;
}
.e-bigger .e-upload .e-upload-actions .e-btn {
  margin-left: 12px;
}
.e-bigger .e-upload .e-upload-actions .e-file-upload-btn {
  margin: 12px;
}
.e-bigger .e-upload .e-upload-actions .e-file-clear-btn {
  margin: 12px;
}
.e-bigger .e-upload.e-rtl .e-file-select-wrap {
  padding: 20px 15px 20px 0;
}
.e-bigger .e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 0;
}
.e-bigger .e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-right: 16px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  margin-left: 60px;
  margin-right: 15px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  top: 30px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  left: 16px;
  right: initial;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  left: 0;
  margin-left: 15px;
  margin-right: 15px;
  padding: 24px;
  top: 50%;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons.e-disabled, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-msie.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-msie.e-icons {
  padding: 20px 17px 20px 26px;
}
.e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 41px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: var(--color-sf-utility-danger);
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-text-quarterary);
  display: block;
  font-size: 16px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: var(--color-sf-utility-danger);
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-bigger .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: var(--color-sf-utility-danger);
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 38px;
}
.e-bigger .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.e-bigger.e-small .e-upload {
  width: 100%;
}
.e-bigger.e-small .e-upload.e-control-wrapper {
  font-family: "Inter";
}
.e-bigger.e-small .e-upload .e-hidden-file-input {
  border: 0;
  height: 0;
  margin: 0;
  outline: none;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}
.e-bigger.e-small .e-upload .e-file-select-wrap {
  padding: 12px 0 12px 12px;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-select,
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-select .e-uploader {
  display: inline-block;
  width: 0;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-select .e-uploader {
  opacity: 0;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-file-drop {
  font-family: inherit;
  font-size: 14px;
  margin-left: 12px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: var(--color-sf-utility-danger);
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-text-quarterary);
  display: block;
  font-size: 14px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: var(--color-sf-utility-danger);
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: var(--color-sf-utility-danger);
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 42px;
}
.e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger.e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger.e-small .e-upload .e-upload-files {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list {
  font-family: inherit;
  font-size: 14px;
  height: 100%;
  line-height: 24px;
  line-height: 22px;
  min-height: 82px;
  position: relative;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container {
  display: block;
  height: 100%;
  margin-left: 12px;
  margin-right: 90px;
  min-height: 35px;
  position: relative;
  top: 0;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: left;
  font-family: inherit;
  font-size: 14px;
  max-width: 85%;
  overflow: hidden;
  padding-top: 8px;
  position: relative;
  text-overflow: ellipsis;
  top: 0;
  white-space: nowrap;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name::before {
  content: attr(data-tail);
  float: right;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-top: 8px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-hidden {
  visibility: hidden;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  display: block;
  font-size: 14px;
  padding: 2px 0;
  font-size: 12px;
  line-height: 18px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-bottom: 8px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information.e-upload-progress {
  display: none;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  display: block;
  height: 10px;
  padding-bottom: 11px;
  padding-top: 6px;
  position: absolute;
  width: 95%;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  border-radius: 4px;
  display: block;
  height: 4px;
  width: 100%;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  border-radius: 4px;
  display: inherit;
  height: 4px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: right;
  font-family: inherit;
  font-size: 14px;
  position: relative;
  right: 0;
  top: -33px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 18px;
  height: 32px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 12px;
  margin-top: -9px;
  padding: 10px;
  position: absolute;
  right: 0;
  top: 50%;
  vertical-align: middle;
  width: 32px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons.e-upload-progress, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons.e-upload-progress {
  cursor: default;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-fg-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons {
  padding: 18px 13px 18px 23px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  right: 36px;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):hover, .e-bigger.e-small .e-upload .e-upload-files .e-clear-icon-focus {
  background-color: var(--color-sf-bg-secondary-hover);
  border-color: transparent;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-bigger.e-small .e-upload .e-upload-files .e-clear-icon-focus {
  border-radius: 50%;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 18px;
  opacity: 1;
}
.e-bigger.e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 18px;
  opacity: 1;
}
.e-bigger.e-small .e-upload .e-file-select-wrap .e-btn, .e-bigger.e-small .e-upload .e-upload-actions .e-btn {
  font-family: inherit;
}
.e-bigger.e-small .e-upload .e-upload-actions {
  position: relative;
  text-align: right;
}
.e-bigger.e-small .e-upload .e-upload-actions .e-file-upload-btn {
  margin: 8px;
}
.e-bigger.e-small .e-upload .e-upload-actions .e-file-clear-btn {
  margin: 8px;
}
.e-bigger.e-small .e-upload.e-rtl .e-file-select-wrap {
  padding: 16px 12px 16px 0;
}
.e-bigger.e-small .e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 0;
}
.e-bigger.e-small .e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-actions {
  text-align: left;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  height: 100%;
  margin-left: 60px;
  margin-right: 11px;
  position: relative;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-rtl-container {
  direction: ltr;
  float: right;
  width: 100%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  float: right;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: right;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  direction: ltr;
  float: right;
  position: relative;
  text-align: right;
  width: 100%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  float: right;
  position: initial;
  top: 23px;
  width: 86%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: left;
  right: 0;
  top: -32px;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons {
  left: 0;
  margin-left: 11px;
  margin-right: 11px;
  right: auto;
  top: 50%;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons {
  left: 36px;
  right: auto;
}
.e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-bigger.e-small .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 36px;
}
.e-bigger.e-small .e-upload.e-disabled .e-file-drop {
  color: var(--color-sf-fg-disabled);
}
.e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-type, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-size, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-bigger.e-small .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-fg-disabled);
}
.e-bigger.e-small .e-upload .e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 65px;
  min-height: 65px;
}
.e-bigger.e-small .e-upload .e-bigger .e-content-placeholder.e-upload.e-placeholder-upload,
.e-bigger.e-small .e-upload .e-bigger.e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 80px;
  min-height: 80px;
}

.e-upload .e-upload-actions,
.e-bigger.e-small .e-upload .e-upload-actions {
  background-color: var(--color-sf-bg-primary-alt);
  border-radius: 0 0 6px 6px;
}