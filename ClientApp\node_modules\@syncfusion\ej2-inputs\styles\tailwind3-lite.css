



.e-signature.e-control {
  border: 1px solid;
  -webkit-box-shadow: 0 8px 10px -6px rgba(var(--color-sf-black), 0.1), 0 20px 25px -5px rgba(var(--color-sf-black), 0.1);
          box-shadow: 0 8px 10px -6px rgba(var(--color-sf-black), 0.1), 0 20px 25px -5px rgba(var(--color-sf-black), 0.1);
}

.e-signature.e-control {
  background-color: var(--color-sf-content-bg-color);
  border: 1px solid;
  border-color: var(--color-sf-dialog-border);
}

/* stylelint-disable property-no-vendor-prefix */
/* stylelint-disable */
.e-filled.e-float-input.e-error label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error label.e-float-text,
.e-filled.e-float-input.e-error input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text {
  color: var(--color-sf-danger) !important;
}

.e-float-input .e-clear-icon::before,
.e-float-input.e-control-wrapper .e-clear-icon::before {
  content: "\e7e7";
  font-family: "e-icons";
}

.e-input-group .e-clear-icon::before,
.e-input-group.e-control-wrapper .e-clear-icon::before {
  content: "\e7e7";
  font-family: "e-icons";
}

/*! input layout */
.e-input-group,
.e-input-group.e-control-wrapper {
  display: table;
  line-height: 1.4;
  margin-bottom: 0;
}

input.e-input,
.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input,
textarea.e-input,
.e-input-group textarea.e-input,
.e-input-group.e-control-wrapper textarea.e-input {
  border: 0 solid;
  border-width: 1px;
  height: auto;
  line-height: inherit;
  margin: 0;
  margin-bottom: 0;
  outline: none;
  padding: 0;
  text-indent: 8px;
  width: 100%;
}

input.e-input,
textarea.e-input,
.e-input-group,
.e-input-group.e-control-wrapper,
.e-input-group.e-disabled,
.e-input-group.e-control-wrapper.e-disabled {
  font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", arial, "Noto Sans", "Liberation Sans", sans-serif, "apple color emoji", "Segoe UI emoji", "Segoe UI Symbol", "Noto color emoji";
  font-size: 14px;
  font-weight: normal;
  -webkit-font-feature-settings: "calt" 0;
          font-feature-settings: "calt" 0;
}

.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group textarea.e-input,
.e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-small .e-input,
.e-input-group.e-small.e-control-wrapper .e-input,
.e-small .e-input-group .e-input,
.e-small .e-input-group.e-control-wrapper .e-input {
  font: inherit;
}

input.e-input,
.e-input-group input.e-input,
.e-input-group input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group.e-control-wrapper input,
.e-float-input input,
.e-float-input.e-input-group input,
.e-float-input.e-control-wrapper input,
.e-float-input.e-control-wrapper.e-input-group input,
input.e-input:focus,
.e-input-group input.e-input:focus,
.e-input-group input:focus,
.e-input-group.e-control-wrapper input.e-input:focus,
.e-input-group.e-control-wrapper input:focus,
.e-float-input input:focus,
.e-float-input.e-input-group input:focus,
.e-float-input.e-control-wrapper input:focus,
.e-float-input.e-control-wrapper.e-input-group input:focus,
.e-input-group.e-input-focus input.e-input,
.e-input-group.e-input-focus input,
.e-input-group.e-control-wrapper.e-input-focus input.e-input,
.e-input-group.e-control-wrapper.e-input-focus input,
.e-float-input.e-input-focus input,
.e-float-input.e-input-group.e-input-focus input,
.e-float-input.e-control-wrapper.e-input-focus input,
.e-float-input.e-control-wrapper.e-input-group.e-input-focus input {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input.e-input,
.e-input-group input.e-input,
.e-input-group input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group.e-control-wrapper input,
.e-float-input input,
.e-float-input.e-input-group input,
.e-float-input.e-control-wrapper input,
.e-float-input.e-control-wrapper.e-input-group input,
.e-input-group,
.e-input-group.e-control-wrapper,
.e-float-input,
.e-float-input.e-control-wrapper,
.e-input.e-corner,
.e-input-group.e-corner.e-rtl input.e-input:only-child,
.e-input-group.e-control-wrapper.e-corner.e-rtl input.e-input:only-child,
.e-input-group.e-input-focus.e-corner,
.e-input-group.e-control-wrapper.e-input-focus.e-corner,
textarea.e-input,
.e-input-group textarea.e-input,
.e-input-group textarea,
.e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-control-wrapper textarea,
.e-float-input textarea,
.e-float-input.e-input-group textarea,
.e-float-input.e-control-wrapper textarea,
.e-float-input.e-control-wrapper.e-input-group textarea {
  border-radius: 6px;
}

.e-input.e-small,
.e-input-group.e-small,
.e-input-group.e-control-wrapper.e-small,
.e-input-group.e-small .e-input,
.e-input-group.e-small input,
.e-input-group.e-control-wrapper.e-small .e-input,
.e-input-group.e-control-wrapper.e-small input,
.e-float-input.e-small input,
.e-float-input.e-input-group.e-small input,
.e-float-input.e-control-wrapper.e-small input,
.e-float-input.e-control-wrapper.e-input-group.e-small input,
.e-float-input.e-small,
.e-float-input.e-control-wrapper.e-small,
.e-small .e-input-group,
.e-small .e-input-group.e-control-wrapper,
.e-small .e-input-group .e-input,
.e-small .e-input-group input,
.e-small .e-input-group.e-control-wrapper .e-input,
.e-small .e-input-group.e-control-wrapper input,
.e-small .e-float-input input,
.e-small .e-float-input.e-input-group input,
.e-small .e-float-input.e-control-wrapper input,
.e-small .e-float-input.e-control-wrapper.e-input-group input,
.e-small .e-float-input,
.e-small .e-float-input.e-control-wrapper {
  border-radius: 4px;
}

.e-input:focus {
  border-width: 1px;
  padding-bottom: 0;
}

.e-input.e-small:focus {
  border-width: 1px;
  padding-bottom: 0;
}

.e-input-group input.e-input:focus,
.e-input-group.e-control-wrapper input.e-input:focus,
.e-input-group textarea.e-input:focus,
.e-input-group.e-control-wrapper textarea.e-input:focus,
.e-input-group.e-input-focus input.e-input,
.e-input-group.e-control-wrapper.e-input-focus input.e-input {
  padding: 0;
}

.e-input-group textarea.e-input:focus,
.e-input-group.e-control-wrapper textarea.e-input:focus {
  padding: 4px 8px 4px 8px;
}

.e-input-group .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input-group-icon {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 0 solid;
  border-width: 0;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  cursor: pointer;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  font-size: 14px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  line-height: 1;
  min-height: 30px;
  min-width: 30px;
  padding: 0;
  text-align: center;
}

/* stylelint-disable property-no-vendor-prefix */
.e-input-group .e-input-group-icon:first-child,
.e-input-group.e-control-wrapper .e-input-group-icon:first-child {
  border-left-width: 0;
}

.e-input-group .e-input-group-icon:last-child,
.e-input-group.e-control-wrapper .e-input-group-icon:last-child {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}

.e-input-group .e-input-group-icon:first-child,
.e-input-group.e-control-wrapper .e-input-group-icon:first-child {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}

.e-input-group.e-rtl .e-input-group-icon:last-child,
.e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:last-child,
.e-rtl .e-input-group .e-input-group-icon:last-child,
.e-rtl .e-input-group.e-control-wrapper .e-input-group-icon:last-child {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 0;
  border-top-left-radius: 3px;
  border-top-right-radius: 0;
}

.e-input[disabled],
.e-input-group .e-input[disabled],
.e-input-group.e-control-wrapper .e-input[disabled],
.e-input-group.e-disabled,
.e-input-group.e-disabled input,
.e-input-group.e-disabled input.e-input,
.e-input-group.e-disabled textarea,
.e-input-group.e-disabled textarea.e-input,
.e-input-group.e-control-wrapper.e-disabled,
.e-input-group.e-control-wrapper.e-disabled input,
.e-input-group.e-control-wrapper.e-disabled input.e-input,
.e-input-group.e-control-wrapper.e-disabled textarea,
.e-input-group.e-control-wrapper.e-disabled textarea.e-input,
.e-float-input.e-disabled input,
.e-float-input.e-disabled textarea,
.e-float-input input[disabled],
.e-float-input input.e-disabled,
.e-float-input textarea[disabled],
.e-float-input textarea.e-disabled,
.e-float-input.e-control-wrapper.e-disabled input,
.e-float-input.e-control-wrapper.e-disabled textarea,
.e-float-input.e-control-wrapper input[disabled],
.e-float-input.e-control-wrapper input.e-disabled,
.e-float-input.e-control-wrapper textarea[disabled],
.e-float-input.e-control-wrapper textarea.e-disabled,
.e-input-group.e-disabled span,
.e-input-group.e-control-wrapper.e-disabled span,
.e-input-group.e-disabled input.e-input:not(:valid):first-child ~ .e-clear-icon,
.e-input-group.e-control-wrapper.e-disabled input.e-input:not(:valid):first-child ~ .e-clear-icon,
.e-float-input.e-disabled input:not(:valid):first-child ~ .e-clear-icon,
.e-float-input.e-input-group.e-disabled input:not(:valid):first-child ~ .e-clear-icon,
.e-float-input.e-input-group.e-control-wrapper.e-disabled input:not(:valid):first-child ~ .e-clear-icon,
.e-float-input.e-control-wrapper.e-disabled input:not(:valid):first-child ~ .e-clear-icon,
.e-input-group.e-disabled .e-clear-icon.e-clear-icon-hide,
.e-input-group.e-control-wrapper.e-disabled .e-clear-icon.e-clear-icon-hide {
  cursor: not-allowed;
}

.e-input[disabled],
.e-input-group.e-disabled,
.e-input-group.e-control-wrapper.e-disabled,
.e-float-input input[disabled],
.e-float-input input.e-disabled,
.e-float-input.e-control-wrapper input[disabled],
.e-float-input.e-control-wrapper input.e-disabled {
  border-color: var(--color-sf-border);
  border-style: solid;
}

.e-input-group.e-disabled,
.e-input-group.e-control-wrapper.e-disabled {
  border-bottom-style: solid;
  border-width: 1px;
}

.e-input[disabled],
.e-input-group.e-disabled,
.e-input-group.e-control-wrapper.e-disabled,
.e-float-input.e-disabled,
.e-float-input input[disabled],
.e-float-input input.e-disabled,
.e-float-input.e-disabled input,
.e-float-input.e-control-wrapper.e-disabled,
.e-float-input.e-control-wrapper input[disabled],
.e-float-input.e-control-wrapper input.e-disabled,
.e-float-input.e-control-wrapper.e-disabled input,
.e-float-input textarea[disabled],
.e-float-input textarea.e-disabled,
.e-float-input.e-disabled textarea,
.e-float-input.e-control-wrapper textarea[disabled],
.e-float-input.e-control-wrapper textarea.e-disabled,
.e-float-input.e-control-wrapper.e-disabled textarea {
  filter: alpha(opacity=100);
  opacity: 1;
}

.e-input.e-rtl,
.e-input-group.e-rtl,
.e-input-group.e-control-wrapper.e-rtl,
.e-float-input.e-rtl,
.e-float-input.e-control-wrapper.e-rtl {
  direction: rtl;
}

.e-input-group,
.e-input-group.e-control-wrapper,
.e-float-custom-tag.e-input-group,
.e-float-custom-tag.e-input-group.e-control-wrapper,
.e-input-custom-tag,
.e-input-custom-tag.e-input-group,
.e-input-custom-tag.e-input-group.e-control-wrapper {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}

.e-float-input:not(.e-input-group),
.e-float-input.e-control-wrapper:not(.e-input-group),
.e-float-custom-tag,
.e-float-custom-tag.e-control-wrapper {
  display: inline-block;
}

.e-input-group .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input-group-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.e-input-group .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input-group-icon {
  white-space: nowrap;
}

.e-input-group .e-input-group-icon:not(:last-child),
.e-input-group.e-control-wrapper .e-input-group-icon:not(:last-child) {
  border-right-width: 0;
}

.e-input + .e-input-group-icon,
.e-input-group .e-input + .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input + .e-input-group-icon {
  border-left-width: 0;
}

.e-input-group.e-corner .e-input:first-child,
.e-input-group.e-corner .e-input-group-icon:first-child,
.e-input-group.e-control-wrapper.e-corner .e-input:first-child,
.e-input-group.e-control-wrapper.e-corner .e-input-group-icon:first-child {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}

.e-input-group.e-corner .e-input:last-child,
.e-input-group.e-corner .e-input-group-icon:last-child,
.e-input-group.e-control-wrapper.e-corner .e-input:last-child,
.e-input-group.e-control-wrapper.e-corner .e-input-group-icon:last-child {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}

.e-input-group.e-rtl .e-input-group-icon:first-child,
.e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:first-child {
  border-left-width: 0;
  border-right-width: 1px;
}

.e-input-group.e-rtl .e-input-group-icon + .e-input,
.e-input-group.e-control-wrapper.e-rtl .e-input-group-icon + .e-input {
  border-right-width: 0;
}

input.e-input.e-small,
textarea.e-input.e-small,
.e-small input.e-input,
.e-small textarea.e-input,
.e-input-group.e-small,
.e-small .e-input-group,
.e-input-group.e-control-wrapper.e-small,
.e-small .e-input-group.e-control-wrapper,
.e-input-group.e-small.e-disabled,
.e-small .e-input-group.e-disabled,
.e-input-group.e-control-wrapper.e-small.e-disabled,
.e-small .e-input-group.e-control-wrapper.e-disabled {
  font-size: 12px;
}

.e-input.e-small,
.e-input-group.e-small .e-input,
.e-input-group.e-control-wrapper.e-small .e-input {
  line-height: inherit;
  padding: 0;
}

.e-input-group.e-small .e-input:focus,
.e-input-group.e-control-wrapper.e-small .e-input:focus,
.e-input-group.e-small.e-input-focus .e-input,
.e-input-group.e-control-wrapper.e-small.e-input-focus .e-input {
  padding: 0;
}

.e-input-group.e-small .e-input-group-icon,
.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-input-group .e-input-group-icon,
.e-small .e-input-group.e-control-wrapper .e-input-group-icon {
  font-size: 12px;
  min-height: 24px;
  min-width: 24px;
  padding: 0;
}

label.e-float-text,
.e-float-input label.e-float-text,
.e-float-input.e-control-wrapper label.e-float-text,
.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  font-style: normal;
  font-weight: normal;
  left: 0;
  overflow: hidden;
  padding-left: 10px;
  pointer-events: none;
  position: absolute;
  text-overflow: ellipsis;
  top: -11px;
  -webkit-transform: translate3d(0, 16px, 0) scale(1);
          transform: translate3d(0, 16px, 0) scale(1);
  -webkit-transform-origin: left top;
          transform-origin: left top;
  -webkit-transition: 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 100%;
}

label.e-float-text,
.e-float-input label.e-float-text,
.e-float-input.e-control-wrapper label.e-float-text,
.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  top: -11px;
}

label.e-float-text,
.e-float-input label.e-float-text,
.e-float-input.e-control-wrapper label.e-float-text,
.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  left: 0%;
  top: 50%;
  transform: translate(0%, -50%);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
  font-style: normal;
}

.e-float-input.e-small label.e-float-text,
.e-float-input.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
}

.e-float-input input:focus ~ label.e-float-text,
.e-float-input input:valid ~ label.e-float-text,
.e-float-input input ~ label.e-label-top.e-float-text,
.e-float-input input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input input label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper input label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-input-focus input ~ label.e-float-text,
.e-float-input.e-input-focus input ~ label.e-float-text {
  font-size: 12px;
  font-weight: 500;
  padding-right: 0;
  -webkit-transform: translate3d(-10px, -35px, 0) scale(1);
          transform: translate3d(-10px, -35px, 0) scale(1);
  padding-left: 0;
  left: 12px;
  top: 40%;
}

.e-float-input.e-small input:focus ~ label.e-float-text,
.e-float-input.e-small input:valid ~ label.e-float-text,
.e-float-input.e-small input ~ label.e-label-top.e-float-text,
.e-float-input.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-small input label.e-float-text.e-label-top,
.e-small .e-float-input input:focus ~ label.e-float-text,
.e-small .e-float-input input:valid ~ label.e-float-text,
.e-small .e-float-input input ~ label.e-label-top.e-float-text,
.e-small .e-float-input input[readonly] ~ label.e-label-top.e-float-text,
.e-small .e-float-input input[disabled] ~ label.e-label-top.e-float-text,
.e-small .e-float-input input label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small input label.e-float-text.e-label-top,
.e-small .e-float-input.e-control-wrapper input:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper input:valid ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper input[readonly] ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper input label.e-float-text.e-label-top {
  font-size: 12px;
  font-weight: 500;
  padding-right: 0;
  -webkit-transform: translate3d(-10px, -34px, 0) scale(1);
          transform: translate3d(-10px, -34px, 0) scale(1);
}

.e-float-input.e-small input:focus ~ label.e-float-text,
.e-float-input.e-small input:valid ~ label.e-float-text,
.e-float-input.e-small input ~ label.e-label-top.e-float-text,
.e-small .e-float-input input ~ label.e-label-top.e-float-text,
.e-float-input.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-small.e-input-focus input-group-animation ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small.e-input-focus input ~ label.e-float-text {
  font-size: 10px;
  padding-right: 0;
  top: 17px;
  padding-left: 0;
  left: 12px;
}

.e-float-input.e-small .e-input-in-wrap input:focus ~ label.e-float-text,
.e-float-input.e-small .e-input-in-wrap input:valid ~ label.e-float-text,
.e-float-input.e-small .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-small .e-float-input .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-float-input.e-small .e-input-in-wrap input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-small .e-input-in-wrap input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input[disabled] ~ label.e-label-top.e-float-text {
  top: -7px;
}

.e-float-input,
.e-float-input.e-control-wrapper {
  line-height: 1.4;
  margin-bottom: 0;
  margin-top: 24px;
  padding-top: 0;
  position: relative;
  width: 100%;
}

.e-float-input.e-small,
.e-float-input.e-control-wrapper.e-small,
.e-small .e-float-input.e-control-wrapper {
  line-height: 1.35;
  margin-bottom: 0;
  margin-top: 20px;
  padding-top: 0;
}

.e-input-group.e-small,
.e-input-group.e-control-wrapper.e-small,
.e-small .e-input-group,
.e-small .e-input-group.e-control-wrapper {
  line-height: normal;
}

.e-float-input.e-no-float-label,
.e-float-input.e-small.e-no-float-label,
.e-small .e-float-input.e-no-float-label,
.e-float-input.e-control-wrapper.e-no-float-label,
.e-float-input.e-control-wrapper.e-small.e-no-float-label,
.e-small .e-float-input.e-control-wrapper.e-no-float-label {
  margin-top: 0;
}

.e-float-input,
.e-float-input.e-control-wrapper,
.e-float-input.e-disabled,
.e-float-input.e-control-wrapper.e-disabled,
.e-float-input.e-input-group.e-disabled,
.e-float-input.e-input-group.e-control-wrapper.e-disabled {
  font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", arial, "Noto Sans", "Liberation Sans", sans-serif, "apple color emoji", "Segoe UI emoji", "Segoe UI Symbol", "Noto color emoji";
  font-size: 14px;
  font-weight: normal;
}

.e-float-input input,
.e-float-input textarea,
.e-float-input.e-control-wrapper input,
.e-float-input.e-control-wrapper textarea {
  border: 0 solid;
  border-width: 1px;
  display: block;
  font: inherit;
  width: 100%;
}

.e-float-input input,
.e-float-input.e-control-wrapper input {
  min-width: 0;
  padding: 0;
}

.e-float-input input,
.e-input-group input,
.e-float-input.e-control-wrapper input,
.e-input-group.e-control-wrapper input,
.e-float-input textarea,
.e-input-group textarea,
.e-float-input.e-control-wrapper textarea,
.e-input-group.e-control-wrapper textarea {
  text-indent: 8px;
}

.e-float-input.e-small.e-disabled,
.e-small .e-float-input.e-disabled,
.e-float-input.e-control-wrapper.e-small.e-disabled,
.e-small .e-float-input.e-control-wrapper.e-disabled,
.e-float-input.e-input-group.e-small.e-disabled,
.e-small .e-float-input.e-input-group.e-disabled,
.e-float-input.e-input-group.e-control-wrapper.e-small.e-disabled,
.e-small .e-float-input.e-input-group.e-control-wrapper.e-disabled,
.e-float-input.e-small,
.e-small .e-float-input,
.e-float-input.e-control-wrapper.e-small,
.e-small .e-float-input.e-control-wrapper {
  font-size: 12px;
}

.e-float-input.e-small input,
.e-float-input.e-control-wrapper.e-small input {
  font: inherit;
  line-height: inherit;
  padding: 0;
}

.e-float-input input:focus,
.e-float-input.e-control-wrapper input:focus,
.e-float-input textarea:focus,
.e-float-input.e-control-wrapper textarea:focus,
.e-float-input.e-input-focus input,
.e-float-input.e-control-wrapper.e-input-focus input,
.e-input-group.e-control-container.valid.modified,
.e-input-group.e-control-container.invalid,
.e-float-input.e-control-container.valid.modified,
.e-float-input.e-control-container.invalid {
  outline: none;
}

label.e-float-text,
.e-float-input label.e-float-text,
.e-float-input.e-control-wrapper label.e-float-text {
  font-family: inherit;
}

.e-float-input input:valid ~ label.e-float-text,
.e-float-input input:focus ~ label.e-float-text,
.e-float-input input:valid ~ label.e-float-text.e-label-top,
.e-float-input input ~ label.e-float-text.e-label-top,
.e-float-input .e-input-in-wrap input:valid ~ label.e-float-text,
.e-float-input .e-input-in-wrap input:valid ~ label.e-float-text.e-label-top,
.e-float-input .e-input-in-wrap input ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper input:valid ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper input ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper .e-input-in-wrap input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper .e-input-in-wrap input:valid ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper .e-input-in-wrap input ~ label.e-float-text.e-label-top,
.e-float-input.e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-input-focus input ~ label.e-float-text,
.e-float-input textarea:valid ~ label.e-float-text,
.e-float-input textarea:focus ~ label.e-float-text,
.e-float-input textarea:valid ~ label.e-float-text.e-label-top,
.e-float-input textarea ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper textarea ~ label.e-float-text.e-label-top {
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

label.e-float-text,
.e-float-input label.e-float-text,
.e-float-input.e-control-wrapper label.e-float-text,
.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-small:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-weight: normal;
}

.e-float-input:not(.e-input-group) .e-float-line::before,
.e-float-input:not(.e-input-group) .e-float-line::after,
.e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::before,
.e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::after {
  bottom: 0;
  content: "";
  height: 2px;
  position: absolute;
  -webkit-transition: 0.2s ease;
  transition: 0.2s ease;
  width: 0;
}

.e-float-input:not(.e-input-group) .e-float-line::before,
.e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::before {
  left: 50%;
}

.e-float-input:not(.e-input-group) .e-float-line::after,
.e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::after {
  right: 50%;
}

.e-float-input:not(.e-input-group) input:focus ~ .e-float-line::before,
.e-float-input:not(.e-input-group) textarea:focus ~ .e-float-line::before,
.e-float-input:not(.e-input-group) input:focus ~ .e-float-line::after,
.e-float-input:not(.e-input-group) textarea:focus ~ .e-float-line::after,
.e-float-input.e-control-wrapper:not(.e-input-group) input:focus ~ .e-float-line::before,
.e-float-input.e-control-wrapper:not(.e-input-group) textarea:focus ~ .e-float-line::before,
.e-float-input.e-control-wrapper:not(.e-input-group) input:focus ~ .e-float-line::after,
.e-float-input.e-control-wrapper:not(.e-input-group) textarea:focus ~ .e-float-line::after,
.e-float-input:not(.e-input-group).e-input-focus input ~ .e-float-line::before,
.e-float-input:not(.e-input-group).e-input-focus input ~ .e-float-line::after,
.e-float-input.e-control-wrapper:not(.e-input-group).e-input-focus input ~ .e-float-line::before,
.e-float-input.e-control-wrapper:not(.e-input-group).e-input-focus input ~ .e-float-line::after {
  width: 50%;
}

.e-float-input .e-float-line,
.e-float-input.e-control-wrapper .e-float-line {
  display: block;
  position: relative;
  width: 100%;
}

.e-float-input.e-rtl label.e-float-text,
.e-float-input.e-control-wrapper.e-rtl label.e-float-text,
.e-rtl .e-float-input label.e-float-text,
.e-rtl .e-float-input.e-control-wrapper label.e-float-text,
.e-rtl label.e-float-text,
.e-rtl .e-float-input.e-control-wrapper label.e-float-text,
.e-rtl.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  right: 0;
  -webkit-transform-origin: right top;
          transform-origin: right top;
}

.e-float-input.e-rtl:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-float-input.e-rtl.e-control-wrapper:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-control-wrapper:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-float-input.e-rtl input:not(:focus):not(:valid) label.e-float-text,
.e-float-input.e-rtl input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input input:not(:focus):not(:valid) label.e-float-text,
.e-rtl .e-float-input input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-rtl input:not(:focus):not(:valid) label.e-float-text,
.e-rtl .e-float-input.e-control-wrapper input:not(:focus):not(:valid) label.e-float-text,
.e-float-input.e-rtl.e-control-wrapper input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-control-wrapper input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-float-input.e-rtl.e-small:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-float-input.e-rtl.e-control-wrapper.e-small:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-small:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-control-wrapper.e-small:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-rtl:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-rtl.e-control-wrapper:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-small.e-rtl .e-float-input:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-small.e-rtl .e-float-input.e-control-wrapper:not(.e-input-focus) label.e-float-text.e-label-bottom,
.e-float-input.e-small.e-rtl input:not(:focus):not(:valid) label.e-float-text,
.e-float-input.e-rtl.e-small input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-small input:not(:focus):not(:valid) label.e-float-text,
.e-rtl .e-float-input.e-small input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-rtl input:not(:focus):not(:valid) label.e-float-text,
.e-small .e-float-input.e-rtl input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-small.e-rtl .e-float-input input:not(:focus):not(:valid) label.e-float-text,
.e-small.e-rtl .e-float-input input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small.e-rtl input:not(:focus):not(:valid) label.e-float-text,
.e-float-input.e-control-wrapper.e-rtl.e-small input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-rtl .e-float-input.e-control-wrapper.e-small input:not(:focus):not(:valid) label.e-float-text,
.e-rtl .e-float-input.e-control-wrapper.e-small input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper.e-rtl input:not(:focus):not(:valid) label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-rtl input:not(:focus):not(:valid) label.e-float-text.e-label-bottom,
.e-small.e-rtl .e-float-input.e-control-wrapper input:not(:focus):not(:valid) label.e-float-text,
.e-small.e-rtl .e-float-input.e-control-wrapper input:not(:focus):not(:valid) label.e-float-text.e-label-bottom {
  padding-right: 10px;
}

.e-input-group.e-corner.e-rtl .e-input:first-child,
.e-input-group.e-corner.e-rtl .e-input-group-icon:first-child,
.e-input-group.e-control-wrapper.e-corner.e-rtl .e-input:first-child,
.e-input-group.e-control-wrapper.e-corner.e-rtl .e-input-group-icon:first-child {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 6px;
  border-top-left-radius: 0;
  border-top-right-radius: 6px;
}

.e-input-group.e-corner.e-rtl .e-input:last-child,
.e-input-group.e-corner.e-rtl .e-input-group-icon:last-child,
.e-input-group.e-control-wrapper.e-corner.e-rtl .e-input:last-child,
.e-input-group.e-control-wrapper.e-corner.e-rtl .e-input-group-icon:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 0;
  border-top-left-radius: 6px;
  border-top-right-radius: 0;
}

.e-input-group.e-warning::before,
.e-input-group.e-control-wrapper.e-warning::before {
  content: "";
}

.e-float-input input[disabled],
.e-float-input input.e-disabled,
.e-float-input.e-control-wrapper input[disabled],
.e-float-input.e-control-wrapper input.e-disabled {
  background: transparent;
  background-image: none;
  cursor: not-allowed;
}

.e-input-group.e-rtl .e-input:not(:first-child):focus,
.e-input-group.e-control-wrapper.e-rtl .e-input:not(:first-child):focus {
  border-right-width: 0;
}

.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input {
  min-width: 0;
  width: 100%;
}

.e-input-group input.e-input,
.e-input-group textarea.e-input,
.e-input-group input.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]):not(:focus),
.e-input-group textarea.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]),
.e-input-group.e-control-wrapper input.e-input,
.e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-control-wrapper input.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]):not(:focus),
.e-input-group.e-control-wrapper textarea.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]) {
  border: 0 solid;
  border-width: 0;
}

.e-input-group input.e-input,
.e-input-group textarea.e-input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group.e-control-wrapper textarea.e-input,
.e-float-input input.e-input,
.e-float-input.e-control-wrapper input.e-input {
  margin-bottom: 0;
}

.e-input-group::before,
.e-input-group::after,
.e-input-group.e-control-wrapper::before,
.e-input-group.e-control-wrapper::after {
  content: "";
}

.e-input-group::before,
.e-input-group.e-control-wrapper::before {
  content: "";
}

.e-input-group.e-input-focus::before,
.e-input-group.e-input-focus::after,
.e-input-group.e-control-wrapper.e-input-focus::before,
.e-input-group.e-control-wrapper.e-input-focus::after {
  content: "";
}

.e-input-group::after,
.e-input-group.e-control-wrapper::after {
  content: "";
}

.e-input-group,
.e-input-group.e-control-wrapper {
  position: relative;
  width: 100%;
}

.e-input-group .e-input-group-icon:hover,
.e-input-group.e-rtl.e-corner .e-input-group-icon:hover,
.e-input-group.e-control-wrapper .e-input-group-icon:hover,
.e-input-group.e-control-wrapper.e-rtl.e-corner .e-input-group-icon:hover {
  border-radius: 0;
}

.e-input.e-small,
.e-input-group.e-small,
.e-input-group.e-control-wrapper.e-small {
  margin-bottom: 0;
}

.e-input-group .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input-group-icon {
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.e-float-input.e-input-group .e-input-group-icon,
.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  margin-top: 0;
}

.e-input-group.e-small .e-input-group-icon,
.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-input-group .e-input-group-icon,
.e-small .e-input-group.e-control-wrapper .e-input-group-icon {
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small .e-float-input.e-input-group .e-input-group-icon,
.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small .e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  margin-top: 0;
}

.e-input-group .e-input-group-icon:last-child,
.e-input-group.e-control-wrapper .e-input-group-icon:last-child,
.e-input-group.e-small .e-input-group-icon:last-child,
.e-input-group.e-control-wrapper.e-small .e-input-group-icon:last-child {
  margin-right: 0;
}

.e-input-group,
.e-input-group.e-control-wrapper {
  border-bottom: 1px solid;
}

.e-input-group:not(.e-float-icon-left),
.e-input-group.e-control-wrapper:not(.e-float-icon-left),
.e-filled.e-input-group.e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left {
  border-bottom: 1px solid;
}

.e-underline.e-input-group:not(.e-float-icon-left),
.e-underline.e-input-group.e-success:not(.e-float-icon-left),
.e-underline.e-input-group.e-warning:not(.e-float-icon-left),
.e-underline.e-input-group.e-error:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper.e-success:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper.e-warning:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper.e-error:not(.e-float-icon-left) {
  border: 1px solid;
  border-width: 1px;
  padding-top: 1px;
  border-radius: 0;
}

.e-input-group,
.e-input-group.e-success,
.e-input-group.e-warning,
.e-input-group.e-error,
.e-input-group.e-control-wrapper,
.e-input-group.e-control-wrapper.e-success,
.e-input-group.e-control-wrapper.e-warning,
.e-input-group.e-control-wrapper.e-error {
  border: 1px solid;
  border-width: 1px;
}

.e-input-group.e-rtl.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:not(:first-child):focus,
.e-input-group.e-control-wrapper.e-rtl.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:not(:first-child):focus {
  border-right-width: 0;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled,
.e-input-group.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error).e-disabled .e-input-in-wrap,
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error).e-disabled .e-input-in-wrap {
  background: var(--color-sf-content-bg-color-alt1);
  color: var(--color-sf-content-text-color-alt1);
  border-color: var(--color-sf-border);
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error).e-disabled,
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error).e-disabled {
  border-style: solid;
}

.e-input-group .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input-group-icon {
  content: "";
}

.e-input-group:not(.e-filled) .e-input-group-icon::after,
.e-input-group.e-control-wrapper:not(.e-filled) .e-input-group-icon::after {
  content: "";
}

.e-input-group .e-input-group-icon.e-input-btn-ripple::after,
.e-input-group.e-control-wrapper .e-input-group-icon.e-input-btn-ripple::after {
  content: "";
}

input.e-input::-ms-clear,
.e-float-input input::-ms-clear,
.e-float-input.e-control-wrapper input::-ms-clear,
.e-input:not(:valid):not(.e-control):first-child ~ .e-clear-icon,
.e-input-group input:not(.e-control).e-input:not(:valid):first-child ~ .e-clear-icon,
.e-input-group.e-control-wrapper input.e-input:not(:valid):not(.e-control):first-child ~ .e-clear-icon,
.e-float-input input:not(:valid):not(.e-control):first-child ~ .e-clear-icon,
.e-float-input.e-control-wrapper input:not(:valid):not(.e-control):first-child ~ .e-clear-icon,
.e-float-input.e-input-group input:not(:valid):not(.e-control):first-child ~ .e-clear-icon,
.e-float-input.e-input-group.e-control-wrapper input:not(:valid):not(.e-control):first-child ~ .e-clear-icon {
  display: none;
}

.e-input-group .e-clear-icon.e-clear-icon-hide,
.e-input-group.e-control-wrapper .e-clear-icon.e-clear-icon-hide,
.e-float-input.e-control-wrapper.e-hidden,
.e-input-group.e-control-wrapper.e-hidden {
  display: none;
}

input.e-input[type=search]::-webkit-search-decoration,
input.e-input[type=search]::-webkit-search-cancel-button,
input.e-input[type=search]::-webkit-search-results-button,
input.e-input[type=search]::-webkit-search-results-decoration,
.e-float-input input[type=search]::-webkit-search-decoration,
.e-float-input input[type=search]::-webkit-search-cancel-button,
.e-float-input input[type=search]::-webkit-search-results-button,
.e-float-input input[type=search]::-webkit-search-results-decoration,
.e-float-input.e-control-wrapper input[type=search]::-webkit-search-decoration,
.e-float-input.e-control-wrapper input[type=search]::-webkit-search-cancel-button,
.e-float-input.e-control-wrapper input[type=search]::-webkit-search-results-button,
.e-float-input.e-control-wrapper input[type=search]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

.e-float-input.e-input-group .e-float-line,
.e-float-input.e-input-group.e-control-wrapper .e-float-line,
.e-float-input.e-control-wrapper.e-input-group .e-float-line,
.e-float-input.e-control-wrapper.e-input-group.e-control-wrapper .e-float-line {
  bottom: -1px;
  position: absolute;
}

.e-float-input.e-input-group input,
.e-float-input.e-input-group textarea,
.e-float-input.e-input-group.e-control-wrapper input,
.e-float-input.e-input-group.e-control-wrapper textarea {
  border: 0;
}

.e-float-input.e-input-group input:focus,
.e-input-group input:focus,
.e-float-input.e-input-group textarea:focus,
.e-float-input.e-input-group.e-control-wrapper input:focus,
.e-float-input.e-input-group.e-control-wrapper textarea {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.e-float-input.e-input-group .e-float-line,
.e-float-input.e-input-group .e-float-text,
.e-float-input.e-input-group.e-control-wrapper .e-float-line,
.e-float-input.e-input-group.e-control-wrapper .e-float-text {
  right: 0;
}

input.e-input::-webkit-input-placeholder,
input.e-input:-moz-placeholder,
input.e-input:-ms-input-placeholder,
input.e-input::-moz-placeholder,
textarea.e-input::-webkit-input-placeholder,
textarea.e-input:-moz-placeholder,
textarea.e-input:-ms-input-placeholder,
textarea.e-input::-moz-placeholder,
textarea.e-input::-webkit-textarea-placeholder,
textarea.e-input:-moz-placeholder,
textarea.e-input:-ms-textarea-placeholder,
textarea.e-input::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

.e-small input.e-input::-webkit-input-placeholder,
input.e-small.e-input::-webkit-input-placeholder,
.e-small input.e-input:-moz-placeholder,
input.e-small.e-input:-moz-placeholder,
.e-small input.e-input:-ms-input-placeholder,
input.e-small.e-input:-ms-input-placeholder,
.e-small input.e-input::-moz-placeholder,
input.e-small.e-input::-moz-placeholder,
.e-small textarea.e-input::-webkit-input-placeholder,
textarea.e-small.e-input::-webkit-input-placeholder,
.e-small textarea.e-input:-moz-placeholder,
textarea.e-small.e-input:-moz-placeholder,
.e-small textarea.e-input:-ms-input-placeholder,
textarea.e-small.e-input:-ms-input-placeholder,
.e-small textarea.e-input::-moz-placeholder,
textarea.e-small.e-input::-moz-placeholder,
.e-small textarea.e-input::-webkit-textarea-placeholder,
textarea.e-small.e-input::-webkit-textarea-placeholder,
.e-small textarea.e-input:-moz-placeholder,
textarea.e-small.e-input:-moz-placeholder,
.e-small textarea.e-input:-ms-input-placeholder,
textarea.e-small.e-input:-ms-input-placeholder,
.e-small textarea.e-input::-moz-placeholder,
textarea.e-small.e-input::-moz-placeholder {
  font-size: 12px;
  font-style: normal;
}

input.e-input:-moz-placeholder,
textarea.e-input:-moz-placeholder,
.e-input-group input.e-input:-moz-placeholder,
.e-input-group textarea.e-input:-moz-placeholder,
.e-input-group.e-control-wrapper input.e-input:-moz-placeholder,
.e-input-group.e-control-wrapper textarea.e-input:-moz-placeholder,
input.e-input:-moz-placeholder,
.e-input-group input.e-input:-moz-placeholder,
.e-input-group.e-control-wrapper input.e-input:-moz-placeholder,
textarea.e-input:-moz-placeholder,
input.e-input::-moz-placeholder,
textarea.e-input::-moz-placeholder,
input.e-input::-webkit-input-placeholder,
textarea.e-input::-webkit-input-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

input.e-input:-ms-input-placeholder,
textarea.e-input:-ms-input-placeholder {
  font-style: normal;
}

input.e-input,
.e-input-group input,
.e-input-group.e-control-wrapper input,
.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 30px;
}

.e-float-input:not(.e-input-group) input,
.e-float-input.e-control-wrapper:not(.e-input-group) input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 32px;
}

input.e-input.e-small,
.e-input-group.e-small input,
.e-input-group.e-small input.e-input,
.e-small .e-input-group input,
.e-small .e-input-group input.e-input,
.e-input-group.e-control-wrapper.e-small input,
.e-input-group.e-control-wrapper.e-small input.e-input,
.e-small .e-input-group.e-control-wrapper input,
.e-small .e-input-group.e-control-wrapper input.e-input,
.e-float-input.e-small input,
.e-float-input.e-small input.e-input,
.e-small .e-float-input input,
.e-small .e-float-input input.e-input,
.e-float-input.e-control-wrapper.e-small input,
.e-float-input.e-control-wrapper.e-small input.e-input,
.e-small .e-float-input.e-control-wrapper input,
.e-small .e-float-input.e-control-wrapper input.e-input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 22px;
}

.e-float-input.e-small:not(.e-input-group) input,
.e-float-input.e-small:not(.e-input-group) input.e-input,
.e-small .e-float-input:not(.e-input-group) input,
.e-small .e-float-input:not(.e-input-group) input.e-input .e-float-input.e-control-wrapper.e-small:not(.e-input-group) input,
.e-float-input.e-control-wrapper.e-small:not(.e-input-group) input.e-input,
.e-small .e-float-input.e-control-wrapper:not(.e-input-group) input,
.e-small .e-float-input.e-control-wrapper:not(.e-input-group) input.e-input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 22px;
}

textarea.e-input,
.e-input-group textarea,
.e-input-group.e-control-wrapper textarea,
.e-float-input textarea,
.e-float-input.e-control-wrapper textarea {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: auto;
}

textarea.e-input.e-small,
.e-input-group.e-small textarea,
.e-input-group.e-small textarea.e-input,
.e-small .e-input-group textarea,
.e-small .e-input-group textarea.e-input,
.e-input-group.e-control-wrapper.e-small textarea,
.e-input-group.e-control-wrapper.e-small textarea.e-input,
.e-small .e-input-group.e-control-wrapper textarea,
.e-small .e-input-group.e-control-wrapper textarea.e-input,
.e-float-input.e-small textarea,
.e-float-input.e-small textarea.e-input,
.e-small .e-float-input textarea,
.e-small .e-float-input textarea.e-input,
.e-float-input.e-control-wrapper.e-small textarea,
.e-float-input.e-control-wrapper.e-small textarea.e-input,
.e-small .e-float-input.e-control-wrapper textarea,
.e-small .e-float-input.e-control-wrapper textarea.e-input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: auto;
}

input.e-input.e-small,
.e-input-group input.e-input.e-small,
.e-input-group.e-control-wrapper input.e-input.e-small,
.e-input-group.e-small .e-input,
.e-input-group.e-control-wrapper.e-small .e-input,
.e-small input.e-input,
.e-small .e-input-group .e-input,
.e-small .e-input-group.e-control-wrapper .e-input,
.e-float-input.e-small input,
.e-float-input input.e-small,
.e-small .e-float-input input,
.e-float-input.e-control-wrapper.e-small input,
.e-float-input.e-control-wrapper input.e-small,
.e-small .e-float-input.e-control-wrapper input,
textarea.e-input.e-small,
.e-input-group textarea.e-input.e-small,
.e-input-group.e-control-wrapper input.e-input-group textarea.e-input.e-small,
.e-small input.e-input,
.e-float-input.e-small textarea,
.e-float-input textarea.e-small,
.e-small .e-float-input textarea,
.e-float-input.e-control-wrapper.e-small textarea,
.e-float-input.e-control-wrapper textarea.e-small,
.e-small .e-float-input.e-control-wrapper textarea {
  text-indent: 8px;
}

input.e-input,
.e-input-group input.e-input,
.e-input-group input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group.e-control-wrapper input,
.e-float-input input.e-input,
.e-float-input input,
.e-float-input.e-control-wrapper input.e-input,
.e-float-input.e-control-wrapper input,
.e-input-group input.e-input:focus,
.e-input-group.e-control-wrapper input.e-input:focus,
.e-float-input.e-control-wrapper input:focus,
.e-float-input input:focus,
.e-input-group.e-input-focus input.e-input,
.e-input-group.e-control-wrapper.e-input-focus input.e-input,
.e-float-input.e-control-wrapper.e-input-focus input,
.e-float-input.e-input-focus input {
  padding-left: 8px;
  text-indent: 0;
}

textarea.e-input,
.e-input-group textarea.e-input,
.e-input-group textarea,
.e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-control-wrapper textarea,
.e-float-input textarea.e-input,
.e-float-input textarea,
.e-float-input.e-control-wrapper textarea.e-input,
.e-float-input.e-control-wrapper textarea,
.e-input-group textarea.e-input:focus,
.e-input-group.e-control-wrapper textarea.e-input:focus,
.e-float-input.e-control-wrapper textarea:focus,
.e-float-input textarea:focus {
  padding-left: 8px;
  text-indent: 0;
}

input.e-input.e-rtl,
.e-input-group.e-rtl input.e-input,
.e-input-group.e-control-container.e-rtl input.e-control,
.e-input-group.e-control-wrapper.e-rtl input.e-input,
.e-float-input.e-rtl input,
.e-float-input.e-control-wrapper.e-rtl input,
.e-rtl .e-input-group input.e-input,
.e-rtl .e-input-group.e-control-wrapper input.e-input,
.e-rtl .e-float-input input,
.e-rtl .e-float-input.e-control-wrapper input,
.e-input-group.e-rtl input.e-input,
.e-input-group.e-control-wrapper.e-rtl input.e-input,
.e-float-input.e-rtl input,
.e-float-input.e-control-wrapper.e-rtl input,
.e-rtl .e-input-group input.e-input,
.e-rtl .e-input-group.e-control-wrapper input.e-input,
.e-rtl .e-float-input input,
.e-rtl .e-float-input.e-control-wrapper input,
.e-input-group.e-rtl input.e-input:focus,
.e-input-group.e-control-wrapper.e-rtl input.e-input:focus,
.e-float-input.e-rtl input:focus,
.e-float-input.e-control-wrapper.e-rtl input:focus,
.e-rtl .e-input-group input.e-input:focus,
.e-rtl .e-input-group.e-control-wrapper input.e-input:focus,
.e-rtl .e-float-input input:focus,
.e-rtl .e-float-input.e-control-wrapper input:focus,
.e-input-group.e-rtl.e-input-focus input.e-input,
.e-input-group.e-control-wrapper.e-rtl.e-input-focus input.e-input,
.e-rtl .e-input-group.e-input-focus input.e-input,
.e-rtl .e-input-group.e-control-wrapper.e-input-focus input.e-input,
.e-float-input.e-rtl.e-input-focus input,
.e-float-input.e-control-wrapper.e-rtl.e-input-focus input,
.e-rtl .e-float-input.e-input-focus input,
.e-rtl .e-float-input.e-control-wrapper.e-input-focus input {
  padding-left: 0;
  padding-right: 8px;
  text-indent: 0;
}

textarea.e-input.e-rtl,
.e-input-group:not(.e-outline).e-rtl textarea.e-input,
.e-input-group:not(.e-outline).e-control-wrapper.e-rtl textarea.e-input,
.e-float-input:not(.e-outline).e-rtl textarea,
.e-float-input:not(.e-outline).e-control-wrapper.e-rtl textarea,
.e-rtl .e-input-group:not(.e-outline) textarea.e-input,
.e-rtl .e-input-group:not(.e-outline).e-control-wrapper textarea.e-input,
.e-rtl .e-float-input:not(.e-outline) textarea,
.e-rtl .e-float-input:not(.e-outline).e-control-wrapper textarea,
.e-input-group:not(.e-outline).e-rtl textarea.e-input,
.e-input-group:not(.e-outline).e-control-wrapper.e-rtl textarea.e-input,
.e-float-input:not(.e-outline).e-rtl textarea,
.e-float-input:not(.e-outline).e-control-wrapper.e-rtl textarea,
.e-rtl .e-input-group:not(.e-outline) textarea.e-input,
.e-rtl .e-input-group:not(.e-outline).e-control-wrapper textarea.e-input,
.e-rtl .e-float-input:not(.e-outline) textarea,
.e-rtl .e-float-input:not(.e-outline).e-control-wrapper textarea,
.e-input-group:not(.e-outline).e-rtl textarea.e-input:focus,
.e-input-group:not(.e-outline).e-control-wrapper.e-rtl textarea.e-input:focus,
.e-float-input:not(.e-outline).e-rtl textarea:focus,
.e-float-input:not(.e-outline).e-control-wrapper.e-rtl textarea:focus,
.e-rtl .e-input-group:not(.e-outline) textarea.e-input:focus,
.e-rtl .e-input-group:not(.e-outline).e-control-wrapper textarea.e-input:focus,
.e-rtl .e-float-input:not(.e-outline) textarea:focus,
.e-rtl .e-float-input:not(.e-outline).e-control-wrapper textarea:focus {
  padding-right: 8px;
  text-indent: 0;
}

input.e-input.e-small,
.e-small input.e-input,
.e-input-group.e-small input.e-input,
.e-input-group.e-control-wrapper.e-small input.e-input,
.e-float-input.e-small input,
.e-float-input.e-control-wrapper input.e-small,
.e-float-input.e-small input,
.e-float-input.e-control-wrapper input.e-small,
.e-input-group input.e-input.e-small,
.e-input-group.e-control-wrapper input.e-input.e-small,
.e-small .e-float-input input,
.e-small .e-float-input.e-control-wrapper input,
.e-small .e-input-group input.e-input,
.e-small .e-input-group.e-control-wrapper input.e-input,
.e-input-group.e-small input.e-input:focus,
.e-input-group.e-control-wrapper.e-small input.e-input:focus,
.e-float-input.e-small input:focus,
.e-float-input.e-control-wrapper.e-small input:focus,
.e-small .e-input-group.e-control-wrapper input.e-input:focus,
.e-small .e-input-group input.e-input:focus,
.e-small .e-float-input input:focus,
.e-small .e-float-input.e-control-wrapper input:focus,
.e-input-group.e-small.e-input-focus input.e-input,
.e-input-group.e-control-wrapper.e-small.e-input-focus input.e-input,
.e-small .e-input-group.e-control-wrapper.e-input-focus input.e-input,
.e-small .e-input-group.e-input-focus input.e-input,
.e-float-input.e-small.e-input-focus input,
.e-float-input.e-control-wrapper.e-input-focus.e-small input,
.e-small .e-float-input.e-input-focus input,
.e-small .e-float-input.e-control-wrapper.e-input-focus input,
textarea.e-input.e-small,
.e-small textarea.e-input,
.e-input-group.e-small textarea.e-input,
.e-input-group.e-control-wrapper.e-small textarea.e-input,
.e-float-input.e-control-wrapper.e-small textarea,
.e-float-input.e-control-wrapper textarea.e-small,
.e-float-input.e-small textarea,
.e-float-input textarea.e-small,
.e-input-group textarea.e-input.e-small,
.e-input-group.e-control-wrapper textarea.e-input.e-small,
.e-small .e-float-input.e-control-wrapper textarea,
.e-small .e-float-input textarea,
.e-small .e-input-group textarea.e-input,
.e-small .e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-small textarea.e-input:focus,
.e-input-group.e-control-wrapper.e-small textarea.e-input:focus,
.e-float-input.e-small textarea:focus,
.e-float-input.e-control-wrapper.e-small textarea:focus,
.e-small .e-input-group textarea.e-input:focus,
.e-small .e-input-group.e-control-wrapper textarea.e-input:focus,
.e-small .e-float-input.e-control-wrapper textarea:focus,
.e-small .e-float-input textarea:focus {
  padding-left: 8px;
  text-indent: 0;
}

.e-rtl input.e-input.e-small,
input.e-input.e-small.e-rtl,
.e-small.e-rtl input.e-input,
.e-small input.e-input.e-rtl,
.e-float-input.e-control-wrapper.e-small.e-rtl input,
.e-float-input.e-small.e-rtl input,
.e-input-group.e-small.e-rtl input.e-input,
.e-input-group.e-control-wrapper.e-small.e-rtl input.e-input,
.e-rtl .e-float-input.e-small input,
.e-rtl .e-float-input.e-control-wrapper.e-small input,
.e-rtl .e-input-group.e-small input.e-input,
.e-rtl .e-input-group.e-control-wrapper.e-small input.e-input,
.e-float-input.e-rtl input.e-small,
.e-float-input.e-control-wrapper.e-rtl input.e-small,
.e-input-group.e-rtl input.e-input.e-small,
.e-input-group.e-control-wrapper.e-rtl input.e-input.e-small,
.e-rtl .e-float-input input.e-small,
.e-rtl .e-float-input.e-control-wrapper input.e-small,
.e-rtl .e-input-group input.e-input.e-small,
.e-rtl .e-input-group.e-control-wrapper input.e-input.e-small,
.e-small .e-float-input.e-rtl input,
.e-small .e-float-input.e-control-wrapper.e-rtl input,
.e-small .e-input-group.e-rtl input.e-input,
.e-small .e-input-group.e-control-wrapper.e-rtl input.e-input,
.e-small.e-rtl .e-float-input.e-control-wrapper input,
.e-small.e-rtl .e-float-input input,
.e-small.e-rtl .e-input-group.e-control-wrapper input.e-input,
.e-small.e-rtl .e-input-group input.e-input,
.e-small.e-rtl .e-input-group.e-control-wrapper input.e-input:focus,
.e-small.e-rtl .e-input-group input.e-input:focus,
.e-small.e-rtl .e-float-input.e-control-wrapper input:focus,
.e-small.e-rtl .e-float-input input:focus,
.e-small .e-input-group.e-control-wrapper.e-rtl input.e-input:focus,
.e-small .e-input-group.e-rtl input.e-input:focus,
.e-small .e-float-input.e-control-wrapper.e-rtl input:focus,
.e-small .e-float-input.e-rtl input:focus,
.e-small.e-rtl .e-input-group.e-control-wrapper.e-input-focus input.e-input,
.e-small.e-rtl .e-input-group.e-input-focus input.e-input,
.e-small .e-input-group.e-control-wrapper.e-rtl.e-input-focus input.e-input,
.e-small .e-input-group.e-rtl.e-input-focus input.e-input,
.e-small.e-rtl .e-float-input.e-control-wrapper.e-input-focus input,
.e-small.e-rtl .e-float-input.e-input-focus input,
.e-small .e-float-input.e-control-wrapper.e-rtl.e-input-focus input,
.e-small .e-float-input.e-rtl.e-input-focus input {
  padding-left: 0;
  padding-right: 8px;
  text-indent: 0;
}

.e-rtl textarea.e-input.e-small,
textarea.e-input.e-small.e-rtl,
.e-small.e-rtl textarea.e-input,
.e-small textarea.e-input.e-rtl,
.e-float-input:not(.e-outline).e-small.e-rtl textarea,
.e-float-input:not(.e-outline).e-control-wrapper.e-small.e-rtl textarea,
.e-input-group:not(.e-outline).e-small.e-rtl textarea.e-input,
.e-input-group:not(.e-outline).e-control-wrapper.e-small.e-rtl textarea.e-input,
.e-rtl .e-float-input:not(.e-outline).e-control-wrapper.e-small textarea,
.e-rtl .e-float-input:not(.e-outline).e-small textarea,
.e-rtl .e-input-group:not(.e-outline).e-small textarea.e-input,
.e-rtl .e-input-group:not(.e-outline).e-control-wrapper.e-small textarea.e-input,
.e-float-input:not(.e-outline).e-control-wrapper.e-rtl textarea.e-small,
.e-float-input:not(.e-outline).e-rtl textarea.e-small,
.e-input-group:not(.e-outline).e-rtl textarea.e-input.e-small,
.e-input-group:not(.e-outline).e-control-wrapper.e-rtl textarea.e-input.e-small,
.e-rtl .e-float-input:not(.e-outline).e-control-wrapper textarea.e-small,
.e-rtl .e-float-input:not(.e-outline) textarea.e-small,
.e-rtl .e-input-group:not(.e-outline) textarea.e-input.e-small,
.e-rtl .e-input-group:not(.e-outline).e-control-wrapper textarea.e-input.e-small,
.e-small .e-float-input:not(.e-outline).e-control-wrapper.e-rtl textarea,
.e-small .e-float-input:not(.e-outline).e-rtl textarea,
.e-small .e-input-group:not(.e-outline).e-rtl textarea.e-input,
.e-small .e-input-group:not(.e-outline).e-control-wrapper.e-rtl textarea.e-input,
.e-small.e-rtl .e-float-input:not(.e-outline).e-control-wrapper textarea,
.e-small.e-rtl .e-float-input:not(.e-outline) textarea,
.e-small.e-rtl .e-input-group:not(.e-outline) textarea.e-input,
.e-small.e-rtl .e-input-group:not(.e-outline).e-control-wrapper textarea.e-input,
.e-small.e-rtl .e-input-group:not(.e-outline) textarea.e-input:focus,
.e-small.e-rtl .e-input-group:not(.e-outline).e-control-wrapper textarea.e-input:focus,
.e-small.e-rtl .e-float-input:not(.e-outline).e-control-wrapper textarea:focus,
.e-small.e-rtl .e-float-input:not(.e-outline) textarea:focus,
.e-small .e-input-group:not(.e-outline).e-rtl textarea.e-input:focus,
.e-small .e-input-group:not(.e-outline).e-control-wrapper.e-rtl textarea.e-input:focus,
.e-small .e-float-input:not(.e-outline).e-control-wrapper.e-rtl textarea:focus,
.e-small .e-float-input:not(.e-outline).e-rtl textarea:focus {
  padding-right: 8px;
  text-indent: 0;
}

.e-float-input .e-clear-icon,
.e-float-input.e-control-wrapper .e-clear-icon,
.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  background: transparent;
  border: 0;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  line-height: 1;
  min-width: 24px;
  outline: none;
  padding: 0;
  text-align: center;
}

.e-float-input .e-clear-icon::before,
.e-float-input.e-control-wrapper .e-clear-icon::before,
.e-input-group .e-clear-icon::before,
.e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 14px;
  padding: 0;
  text-align: center;
  vertical-align: middle;
}

.e-float-input.e-static-clear .e-clear-icon.e-clear-icon-hide,
.e-float-input.e-control-wrapper.e-static-clear .e-clear-icon.e-clear-icon-hide,
.e-input-group.e-static-clear .e-clear-icon.e-clear-icon-hide,
.e-input-group.e-control-wrapper.e-static-clear .e-clear-icon.e-clear-icon-hide {
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.e-float-input.e-small .e-clear-icon::before,
.e-float-input.e-control-wrapper.e-small .e-clear-icon::before,
.e-input-group.e-small .e-clear-icon::before,
.e-input-group.e-control-wrapper.e-small .e-clear-icon::before,
.e-float-input.e-control-wrapper input.e-small:first-child ~ .e-clear-icon::before,
.e-small .e-float-input.e-control-wrapper .e-clear-icon::before,
.e-float-input input.e-small:first-child ~ .e-clear-icon::before,
.e-small .e-float-input .e-clear-icon::before,
.e-small .e-input-group .e-clear-icon::before,
.e-small .e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 12px;
}

.e-input-group.e-static-clear .e-clear-icon.e-clear-icon-hide,
.e-input-group.e-control-wrapper.e-static-clear .e-clear-icon.e-clear-icon-hide {
  cursor: pointer;
}

.e-float-input.e-control-wrapper input[readonly]:first-child ~ .e-clear-icon,
.e-float-input.e-control-wrapper.e-input-group input[readonly]:first-child ~ .e-clear-icon,
.e-float-input input[readonly]:first-child ~ .e-clear-icon,
.e-float-input.e-input-group input[readonly]:first-child ~ .e-clear-icon,
.e-input-group input[readonly]:first-child ~ .e-clear-icon.e-clear-icon-hide,
.e-float-input.e-control-wrapper.e-input-group input[readonly]:first-child ~ .e-clear-icon,
.e-float-input.e-input-group.e-control-wrapper input[readonly]:first-child ~ .e-clear-icon,
.e-input-group.e-control-wrapper input[readonly]:first-child .e-clear-icon.e-clear-icon-hide {
  cursor: auto;
}

.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  min-height: 20px;
  min-width: 27px;
  padding-bottom: 5px;
  padding-left: 4px;
  padding-right: 4px;
  padding-top: 5px;
}

.e-input-group.e-small .e-clear-icon,
.e-input-group .e-clear-icon.e-small,
.e-input-group.e-control-wrapper.e-small .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon.e-small {
  min-height: 24px;
  min-width: 24px;
  padding-bottom: 7px;
  padding-right: 8px;
  padding-top: 7px;
}

.e-input-group.e-small .e-clear-icon,
.e-input-group .e-clear-icon.e-small,
.e-small .e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper.e-small .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon.e-small,
.e-small .e-input-group.e-control-wrapper .e-clear-icon {
  min-height: 16px;
  min-width: 16px;
  padding-bottom: 3px;
  padding-right: 4px;
  padding-top: 3px;
}

.e-input:not(:valid),
.e-input:valid,
.e-float-input.e-control-wrapper input:not(:valid),
.e-float-input.e-control-wrapper input:valid,
.e-float-input input:not(:valid),
.e-float-input input:valid,
.e-input-group input:not(:valid),
.e-input-group input:valid,
.e-input-group.e-control-wrapper input:not(:valid),
.e-input-group.e-control-wrapper input:valid,
.e-float-input.e-control-wrapper textarea:not(:valid),
.e-float-input.e-control-wrapper textarea:valid,
.e-float-input textarea:not(:valid),
.e-float-input textarea:valid,
.e-input-group.e-control-wrapper textarea:not(:valid),
.e-input-group.e-control-wrapper textarea:valid,
.e-input-group textarea:not(:valid),
.e-input-group textarea:valid {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.e-input-group .e-input-in-wrap,
.e-input-group.e-control-wrapper .e-input-in-wrap,
.e-float-input .e-input-in-wrap,
.e-float-input.e-control-wrapper .e-input-in-wrap {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}

.e-float-input .e-input-in-wrap label.e-float-text,
.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text {
  right: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
}

.e-float-input textarea:focus ~ label.e-float-text,
.e-float-input textarea:valid ~ label.e-float-text,
.e-float-input textarea ~ label.e-label-top.e-float-text,
.e-float-input textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input textarea label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper textarea label.e-float-text.e-label-top {
  font-size: 12px;
  top: -24px;
  -webkit-transform: translate3d(0, 6px, 0) scale(0.92);
          transform: translate3d(0, 6px, 0) scale(0.92);
  left: 2px;
}

.e-float-input.e-small textarea:focus ~ label.e-float-text,
.e-float-input.e-small textarea:valid ~ label.e-float-text,
.e-float-input.e-small textarea ~ label.e-label-top.e-float-text,
.e-small .e-float-input textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text {
  font-size: 10px;
  top: -21px;
  left: 2px;
}

.e-float-input textarea ~ .e-float-text,
.e-float-input.e-control-wrapper textarea ~ .e-float-text {
  top: 13px;
}

.e-float-input.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
}

.e-float-input.e-small textarea ~ label.e-float-text,
.e-float-input textarea ~ label.e-float-text.e-small,
.e-float-input textarea.e-small ~ label.e-float-text,
.e-small .e-float-input textarea ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea ~ label.e-float-text,
.e-float-input.e-control-wrapper textarea ~ label.e-float-text.e-small,
.e-float-input.e-control-wrapper textarea.e-small ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea ~ label.e-float-text {
  font-size: 12px;
  top: 15px;
}

.e-input-group:hover:not(.e-disabled),
.e-input-group.e-control-wrapper:hover:not(.e-disabled),
.e-float-input:hover:not(.e-disabled),
.e-float-input:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-float-input:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-float-input:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-float-input:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-float-input.e-control-wrapper:hover:not(.e-disabled),
.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]) {
  border-bottom-width: 1px;
}

.e-float-input:not(.e-outline) input:-webkit-autofill ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-autofill:not(.e-outline) input:-webkit-autofill ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-autofill:not(.e-input-focus):not(.e-outline) input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
  -webkit-user-select: text;
          user-select: text;
}

.e-small .e-float-input:not(.e-outline) input:-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input:not(.e-outline) input:-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-outline) input:-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input.e-control-wrapper.e-autofill:not(.e-outline) input:-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-input-focus):not(.e-outline) input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom,
.e-small.e-float-input.e-control-wrapper.e-autofill:not(.e-input-focus):not(.e-outline) input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 10px;
  -webkit-user-select: text;
          user-select: text;
}

.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  font-style: normal;
  font-weight: normal;
  left: 0;
  overflow: hidden;
  padding-left: 10px;
  pointer-events: none;
  position: absolute;
  text-overflow: ellipsis;
  top: -11px;
  -webkit-transform: translate3d(0, 16px, 0) scale(1);
          transform: translate3d(0, 16px, 0) scale(1);
  -webkit-transform-origin: left top;
          transform-origin: left top;
  -webkit-transition: 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 100%;
}

.e-float-input textarea:-webkit-autofill ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
  font-weight: 500;
  padding-right: 0;
  -webkit-transform: translate3d(-10px, -40px, 0) scale(1);
          transform: translate3d(-10px, -40px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-small .e-float-input textarea:-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input textarea:-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom,
.e-small.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 10px;
  -webkit-user-select: text;
          user-select: text;
}

.e-float-input.e-small textarea:focus ~ label.e-float-text,
.e-float-input.e-small textarea:valid ~ label.e-float-text,
.e-float-input.e-small textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-small textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-small textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-small textarea label.e-float-text.e-label-top,
.e-small .e-float-input textarea:focus ~ label.e-float-text,
.e-small .e-float-input textarea:valid ~ label.e-float-text,
.e-small .e-float-input textarea ~ label.e-label-top.e-float-text,
.e-small .e-float-input textarea[readonly] ~ label.e-label-top.e-float-text,
.e-small .e-float-input textarea[disabled] ~ label.e-label-top.e-float-text,
.e-small .e-float-input textarea label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small textarea label.e-float-text.e-label-top,
.e-small .e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea[readonly] ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper textarea label.e-float-text.e-label-top {
  -webkit-transform: translate3d(0, 6px, 0) scale(0.86);
          transform: translate3d(0, 6px, 0) scale(0.86);
}

.e-float-input textarea[disabled],
.e-float-input textarea.e-disabled,
.e-float-input.e-control-wrapper textarea[disabled],
.e-float-input.e-control-wrapper textarea.e-disabled {
  border-color: var(--color-sf-border);
  border-style: solid;
}

.e-float-input textarea[disabled],
.e-float-input textarea.e-disabled,
.e-float-input.e-control-wrapper textarea[disabled],
.e-float-input.e-control-wrapper textarea.e-disabled {
  background: transparent;
  background-image: none;
  cursor: not-allowed;
}

textarea.e-input,
.e-input-group textarea,
.e-input-group textarea.e-input,
.e-input-group.e-input-focus textarea,
.e-input-group.e-input-focus textarea.e-input,
.e-input-group.e-control-wrapper textarea,
.e-input-group.e-control-wrapper.e-input-focus textarea,
.e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-control-wrapper.e-input-focus textarea.e-input,
.e-float-input textarea,
.e-float-input.e-control-wrapper textarea {
  line-height: 1.5;
  min-height: 32px;
  min-width: 0;
  padding: 4px 8px 4px 8px;
  resize: vertical;
}

.e-input-group.e-small textarea,
.e-input-group.e-small textarea.e-input,
.e-input-group textarea.e-small,
.e-input-group textarea.e-input.e-small,
.e-input-group.e-control-wrapper.e-small textarea,
.e-input-group.e-control-wrapper.e-small textarea.e-input,
.e-small .e-input-group textarea,
.e-small .e-input-group textarea.e-input,
.e-float-input.e-small textarea,
.e-float-input textarea.e-small,
.e-float-input.e-control-wrapper.e-small textarea,
.e-float-input.e-control-wrapper textarea.e-small,
.e-small .e-float-input textarea,
.e-small .e-float-input.e-control-wrapper textarea {
  font: inherit;
  min-height: 26px;
  padding: 4px 8px 4px 8px;
}

.e-input-group.e-input-focus.e-small textarea,
.e-input-group.e-input-focus.e-small textarea.e-input,
.e-input-group.e-input-focus textarea.e-small,
.e-input-group.e-input-focus textarea.e-input.e-small,
.e-input-group.e-input-focus textarea.e-input.e-small,
.e-input-group.e-control-wrapper.e-input-focus.e-small textarea,
.e-input-group.e-control-wrapper.e-input-focus.e-small textarea.e-input,
.e-small .e-input-group.e-input-focus textarea,
.e-small .e-input-group.e-input-focus textarea.e-input {
  font: inherit;
  min-height: 26px;
  padding: 4px 8px 4px 8px;
}

.e-input-group.e-small textarea:focus,
.e-input-group.e-small textarea.e-input:focus,
.e-input-group textarea.e-small:focus,
.e-input-group textarea.e-input.e-small:focus,
.e-input-group.e-control-wrapper.e-small textarea:focus,
.e-input-group.e-control-wrapper.e-small textarea.e-input:focus,
.e-small .e-input-group textarea:focus,
.e-small .e-input-group textarea.e-input:focus,
.e-float-input.e-small textarea:focus,
.e-float-input textarea.e-small:focus,
.e-float-input.e-control-wrapper.e-small textarea:focus,
.e-float-input.e-control-wrapper textarea.e-small:focus,
.e-small .e-float-input textarea:focus,
.e-small .e-float-input.e-control-wrapper textarea:focus {
  padding: 4px 8px 4px 8px;
}

.e-underline.e-input-group.e-control-wrapper,
.e-underline.e-input-group,
.e-underline.e-input-group:not(.e-float-icon-left),
.e-underline.e-float-input,
.e-underline.e-float-input.e-control-wrapper,
.e-underline.e-input-group:not(.e-float-icon-left),
.e-underline.e-input-group.e-success:not(.e-float-icon-left),
.e-underline.e-input-group.e-warning:not(.e-float-icon-left),
.e-underline.e-input-group.e-error:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper.e-success:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper.e-warning:not(.e-float-icon-left),
.e-underline.e-input-group.e-control-wrapper.e-error:not(.e-float-icon-left) {
  border: 1px solid var(--color-sf-border);
  border-width: 1px;
  padding-top: 1px;
  border-radius: 0;
}

textarea.e-outline.e-input,
.e-outline.e-input-group textarea.e-input,
.e-outline.e-input-group textarea,
.e-outline.e-input-group.e-control-wrapper textarea.e-input,
.e-outline.e-input-group.e-control-wrapper textarea,
.e-outline.e-float-input textarea.e-input,
.e-outline.e-float-input textarea,
.e-outline.e-float-input.e-control-wrapper textarea.e-input,
.e-outline.e-float-input.e-control-wrapper textarea,
.e-outline.e-input-group:not(.e-float-icon-left) textarea.e-input:focus,
.e-outline.e-input-group.e-control-wrapper:not(.e-float-icon-left) textarea.e-input:focus {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin: 8px 0 1px;
  padding: 0 12px 12px;
}

.e-outline.e-float-input.e-small input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-small input:valid ~ label.e-float-text,
.e-outline.e-float-input.e-small input ~ label.e-label-top.e-float-text,
.e-small .e-outline.e-float-input input ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text,
.e-small .e-outline.e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-small textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-small textarea:valid ~ label.e-float-text,
.e-outline.e-float-input.e-small textarea ~ label.e-label-top.e-float-text,
.e-small .e-outline.e-float-input textarea ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-small textarea[readonly] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-small textarea[disabled] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text,
.e-small .e-outline.e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small textarea[readonly] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small textarea[disabled] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-small.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-input-focus input ~ label.e-float-text {
  font-size: 12px;
}

.e-outline.e-float-input textarea:focus ~ label.e-float-text,
.e-outline.e-float-input textarea:valid ~ label.e-float-text,
.e-outline.e-float-input textarea ~ label.e-label-top.e-float-text,
.e-outline.e-float-input textarea[readonly] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input textarea[disabled] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input textarea label.e-float-text.e-label-top,
.e-outline.e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper textarea[readonly] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text,
.e-outline.e-float-input.e-control-wrapper textarea label.e-float-text.e-label-top {
  top: -21px;
  -webkit-transform: translate3d(0, 0, 0) scale(1);
          transform: translate3d(0, 0, 0) scale(1);
  left: 2px;
}

.e-filled.e-input-group,
.e-filled.e-input-group.e-control-wrapper,
.e-filled.e-float-input,
.e-filled.e-float-input.e-control-wrapper {
  line-height: 1;
}

.e-filled input.e-input,
.e-filled.e-input-group input,
.e-filled.e-input-group.e-control-wrapper input,
.e-filled.e-input-group input.e-input,
.e-filled.e-input-group.e-control-wrapper input.e-input,
.e-filled.e-float-input:not(.e-input-group) input,
.e-filled.e-float-input.e-control-wrapper:not(.e-input-group) input,
.e-control .e-filled input.e-input,
.e-control .e-filled.e-input-group input,
.e-control .e-filled.e-input-group input.e-input,
.e-control .e-filled.e-input-group.e-control-wrapper input,
.e-control .e-filled.e-input-group.e-control-wrapper input.e-input,
.e-control .e-filled.e-float-input input,
.e-control .e-filled.e-float-input.e-control-wrapper input,
.e-control.e-filled.e-input-group input,
.e-control.e-filled.e-input-group input.e-input,
.e-control.e-filled.e-input-group.e-control-wrapper input,
.e-control.e-filled.e-input-group.e-control-wrapper input.e-input,
.e-control.e-filled.e-float-input input,
.e-control.e-filled.e-float-input.e-control-wrapper input,
.e-filled input.e-input.e-small,
.e-filled.e-input-group.e-small input,
.e-filled.e-input-group.e-small input.e-input,
.e-small .e-filled.e-input-group input,
.e-small .e-filled.e-input-group input.e-input,
.e-filled.e-input-group.e-control-wrapper.e-small input,
.e-filled.e-input-group.e-control-wrapper.e-small input.e-input,
.e-small .e-filled.e-input-group.e-control-wrapper input,
.e-small .e-filled.e-input-group.e-control-wrapper input.e-input,
.e-filled.e-float-input.e-small input,
.e-filled.e-float-input.e-small input.e-input,
.e-small .e-filled.e-float-input input,
.e-small .e-filled.e-float-input input.e-input,
.e-filled.e-float-input.e-control-wrapper.e-small input,
.e-filled.e-float-input.e-control-wrapper.e-small input.e-input,
.e-small .e-filled.e-float-input.e-control-wrapper input,
.e-small .e-filled.e-float-input.e-control-wrapper input.e-input,
.e-filled.e-float-input.e-small:not(.e-input-group) input,
.e-filled.e-float-input.e-small:not(.e-input-group) input.e-input,
.e-small .e-filled.e-float-input:not(.e-input-group) input,
.e-small .e-filled.e-float-input:not(.e-input-group) input.e-input .e-filled.e-float-input.e-control-wrapper.e-small:not(.e-input-group) input,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-input-group) input.e-input,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-group) input,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-group) input.e-input,
.e-filled textarea.e-input,
.e-filled.e-input-group textarea,
.e-filled.e-input-group.e-control-wrapper textarea,
.e-filled.e-float-input textarea,
.e-filled.e-float-input.e-control-wrapper textarea,
.e-filled textarea.e-input.e-small,
.e-filled.e-input-group.e-small textarea,
.e-filled.e-input-group.e-small textarea.e-input,
.e-small .e-filled.e-input-group textarea,
.e-small .e-filled.e-input-group textarea.e-input,
.e-filled.e-input-group.e-control-wrapper.e-small textarea,
.e-filled.e-input-group.e-control-wrapper.e-small textarea.e-input,
.e-small .e-filled.e-input-group.e-control-wrapper textarea,
.e-small .e-filled.e-input-group.e-control-wrapper textarea.e-input,
.e-filled.e-float-input.e-small textarea,
.e-filled.e-float-input.e-small textarea.e-input,
.e-small .e-filled.e-float-input textarea,
.e-small .e-filled.e-float-input textarea.e-input,
.e-filled.e-float-input.e-control-wrapper.e-small textarea,
.e-filled.e-float-input.e-control-wrapper.e-small textarea.e-input,
.e-small .e-filled.e-float-input.e-control-wrapper textarea,
.e-small .e-filled.e-float-input.e-control-wrapper textarea.e-input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.e-filled.e-float-input input,
.e-filled.e-float-input textarea,
.e-filled.e-float-input.e-control-wrapper input,
.e-filled.e-float-input.e-control-wrapper textarea {
  border: 0 solid;
  border-width: 0;
}

.e-filled.e-float-input:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-filled.e-float-input:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-filled.e-float-input:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-filled.e-float-input:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]),
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]),
.e-filled.e-input-group.e-float-icon-left:not(.e-disabled):not(.e-input-focus) .e-input-in-wrap:hover,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-disabled):not(.e-input-focus) .e-input-in-wrap:hover,
.e-filled.e-float-input.e-float-icon-left:not(.e-disabled):not(.e-input-focus) .e-input-in-wrap:hover,
.e-filled.e-float-input.e-control-wrapper.e-float-icon-left:not(.e-disabled):not(.e-input-focus) .e-input-in-wrap:hover {
  border-bottom-width: 0;
}

.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled).e-success:not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success).e-warning:not(.e-error) input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning).e-error input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning):not(.e-error) textarea,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled).e-success:not(.e-warning):not(.e-error) textarea,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success).e-warning:not(.e-error) textarea,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning).e-error textarea,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled).e-success:not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success).e-warning:not(.e-error) input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning).e-error input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning):not(.e-error) textarea,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled).e-success:not(.e-warning):not(.e-error) textarea,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success).e-warning:not(.e-error) textarea,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-disabled):not(.e-success):not(.e-warning).e-error textarea,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-success):not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left).e-success:not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-success).e-warning:not(.e-error) input,
.e-filled.e-float-input.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-success):not(.e-warning).e-error input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-success):not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left).e-success:not(.e-warning):not(.e-error) input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-success).e-warning:not(.e-error) input,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:not(.e-input-group):not(.e-float-icon-left):not(.e-success):not(.e-warning).e-error input {
  border-width: 0;
}

.e-filled.e-input-group,
.e-filled.e-input-group.e-control-wrapper {
  border-radius: 4px 4px 0 0;
  padding: 0 10px 0 12px;
}

.e-filled.e-input-group.e-small,
.e-filled.e-input-group.e-control-wrapper.e-small,
.e-small .e-filled.e-input-group.e-control-wrapper {
  margin-top: 0;
  padding: 0 6px 0 8px;
}

.e-filled.e-float-input,
.e-filled.e-float-input.e-control-wrapper {
  border: 1px solid;
  border-radius: 4px 4px 0 0;
  border-width: 1px;
  margin-top: 0;
  padding: 0;
}

.e-filled.e-float-input.e-small,
.e-filled.e-float-input.e-control-wrapper.e-small,
.e-small .e-filled.e-float-input.e-control-wrapper {
  margin-top: 0;
  padding: 0;
}

.e-rtl.e-filled.e-input-group,
.e-rtl.e-filled.e-input-group.e-control-wrapper,
.e-rtl .e-filled.e-input-group,
.e-rtl .e-filled.e-input-group.e-control-wrapper {
  padding: 0 12px 0 10px;
}

.e-rtl.e-filled.e-input-group.e-small,
.e-rtl.e-filled.e-input-group.e-control-wrapper.e-small,
.e-small .e-rtl.e-filled.e-input-group.e-control-wrapper,
.e-rtl .e-filled.e-input-group.e-small,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-small,
.e-small.e-rtl .e-filled.e-input-group.e-control-wrapper {
  padding: 0 8px 0 6px;
}

.e-rtl.e-filled.e-float-input,
.e-rtl.e-filled.e-float-input.e-control-wrapper,
.e-rtl .e-filled.e-float-input,
.e-rtl .e-filled.e-float-input.e-control-wrapper {
  padding: 0 12px 0 10px;
}

.e-rtl.e-filled.e-float-input.e-small,
.e-rtl.e-filled.e-float-input.e-control-wrapper.e-small,
.e-small .e-rtl.e-filled.e-float-input.e-control-wrapper,
.e-rtl .e-filled.e-float-input.e-small,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-small,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper {
  padding: 0 8px 0 6px;
}

.e-filled input.e-input,
.e-filled.e-input-group input.e-input,
.e-filled.e-input-group.e-control-wrapper input.e-input,
.e-filled textarea.e-input,
.e-filled.e-input-group textarea.e-input,
.e-filled.e-input-group.e-control-wrapper textarea.e-input,
.e-filled.e-input-group input.e-input:focus,
.e-filled.e-input-group.e-control-wrapper input.e-input:focus,
.e-filled.e-input-group textarea.e-input:focus,
.e-filled.e-input-group.e-control-wrapper textarea.e-input:focus,
.e-filled.e-input-group.e-input-focus input.e-input,
.e-filled.e-input-group.e-control-wrapper.e-input-focus input.e-input {
  padding: 10px 2px 10px 0;
}

.e-filled .e-input:focus {
  padding-bottom: 10px;
}

.e-filled .e-input.e-small:focus {
  padding-bottom: 7px;
}

.e-filled .e-input.e-small,
.e-filled.e-input-group.e-small .e-input,
.e-filled.e-input-group.e-control-wrapper.e-small .e-input,
.e-filled.e-input-group.e-small .e-input:focus,
.e-filled.e-input-group.e-control-wrapper.e-small .e-input:focus,
.e-filled.e-input-group.e-small.e-input-focus .e-input,
.e-filled.e-input-group.e-control-wrapper.e-small.e-input-focus .e-input {
  padding: 7px 2px 7px 0;
}

.e-filled.e-float-input input,
.e-filled.e-float-input.e-control-wrapper input {
  padding: 7px 0 5px 7px;
}

.e-filled.e-float-input.e-small input,
.e-filled.e-float-input.e-control-wrapper.e-small input {
  padding: 12px 2px 4px 0;
}

.e-filled input.e-input.e-rtl,
.e-filled.e-input-group.e-rtl input.e-input,
.e-filled.e-input-group.e-control-wrapper.e-rtl input.e-input,
.e-rtl .e-filled.e-input-group input.e-input,
.e-rtl .e-filled.e-input-group.e-control-wrapper input.e-input,
.e-filled.e-input-group.e-rtl input.e-input,
.e-filled.e-input-group.e-control-wrapper.e-rtl input.e-input,
.e-rtl .e-filled.e-input-group input.e-input,
.e-rtl .e-filled.e-input-group.e-control-wrapper input.e-input,
.e-filled.e-input-group.e-rtl input.e-input:focus,
.e-filled.e-input-group.e-control-wrapper.e-rtl input.e-input:focus,
.e-rtl .e-filled.e-input-group input.e-input:focus,
.e-rtl .e-filled.e-input-group.e-control-wrapper input.e-input:focus,
.e-filled.e-input-group.e-rtl.e-input-focus input.e-input,
.e-filled.e-input-group.e-control-wrapper.e-rtl.e-input-focus input.e-input,
.e-rtl .e-filled.e-input-group.e-input-focus input.e-input,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-input-focus input.e-input {
  padding: 10px 0 10px 2px;
  text-indent: 0;
}

.e-filled.e-float-input.e-rtl input,
.e-filled.e-float-input.e-control-wrapper.e-rtl input,
.e-rtl .e-filled.e-float-input input,
.e-rtl .e-filled.e-float-input.e-control-wrapper input,
.e-filled.e-float-input.e-rtl input,
.e-filled.e-float-input.e-control-wrapper.e-rtl input,
.e-rtl .e-filled.e-float-input input,
.e-rtl .e-filled.e-float-input.e-control-wrapper input,
.e-filled.e-float-input.e-rtl input:focus,
.e-filled.e-float-input.e-control-wrapper.e-rtl input:focus,
.e-rtl .e-filled.e-float-input input:focus,
.e-rtl .e-filled.e-float-input.e-control-wrapper input:focus,
.e-filled.e-float-input.e-rtl.e-input-focus input,
.e-filled.e-float-input.e-control-wrapper.e-rtl.e-input-focus input,
.e-rtl .e-filled.e-float-input.e-input-focus input,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-focus input {
  padding: 14px 0 5px 2px;
  text-indent: 0;
}

.e-rtl .e-filled input.e-input.e-small,
.e-filled input.e-input.e-small.e-rtl,
.e-small.e-rtl .e-filled input.e-input,
.e-small .e-filled input.e-input.e-rtl,
.e-filled.e-input-group.e-small.e-rtl input.e-input,
.e-filled.e-input-group.e-control-wrapper.e-small.e-rtl input.e-input,
.e-rtl .e-filled.e-input-group.e-small input.e-input,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-small input.e-input,
.e-filled.e-input-group.e-rtl input.e-input.e-small,
.e-filled.e-input-group.e-control-wrapper.e-rtl input.e-input.e-small,
.e-rtl .e-filled.e-input-group input.e-input.e-small,
.e-rtl .e-filled.e-input-group.e-control-wrapper input.e-input.e-small,
.e-small .e-filled.e-input-group.e-rtl input.e-input,
.e-small .e-filled.e-input-group.e-control-wrapper.e-rtl input.e-input,
.e-small.e-rtl .e-filled.e-input-group.e-control-wrapper input.e-input,
.e-small.e-rtl .e-filled.e-input-group input.e-input,
.e-small.e-rtl .e-filled.e-input-group.e-control-wrapper input.e-input:focus,
.e-small.e-rtl .e-filled.e-input-group input.e-input:focus,
.e-small .e-filled.e-input-group.e-control-wrapper.e-rtl input.e-input:focus,
.e-small .e-filled.e-input-group.e-rtl input.e-input:focus,
.e-small.e-rtl .e-filled.e-input-group.e-control-wrapper.e-input-focus input.e-input,
.e-small.e-rtl .e-filled.e-input-group.e-input-focus input.e-input,
.e-small .e-filled.e-input-group.e-control-wrapper.e-rtl.e-input-focus input.e-input,
.e-small .e-filled.e-input-group.e-rtl.e-input-focus input.e-input {
  padding: 7px 0 7px 2px;
  text-indent: 0;
}

.e-filled.e-float-input.e-control-wrapper.e-small.e-rtl input,
.e-filled.e-float-input.e-small.e-rtl input,
.e-rtl .e-filled.e-float-input.e-small input,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-small input,
.e-filled.e-float-input.e-rtl input.e-small,
.e-filled.e-float-input.e-control-wrapper.e-rtl input.e-small,
.e-rtl .e-filled.e-float-input input.e-small,
.e-rtl .e-filled.e-float-input.e-control-wrapper input.e-small,
.e-small .e-filled.e-float-input.e-rtl input,
.e-small .e-filled.e-float-input.e-control-wrapper.e-rtl input,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper input,
.e-small.e-rtl .e-filled.e-float-input input,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper input:focus,
.e-small.e-rtl .e-filled.e-float-input input:focus,
.e-small .e-filled.e-float-input.e-control-wrapper.e-rtl input:focus,
.e-small .e-filled.e-float-input.e-rtl input:focus,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-focus input,
.e-small.e-rtl .e-filled.e-float-input.e-input-focus input,
.e-small .e-filled.e-float-input.e-control-wrapper.e-rtl.e-input-focus input,
.e-small .e-filled.e-float-input.e-rtl.e-input-focus input {
  padding: 12px 0 4px 2px;
  text-indent: 0;
}

.e-filled.e-float-input,
.e-filled.e-float-input.e-control-wrapper,
.e-filled.e-float-input.e-disabled,
.e-filled.e-float-input.e-control-wrapper.e-disabled,
.e-filled.e-float-input.e-input-group.e-disabled,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-disabled {
  font-size: 14px;
}

.e-filled.e-float-input.e-small,
.e-small .e-filled.e-float-input,
.e-filled.e-float-input.e-control-wrapper.e-small,
.e-small .e-filled.e-float-input.e-control-wrapper,
.e-filled.e-float-input.e-small.e-disabled,
.e-small .e-filled.e-float-input.e-disabled,
.e-filled.e-float-input.e-control-wrapper.e-small.e-disabled,
.e-small .e-filled.e-float-input.e-control-wrapper.e-disabled,
.e-filled.e-float-input.e-input-group.e-small.e-disabled,
.e-small .e-filled.e-float-input.e-input-group.e-disabled,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-small.e-disabled,
.e-small .e-filled.e-float-input.e-input-group.e-control-wrapper.e-disabled {
  font-size: 12px;
}

.e-filled.e-input-group.e-small:not(.e-float-input) .e-input,
.e-small .e-filled.e-input-group:not(.e-float-input) .e-input,
.e-filled.e-input-group.e-control-wrapper.e-small:not(.e-float-input) .e-input,
.e-small .e-filled.e-input-group.e-control-wrapper:not(.e-float-input) .e-input {
  min-height: 35px;
}

.e-filled.e-float-input.e-small input,
.e-small .e-filled.e-float-input input,
.e-filled.e-float-input.e-input-group.e-small input,
.e-small .e-filled.e-float-input.e-input-group input,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-small input,
.e-small .e-filled.e-float-input.e-input-group.e-control-wrapper input,
.e-filled.e-float-input.e-control-wrapper.e-small input,
.e-small .e-filled.e-float-input.e-control-wrapper input,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small input,
.e-small .e-filled.e-float-input.e-control-wrapper.e-input-group input,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small input,
.e-small .e-filled.e-float-input.e-control-wrapper.e-input-group input {
  min-height: 35px;
}

.e-filled.e-input-group input.e-input,
.e-filled.e-input-group.e-control-wrapper input.e-input,
.e-filled.e-input-group:not(.e-float-input) input.e-input,
.e-filled.e-input-group:not(.e-float-input).e-control-wrapper input.e-input {
  min-height: 22px;
}

.e-float-input.e-filled.e-input-group.e-control-wrapper input,
.e-float-input.e-filled input,
.e-float-input.e-filled.e-control-wrapper input {
  min-height: 22px;
}

.e-filled label.e-float-text,
.e-filled.e-float-input label.e-float-text,
.e-filled.e-float-input.e-control-wrapper label.e-float-text,
.e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  left: 12px;
  letter-spacing: 0.009375em;
  line-height: 1.15;
  padding-left: 10px;
  pointer-events: none;
  right: auto;
  top: 8px;
  -webkit-transform: none;
          transform: none;
  -webkit-transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.e-filled.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  padding-left: 10px;
  top: 8px;
  -webkit-transform: none;
          transform: none;
  width: 100%;
}

.e-filled.e-float-input input:focus ~ label.e-float-text,
.e-filled.e-float-input input:valid ~ label.e-float-text,
.e-filled.e-float-input input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-input-focus input ~ label.e-float-text {
  font-size: 14px;
  top: -7px;
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
  left: 1px;
}

.e-filled.e-float-input textarea:focus ~ label.e-float-text,
.e-filled.e-float-input textarea:valid ~ label.e-float-text,
.e-filled.e-float-input textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea label.e-float-text.e-label-top {
  font-size: 14px;
  top: -7px;
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
}

.e-filled.e-float-input input:-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-input-focus) input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input textarea:-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  top: -7px;
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
  -webkit-user-select: text;
          user-select: text;
}

.e-filled.e-float-input.e-small input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text {
  font-size: 13px;
  top: 10px;
}

.e-small .e-filled.e-float-input input:-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input input:-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-input-focus) input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-input-focus) input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input textarea:-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input textarea:-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 13px;
  top: 10px;
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
  -webkit-user-select: text;
          user-select: text;
}

.e-filled.e-float-input.e-small label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small textarea ~ label.e-float-text,
.e-filled.e-float-input textarea ~ label.e-float-text.e-small,
.e-filled.e-float-input textarea.e-small ~ label.e-float-text,
.e-small .e-filled.e-float-input textarea ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small textarea ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea ~ label.e-float-text.e-small,
.e-filled.e-float-input.e-control-wrapper textarea.e-small ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper textarea ~ label.e-float-text {
  font-size: 13px;
  top: 10px;
}

.e-filled.e-float-input label.e-float-text,
.e-filled.e-float-input label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper label.e-float-text,
.e-filled.e-float-input.e-control-wrapper label.e-float-text.e-label-bottom,
.e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  left: 12px;
}

.e-filled.e-float-input.e-rtl label.e-float-text,
.e-filled.e-float-input.e-rtl label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input label.e-float-text .e-filled.e-float-input.e-control-wrapper.e-rtl label.e-float-text,
.e-filled.e-float-input.e-rtl.e-control-wrapper label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper label.e-float-text,
.e-filled.e-float-input.e-rtl:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-rtl:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  right: 12px;
}

.e-filled.e-float-input.e-small label.e-float-text,
.e-filled.e-float-input.e-small label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  left: 8px;
}

.e-filled.e-float-input.e-small.e-rtl label.e-float-text,
.e-filled.e-float-input.e-rtl.e-small label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-small label.e-float-text .e-filled.e-float-input.e-control-wrapper.e-rtl.e-small label.e-float-text,
.e-filled.e-float-input.e-rtl.e-control-wrapper.e-small label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-small label.e-float-text,
.e-small .e-filled.e-float-input.e-rtl label.e-float-text,
.e-small .e-filled.e-float-input.e-rtl label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input label.e-float-text .e-small .e-filled.e-float-input.e-control-wrapper.e-rtl label.e-float-text,
.e-small .e-filled.e-float-input.e-rtl.e-control-wrapper label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input.e-control-wrapper label.e-float-text,
.e-small.e-filled.e-float-input.e-rtl:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small.e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-small.e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-small.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-rtl:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small.e-filled.e-float-input.e-rtl:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small.e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-small.e-filled.e-float-input:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-small.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-rtl:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  right: 8px;
}

.e-filled.e-float-input .e-float-line,
.e-float-input.e-filled.e-control-wrapper .e-float-line {
  bottom: -1px;
  position: absolute;
}

.e-float-input.e-filled .e-float-line,
.e-float-input.e-filled .e-float-text,
.e-float-input.e-filled.e-control-wrapper .e-float-line,
.e-float-input.e-filled.e-control-wrapper .e-float-text,
.e-filled.e-float-input.e-rtl .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-rtl .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input .e-input-in-wrap label.e-float-text .e-filled.e-float-input.e-control-wrapper.e-rtl .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-rtl.e-control-wrapper .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-rtl:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small.e-rtl .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-rtl.e-small .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-small .e-input-in-wrap label.e-float-text .e-filled.e-float-input.e-control-wrapper.e-rtl.e-small .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-rtl.e-control-wrapper.e-small .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap label.e-float-text,
.e-small .e-filled.e-float-input.e-rtl .e-input-in-wrap label.e-float-text,
.e-small .e-filled.e-float-input.e-rtl .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input .e-input-in-wrap label.e-float-text .e-small .e-filled.e-float-input.e-control-wrapper.e-rtl .e-input-in-wrap label.e-float-text,
.e-small .e-filled.e-float-input.e-rtl.e-control-wrapper .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text,
.e-small.e-filled.e-float-input.e-rtl:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small.e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-small.e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl .e-small.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-rtl:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper.e-rtl:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-rtl.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  right: 0;
}

.e-filled.e-input-group:not(.e-disabled):not(.e-float-icon-left)::before,
.e-filled.e-filled.e-input-group:not(.e-disabled):not(.e-float-icon-left)::after,
.e-filled.e-input-group.e-control-wrapper:not(.e-disabled):not(.e-float-icon-left)::before,
.e-filled.e-input-group.e-control-wrapper:not(.e-disabled):not(.e-float-icon-left)::after {
  bottom: -0.1px;
}

.e-filled.e-input-group .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-filled.e-input-group.e-small .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-filled.e-input-group .e-input-group-icon,
.e-small .e-filled.e-input-group.e-control-wrapper .e-input-group-icon {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 0;
  min-height: 32px;
  min-width: 32px;
  padding: 0 0 0 8px;
}

.e-rtl.e-filled.e-input-group .e-input-group-icon,
.e-rtl.e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl.e-filled.e-input-group.e-small .e-input-group-icon,
.e-rtl.e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-rtl.e-filled.e-input-group .e-input-group-icon,
.e-small .e-rtl.e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl .e-filled.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-small .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small.e-rtl .e-filled.e-input-group .e-input-group-icon,
.e-small.e-rtl .e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl.e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon,
.e-rtl.e-filled.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small .e-rtl.e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small .e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small.e-rtl .e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  padding: 0 8px 0 0;
}

.e-filled.e-input-group.e-small .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-filled.e-input-group .e-input-group-icon,
.e-small .e-filled.e-input-group.e-control-wrapper .e-input-group-icon {
  font-size: 12px;
  min-height: 32px;
  min-width: 32px;
  padding: 0 0 0 4px;
}

.e-rtl.e-filled.e-input-group.e-small .e-input-group-icon,
.e-rtl.e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-rtl.e-filled.e-input-group .e-input-group-icon,
.e-small .e-rtl.e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-small .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small.e-rtl .e-filled.e-input-group .e-input-group-icon,
.e-small.e-rtl .e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl.e-filled.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small .e-rtl.e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small .e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small.e-rtl .e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  padding: 0 4px 0 0;
}

.e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon,
.e-filled.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small .e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small .e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 0;
  padding: 8px;
  padding: 0;
  margin: 9px 12px 9px 0;
}

.e-filled.e-float-input.e-input-group.e-small .e-input-group-icon,
.e-small .e-filled.e-float-input.e-input-group .e-input-group-icon,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon,
.e-small .e-filled.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  font-size: 12px;
  padding: 0 0 0 4px;
}

.e-filled.e-float-input .e-clear-icon,
.e-filled.e-float-input.e-control-wrapper .e-clear-icon,
.e-filled.e-input-group .e-clear-icon,
.e-filled.e-input-group.e-control-wrapper .e-clear-icon {
  font-size: 14px;
  padding: 0;
}

.e-filled.e-input-group .e-clear-icon,
.e-filled.e-input-group.e-control-wrapper .e-clear-icon {
  min-height: 32px;
  min-width: 32px;
  padding: 0;
}

.e-filled.e-float-input.e-input-group .e-clear-icon,
.e-filled.e-float-input.e-input-group.e-control-wrapper .e-clear-icon {
  padding: 0;
  margin: 2px;
  height: 32px;
}

.e-filled.e-input-group.e-small .e-clear-icon,
.e-filled.e-input-group .e-clear-icon.e-small,
.e-small .e-filled.e-input-group .e-clear-icon,
.e-filled.e-input-group.e-control-wrapper.e-small .e-clear-icon,
.e-filled.e-input-group.e-control-wrapper .e-clear-icon.e-small,
.e-small .e-filled.e-input-group.e-control-wrapper .e-clear-icon {
  font-size: 12px;
  min-height: 32px;
  min-width: 32px;
  padding: 0;
}

.e-rtl.e-filled.e-float-input.e-input-group.e-small .e-clear-icon,
.e-small .e-rtl.e-filled.e-float-input.e-input-group .e-clear-icon,
.e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-clear-icon,
.e-small .e-rtl.e-filled.e-float-input.e-control-wrapper.e-input-group .e-clear-icon,
.e-rtl .e-filled.e-float-input.e-input-group.e-small .e-clear-icon,
.e-small.e-rtl .e-filled.e-float-input.e-input-group .e-clear-icon,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group.e-small .e-clear-icon,
.e-small.e-rtl .e-filled.e-float-input.e-control-wrapper.e-input-group .e-clear-icon {
  padding: 0;
}

.e-filled.e-float-input .e-clear-icon::before,
.e-filled.e-float-input.e-control-wrapper .e-clear-icon::before,
.e-filled.e-input-group .e-clear-icon::before,
.e-filled.e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 14px;
}

.e-filled.e-float-input.e-small .e-clear-icon::before,
.e-filled.e-float-input.e-control-wrapper.e-small .e-clear-icon::before,
.e-filled.e-input-group.e-small .e-clear-icon::before,
.e-filled.e-input-group.e-control-wrapper.e-small .e-clear-icon::before,
.e-filled.e-float-input.e-control-wrapper input.e-small:first-child ~ .e-clear-icon::before,
.e-small .e-filled.e-float-input.e-control-wrapper .e-clear-icon::before,
.e-filled.e-float-input input.e-small:first-child ~ .e-clear-icon::before,
.e-small .e-filled.e-float-input .e-clear-icon::before,
.e-small .e-filled.e-input-group .e-clear-icon::before,
.e-small .e-filled.e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 12px;
}

.e-filled.e-float-input .e-input-in-wrap input:focus ~ label.e-float-text,
.e-filled.e-float-input .e-input-in-wrap input:valid ~ label.e-float-text,
.e-filled.e-float-input .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input .e-input-in-wrap input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input .e-input-in-wrap input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input .e-input-in-wrap input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input label.e-float-text.e-label-top {
  top: -7px;
}

.e-filled.e-float-input.e-small .e-input-in-wrap input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small .e-input-in-wrap input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small .e-input-in-wrap input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small .e-input-in-wrap input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper .e-input-in-wrap input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap input[disabled] ~ label.e-label-top.e-float-text {
  top: 10px;
}

.e-filled.e-input-group.e-float-icon-left.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-filled.e-input-group.e-float-icon-left.e-input-focus.e-success:not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-filled.e-input-group.e-float-icon-left.e-input-focus.e-warning:not(.e-success):not(.e-error) .e-input-in-wrap,
.e-filled.e-input-group.e-float-icon-left.e-input-focus.e-error:not(.e-success):not(.e-warning) .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-input-focus.e-success:not(.e-warning):not(.e-error) .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-input-focus.e-warning:not(.e-success):not(.e-error) .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-input-focus.e-error:not(.e-success):not(.e-warning) .e-input-in-wrap {
  border-style: none;
  border-width: 0;
}

.e-filled.e-float-input .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  top: 8px;
}

.e-filled.e-float-input.e-small .e-input-in-wrap label.e-float-text,
.e-small .e-filled.e-float-input .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  top: 10px;
}

.e-filled.e-input-group:not(.e-float-input).e-float-icon-left > .e-input-group-icon,
.e-filled.e-input-group:not(.e-float-input).e-control-wrapper.e-float-icon-left > .e-input-group-icon {
  font-size: 14px;
  margin: 0;
  min-height: 16px;
  min-width: 16px;
  padding: 0;
}

.e-filled.e-input-group:not(.e-float-input).e-small.e-float-icon-left > .e-input-group-icon,
.e-filled.e-input-group:not(.e-float-input).e-float-icon-left > .e-input-group-icon.e-small,
.e-filled.e-input-group:not(.e-float-input).e-control-wrapper.e-small.e-float-icon-left > .e-input-group-icon,
.e-filled.e-input-group:not(.e-float-input).e-control-wrapper.e-float-icon-left > .e-input-group-icon.e-small,
.e-small .e-filled.e-input-group:not(.e-float-input).e-float-icon-left > .e-input-group-icon,
.e-small .e-filled.e-input-group:not(.e-float-input).e-control-wrapper.e-float-icon-left > .e-input-group-icon {
  font-size: 12px;
  margin: 0;
  min-height: 16px;
  min-width: 16px;
  padding: 0;
}

.e-filled.e-input-group.e-float-icon-left > .e-input-group-icon,
.e-filled.e-float-input.e-input-group.e-float-icon-left > .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left > .e-input-group-icon,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left > .e-input-group-icon {
  margin: 0;
  min-height: 16px;
  min-width: 16px;
  padding: 0;
}

.e-filled.e-input-group.e-small.e-float-icon-left > .e-input-group-icon,
.e-filled.e-input-group.e-float-icon-left > .e-input-group-icon.e-small,
.e-filled.e-input-group.e-control-wrapper.e-small.e-float-icon-left > .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left > .e-input-group-icon.e-small,
.e-small .e-filled.e-input-group.e-float-icon-left > .e-input-group-icon,
.e-small .e-filled.e-input-group.e-control-wrapper.e-float-icon-left > .e-input-group-icon,
.e-filled.e-float-input.e-input-group.e-small.e-float-icon-left > .e-input-group-icon,
.e-filled.e-float-input.e-input-group.e-float-icon-left > .e-input-group-icon.e-small,
.e-small .e-filled.e-float-input.e-input-group.e-float-icon-left > .e-input-group-icon,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-small.e-float-icon-left > .e-input-group-icon,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left > .e-input-group-icon.e-small,
.e-small .e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left > .e-input-group-icon {
  margin: 0;
  min-height: 16px;
  min-width: 16px;
  padding: 0;
}

.e-filled.e-float-input .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-small .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-input-focus) .e-input-in-wrap input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  left: 0;
}

.e-filled.e-input-group .e-input-group-icon:last-child,
.e-filled.e-input-group.e-small .e-input-group-icon:last-child,
.e-small .e-filled.e-input-group .e-input-group-icon:last-child,
.e-filled.e-input-group.e-control-wrapper .e-input-group-icon:last-child,
.e-filled.e-input-group.e-small.e-control-wrapper .e-input-group-icon:last-child,
.e-small .e-filled.e-input-group.e-control-wrapper .e-input-group-icon:last-child,
.e-filled.e-input-group .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-filled.e-input-group.e-small .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-small .e-filled.e-input-group .e-input-group-icon,
.e-small .e-filled.e-input-group.e-control-wrapper .e-input-group-icon {
  margin-right: 0;
}

.e-filled.e-input-group.e-rtl .e-input-group-icon:last-child,
.e-filled.e-input-group.e-small.e-rtl .e-input-group-icon:last-child,
.e-small .e-filled.e-input-group.e-rtl .e-input-group-icon:last-child,
.e-filled.e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:last-child,
.e-filled.e-input-group.e-small.e-control-wrapper.e-rtl .e-input-group-icon:last-child,
.e-small .e-filled.e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:last-child,
.e-rtl .e-filled.e-input-group .e-input-group-icon:last-child,
.e-rtl .e-filled.e-input-group.e-small .e-input-group-icon:last-child,
.e-rtl.e-small .e-filled.e-input-group .e-input-group-icon:last-child,
.e-rtl .e-filled.e-input-group.e-control-wrapper .e-input-group-icon:last-child,
.e-rtl .e-filled.e-input-group.e-small.e-control-wrapper .e-input-group-icon:last-child,
.e-rtl.e-small .e-filled.e-input-group.e-control-wrapper .e-input-group-icon:last-child {
  margin-left: 0;
}

.e-filled.e-rtl.e-input-group .e-input-group-icon,
.e-filled.e-rtl.e-input-group.e-control-wrapper .e-input-group-icon,
.e-rtl .e-filled.e-input-group .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-control-wrapper .e-input-group-icon,
.e-filled.e-input-group.e-small.e-rtl .e-input-group-icon,
.e-filled.e-input-group.e-control-wrapper.e-small.e-rtl .e-input-group-icon,
.e-small .e-filled.e-input-group.e-rtl .e-input-group-icon,
.e-small .e-filled.e-input-group.e-control-wrapper.e-rtl .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-small .e-input-group-icon,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-small .e-input-group-icon,
.e-rtl.e-small .e-filled.e-input-group .e-input-group-icon,
.e-rtl.e-small .e-filled.e-input-group.e-control-wrapper .e-input-group-icon {
  margin-left: 0;
  margin-right: 0;
}

.e-filled textarea.e-input,
.e-filled.e-input-group.e-multi-line-input textarea,
.e-filled.e-input-group.e-control-wrapper.e-multi-line-input textarea,
.e-filled.e-float-input.e-multi-line-input textarea,
.e-filled.e-float-input.e-control-wrapper.e-multi-line-input textarea {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.e-filled textarea.e-input,
.e-filled.e-input-group textarea,
.e-filled.e-input-group textarea.e-input,
.e-filled.e-input-group.e-input-focus textarea,
.e-filled.e-input-group.e-input-focus textarea.e-input,
.e-filled.e-input-group.e-control-wrapper textarea,
.e-filled.e-input-group.e-control-wrapper.e-input-focus textarea,
.e-filled.e-input-group.e-control-wrapper textarea.e-input,
.e-filled.e-input-group.e-control-wrapper.e-input-focus textarea.e-input {
  padding: 0 12px 9px;
}

.e-filled.e-float-input textarea,
.e-filled.e-float-input.e-control-wrapper textarea {
  padding: 4px 12px 5px;
}

.e-filled.e-input-group.e-small textarea,
.e-filled.e-input-group.e-small textarea.e-input,
.e-filled.e-input-group textarea.e-small,
.e-filled.e-input-group textarea.e-input.e-small,
.e-filled.e-input-group.e-control-wrapper.e-small textarea,
.e-filled.e-input-group.e-control-wrapper.e-small textarea.e-input,
.e-small .e-filled.e-input-group textarea,
.e-small .e-filled.e-input-group textarea.e-input,
.e-filled.e-input-group.e-input-focus.e-small textarea,
.e-filled.e-input-group.e-input-focus.e-small textarea.e-input,
.e-filled.e-input-group.e-input-focus textarea.e-small,
.e-filled.e-input-group.e-input-focus textarea.e-input.e-small,
.e-filled.e-input-group.e-input-focus textarea.e-input.e-small,
.e-filled.e-input-group.e-control-wrapper.e-input-focus.e-small textarea,
.e-filled.e-input-group.e-control-wrapper.e-input-focus.e-small textarea.e-input,
.e-small .e-filled.e-input-group.e-input-focus textarea,
.e-small .e-filled.e-input-group.e-input-focus textarea.e-input {
  padding: 0 8px 7px;
}

.e-filled.e-float-input.e-small textarea,
.e-filled.e-float-input textarea.e-small,
.e-filled.e-float-input.e-control-wrapper.e-small textarea,
.e-filled.e-float-input.e-control-wrapper textarea.e-small,
.e-small .e-filled.e-float-input textarea,
.e-small .e-filled.e-float-input.e-control-wrapper textarea {
  padding: 0 8px 4px;
}

.e-filled.e-input-group.e-multi-line-input,
.e-filled.e-input-group.e-control-wrapper.e-multi-line-input {
  padding: 10px 0 0;
}

.e-filled.e-input-group.e-small.e-multi-line-input,
.e-filled.e-input-group.e-control-wrapper.e-small.e-multi-line-input,
.e-small .e-filled.e-input-group.e-control-wrapper.e-multi-line-input {
  padding: 7px 0 0;
}

.e-filled.e-float-input.e-multi-line-input,
.e-filled.e-float-input.e-control-wrapper.e-multi-line-input {
  padding: 14px 0 0;
}

.e-filled.e-float-input.e-small.e-multi-line-input,
.e-filled.e-float-input.e-control-wrapper.e-small.e-multi-line-input,
.e-small .e-filled.e-float-input.e-control-wrapper.e-multi-line-input {
  padding: 12px 0 0;
}

.e-filled textarea.e-input.e-rtl,
.e-filled.e-input-group.e-multi-line-input.e-rtl textarea.e-input,
.e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-input,
.e-rtl .e-filled.e-input-group.e-multi-line-input textarea.e-input,
.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper textarea.e-input,
.e-filled.e-input-group.e-multi-line-input.e-rtl textarea.e-input,
.e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-input,
.e-rtl .e-filled.e-input-group.e-multi-line-input textarea.e-input,
.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper textarea.e-input,
.e-filled.e-input-group.e-multi-line-input.e-rtl textarea.e-input:focus,
.e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-input:focus,
.e-rtl .e-filled.e-input-group.e-multi-line-input textarea.e-input:focus,
.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper textarea.e-input:focus {
  padding: 0 12px 9px;
  text-indent: 0;
}

.e-filled.e-float-input.e-multi-line-input.e-rtl textarea,
.e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-rtl textarea,
.e-rtl .e-filled.e-float-input.e-multi-line-input textarea,
.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper textarea,
.e-filled.e-float-input.e-multi-line-input.e-rtl textarea,
.e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-rtl textarea,
.e-rtl .e-filled.e-float-input.e-multi-line-input textarea,
.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper textarea,
.e-filled.e-float-input.e-multi-line-input.e-rtl textarea:focus,
.e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-rtl textarea:focus,
.e-rtl .e-filled.e-float-input.e-multi-line-input textarea:focus,
.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper textarea:focus {
  padding: 4px 12px 5px;
  text-indent: 0;
}

.e-rtl .e-filled textarea.e-input.e-small,
.e-filled textarea.e-input.e-small.e-rtl,
.e-small.e-rtl .e-filled textarea.e-input,
.e-small .e-filled textarea.e-input.e-rtl,
.e-filled.e-input-group.e-multi-line-input.e-small.e-rtl textarea.e-input,
.e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-small.e-rtl textarea.e-input,
.e-rtl .e-filled.e-input-group.e-multi-line-input.e-small textarea.e-input,
.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-small textarea.e-input,
.e-filled.e-input-group.e-multi-line-input.e-rtl textarea.e-input.e-small,
.e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-input.e-small,
.e-rtl .e-filled.e-input-group.e-multi-line-input textarea.e-input.e-small,
.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper textarea.e-input.e-small,
.e-small .e-filled.e-input-group.e-multi-line-input.e-rtl textarea.e-input,
.e-small .e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-input,
.e-small.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper textarea.e-input,
.e-small.e-rtl .e-filled.e-input-group.e-multi-line-input textarea.e-input,
.e-small.e-rtl .e-filled.e-input-group.e-multi-line-input.e-control-wrapper textarea.e-input:focus,
.e-small.e-rtl .e-filled.e-input-group.e-multi-line-input textarea.e-input:focus,
.e-small .e-filled.e-input-group.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-input:focus,
.e-small .e-filled.e-input-group.e-multi-line-input.e-rtl textarea.e-input:focus {
  padding: 0 8px 7px;
  text-indent: 0;
}

.e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-small.e-rtl textarea,
.e-filled.e-float-input.e-multi-line-input.e-small.e-rtl textarea,
.e-rtl .e-filled.e-float-input.e-multi-line-input.e-small textarea,
.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-small textarea,
.e-filled.e-float-input.e-multi-line-input.e-rtl textarea.e-small,
.e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-rtl textarea.e-small,
.e-rtl .e-filled.e-float-input.e-multi-line-input textarea.e-small,
.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper textarea.e-small,
.e-small .e-filled.e-float-input.e-multi-line-input.e-rtl textarea,
.e-small .e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-rtl textarea,
.e-small.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper textarea,
.e-small.e-rtl .e-filled.e-float-input.e-multi-line-input textarea,
.e-small.e-rtl .e-filled.e-float-input.e-multi-line-input.e-control-wrapper textarea:focus,
.e-small.e-rtl .e-filled.e-float-input.e-multi-line-input textarea:focus,
.e-small .e-filled.e-float-input.e-multi-line-input.e-control-wrapper.e-rtl textarea:focus,
.e-small .e-filled.e-float-input.e-multi-line-input.e-rtl textarea:focus {
  padding: 0 8px 4px;
  text-indent: 0;
}

.e-filled.e-float-input.e-float-icon-left .e-input-in-wrap,
.e-filled.e-float-input.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-filled.e-input-group.e-float-icon-left .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left .e-input-in-wrap {
  border: 1px solid;
  border-width: 0;
  margin-left: 8px;
}

.e-rtl .e-filled.e-float-input.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-filled.e-float-input.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-filled.e-input-group.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-filled.e-input-group.e-control-wrapper.e-float-icon-left .e-input-in-wrap .e-filled.e-float-input.e-control-wrapper.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-filled.e-float-input.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-filled.e-input-group.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-rtl .e-input-in-wrap {
  margin-left: 0;
  margin-right: 8px;
}

.e-filled.e-float-input.e-float-icon-left.e-small .e-input-in-wrap,
.e-filled.e-float-input.e-control-wrapper.e-float-icon-left.e-small .e-input-in-wrap,
.e-filled.e-input-group.e-float-icon-left.e-small .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-small .e-input-in-wrap,
.e-small .e-filled.e-float-input.e-float-icon-left .e-input-in-wrap,
.e-small .e-filled.e-float-input.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-small .e-filled.e-input-group.e-float-icon-left .e-input-in-wrap,
.e-small .e-filled.e-input-group.e-control-wrapper.e-float-icon-left .e-input-in-wrap {
  margin-left: 4px;
}

.e-rtl.e-small .e-filled.e-float-input.e-float-icon-left .e-input-in-wrap,
.e-rtl.e-small .e-filled.e-float-input.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-rtl.e-small .e-filled.e-input-group.e-float-icon-left .e-input-in-wrap,
.e-rtl.e-small .e-filled.e-input-group.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-small.e-filled.e-float-input.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-small.e-filled.e-float-input.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-small.e-filled.e-input-group.e-float-icon-left .e-input-in-wrap,
.e-rtl .e-small.e-filled.e-input-group.e-control-wrapper.e-float-icon-left .e-input-in-wrap,
.e-small.e-filled.e-float-input.e-control-wrapper.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small.e-filled.e-float-input.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small.e-filled.e-input-group.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small .e-filled.e-float-input.e-control-wrapper.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small .e-filled.e-float-input.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small .e-filled.e-input-group.e-float-icon-left.e-rtl .e-input-in-wrap,
.e-small .e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-rtl .e-input-in-wrap {
  margin-left: 0;
  margin-right: 4px;
}

.e-filled.e-input-group.e-float-icon-left:not(.e-float-input)::before,
.e-filled.e-input-group.e-float-icon-left:not(.e-float-input)::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input)::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input)::after {
  content: "";
  bottom: -1px;
}

.e-filled.e-input-group.e-float-icon-left:not(.e-float-input).e-input-focus::before,
.e-filled.e-input-group.e-float-icon-left:not(.e-float-input).e-input-focus::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input).e-input-focus::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input).e-input-focus::after {
  content: "";
  bottom: -1px;
}

.e-filled.e-input-group.e-float-icon-left:not(.e-float-input).e-input-focus .e-input-in-wrap::before,
.e-filled.e-input-group.e-float-icon-left:not(.e-float-input).e-input-focus .e-input-in-wrap::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input).e-input-focus .e-input-in-wrap::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input).e-input-focus .e-input-in-wrap::after {
  width: 0;
}

.e-filled.e-input-group.e-float-icon-left,
.e-filled.e-input-group.e-success.e-float-icon-left,
.e-filled.e-input-group.e-warning.e-float-icon-left,
.e-filled.e-input-group.e-error.e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-success.e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-warning.e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-error.e-float-icon-left {
  border: 1px solid;
  border-width: 1px;
}

/*! input theme */
input.e-input,
.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group textarea.e-input,
.e-input-group.e-control-wrapper textarea.e-input,
textarea.e-input,
.e-float-input input,
.e-float-input.e-control-wrapper input,
.e-float-input textarea,
.e-float-input.e-control-wrapper textarea {
  background: var(--color-sf-content-bg-color);
  border-color: var(--color-sf-border);
  color: var(--color-sf-content-text-color);
}

.e-input-group,
.e-input-group.e-control-wrapper,
.e-float-input,
.e-float-input.e-input-group,
.e-float-input.e-control-wrapper,
.e-float-input.e-input-group.e-control-wrapper {
  background: var(--color-sf-content-bg-color);
  color: var(--color-sf-content-text-color);
}

.e-input-group .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input-group-icon {
  background: transparent;
  border-color: var(--color-sf-border);
  color: var(--color-sf-icon-color);
}

.e-input-group.e-disabled .e-input-group-icon,
.e-input-group.e-control-wrapper.e-disabled .e-input-group-icon,
.e-float-input.e-disabled .e-clear-icon,
.e-float-input.e-control-wrapper.e-disabled .e-clear-icon,
.e-input-group.e-disabled .e-clear-icon,
.e-input-group.e-control-wrapper.e-disabled .e-clear-icon {
  color: var(--color-sf-icon-color-disabled);
}

/* stylelint-disable property-no-vendor-prefix */
/* stylelint-disable selector-no-vendor-prefix */
.e-input[disabled],
.e-input-group .e-input[disabled],
.e-input-group.e-control-wrapper .e-input[disabled],
.e-input-group.e-disabled,
.e-input-group.e-control-wrapper.e-disabled,
.e-float-input input[disabled],
.e-float-input.e-control-wrapper input[disabled],
.e-float-input textarea[disabled],
.e-float-input.e-control-wrapper textarea[disabled],
.e-float-input.e-disabled,
.e-float-input.e-control-wrapper.e-disabled {
  -webkit-text-fill-color: var(--color-sf-content-text-color-disabled);
  background: var(--color-sf-content-bg-color-alt2);
  color: var(--color-sf-content-text-color-alt1);
  border-color: var(--color-sf-border);
}

.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input,
.e-float-input input,
.e-float-input.e-control-wrapper input,
.e-input-group textarea.e-input,
.e-input-group.e-control-wrapper textarea.e-input,
.e-float-input textarea,
.e-float-input.e-control-wrapper textarea,
.e-input-group .e-input[disabled],
.e-input-group.e-control-wrapper .e-input[disabled],
.e-input-group.e-disabled input.e-input,
.e-input-group.e-control-wrapper.e-disabled input.e-input,
.e-input-group.e-disabled textarea.e-input,
.e-input-group.e-control-wrapper.e-disabled textarea.e-input {
  background: none;
  color: inherit;
}

.e-input-group .e-input[readonly],
.e-input-group.e-control-wrapper .e-input[readonly],
.e-float-input input[readonly],
.e-float-input.e-control-wrapper input[readonly],
.e-float-input textarea[readonly],
.e-float-input.e-control-wrapper textarea[readonly] {
  background: var(--color-sf-content-bg-color-alt1);
  color: inherit;
}

.e-float-input.e-disabled input,
.e-float-input.e-control-wrapper.e-disabled input,
.e-float-input.e-disabled textarea,
.e-float-input.e-control-wrapper.e-disabled textarea,
.e-float-input input[disabled],
.e-float-input.e-control-wrapper input[disabled],
.e-float-input textarea[disabled],
.e-float-input.e-control-wrapper textarea[disabled] {
  color: inherit;
}

/*! Added color to textbox for disbaled state */
.e-float-input:not(.e-disabled) input[disabled],
.e-float-input.e-control-wrapper:not(.e-disabled) input[disabled],
.e-float-input:not(.e-disabled) textarea[disabled],
.e-float-input.e-control-wrapper:not(.e-disabled) textarea[disabled] {
  -webkit-text-fill-color: var(--color-sf-content-text-color-alt1);
  color: var(--color-sf-content-text-color-alt1);
}

.e-input-group.e-disabled .e-input-group-icon,
.e-input-group.e-control-wrapper.e-disabled .e-input-group-icon {
  background: transparent;
  border-color: var(--color-sf-border);
}

.e-input-group:not(.e-disabled) .e-input-group-icon:hover,
.e-input-group.e-control-wrapper:not(.e-disabled) .e-input-group-icon:hover {
  color: var(--color-sf-icon-color-hover);
}

.e-input.e-success,
.e-input.e-success:focus,
.e-input-group.e-success input.e-input,
.e-input-group.e-control-wrapper.e-success input.e-input,
.e-input-group.e-success .e-input-group-icon,
.e-input-group.e-control-wrapper.e-success .e-input-group-icon,
.e-input-group.e-success textarea.e-input,
.e-input-group.e-control-wrapper.e-success textarea.e-input {
  border-color: var(--color-sf-border-success);
}

.e-input.e-warning,
.e-input.e-warning:focus,
.e-input-group.e-warning input.e-input,
.e-input-group.e-control-wrapper.e-warning input.e-input,
.e-input-group.e-warning .e-input-group-icon,
.e-input-group.e-control-wrapper.e-warning .e-input-group-icon,
.e-input-group.e-warning textarea.e-input,
.e-input-group.e-control-wrapper.e-warning textarea.e-input {
  border-color: var(--color-sf-border-warning);
}

.e-input.e-error,
.e-input.e-error:focus,
.e-input-group.e-error input.e-input,
.e-input-group.e-control-wrapper.e-error input.e-input,
.e-input-group.e-control-wrapper.e-error .e-input-group-icon,
.e-input-group.e-error .e-input-group-icon,
.e-input-group.e-error textarea.e-input,
.e-input-group.e-control-wrapper.e-error textarea.e-input {
  border-color: var(--color-sf-border-error);
}

label.e-float-text,
.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-small:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  color: var(--color-sf-placeholder-text-color);
}

.e-float-input.e-error label.e-float-text,
.e-float-input.e-control-wrapper.e-error label.e-float-text,
.e-float-input.e-error input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text,
.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text {
  color: var(--color-sf-border-error);
}

.e-float-input input,
.e-float-input textarea,
.e-float-input.e-control-wrapper input,
.e-float-input.e-control-wrapper textarea,
.e-input-group,
.e-input-group.e-control-wrapper {
  border-bottom-color: var(--color-sf-border);
}

.e-float-input.e-success input,
.e-float-input.e-success textarea,
.e-float-input.e-control-wrapper.e-success input,
.e-float-input.e-control-wrapper.e-success textarea {
  border-color: var(--color-sf-border-success);
}

.e-float-input.e-warning input,
.e-float-input.e-control-wrapper.e-warning input,
.e-float-input.e-warning textarea,
.e-float-input.e-control-wrapper.e-warning textarea {
  border-color: var(--color-sf-border-warning);
}

.e-float-input.e-error input,
.e-float-input.e-control-wrapper.e-error input,
.e-float-input.e-error textarea,
.e-float-input.e-control-wrapper.e-error textarea {
  border-color: var(--color-sf-border-error);
}

input.e-input:-moz-placeholder,
input.e-input:-moz-placeholder,
textarea.e-input:-moz-placeholder,
input.e-input::-moz-placeholder,
textarea.e-input::-moz-placeholder,
.e-input-group input.e-input:-moz-placeholder,
.e-input-group input.e-input:-moz-placeholder,
.e-input-group.e-control-wrapper input.e-input:-moz-placeholder,
.e-input-group input.e-input:-moz-placeholder,
.e-input-group.e-control-wrapper input.e-input:-moz-placeholder,
.e-input-group textarea.e-input:-moz-placeholder,
.e-input-group.e-control-wrapper textarea.e-input:-moz-placeholder,
.e-input-group input.e-input::-moz-placeholder,
.e-input-group.e-control-wrapper input.e-input::-moz-placeholder,
.e-input-group textarea.e-input::-moz-placeholder,
.e-input-group.e-control-wrapper textarea.e-input::-moz-placeholder {
  color: var(--color-sf-placeholder-text-color);
}

input.e-input::-webkit-input-placeholder,
textarea.e-input::-webkit-input-placeholder,
.e-input-group input.e-input::-webkit-input-placeholder,
.e-input-group textarea.e-input::-webkit-input-placeholder,
.e-input-group.e-control-wrapper input.e-input::-webkit-input-placeholder,
.e-input-group.e-control-wrapper textarea.e-input::-webkit-input-placeholder {
  color: var(--color-sf-placeholder-text-color);
}

input.e-input:-ms-input-placeholder,
textarea.e-input:-ms-input-placeholder,
.e-input-group input.e-input:-ms-input-placeholder,
.e-input-group.e-control-wrapper input.e-input:-ms-input-placeholder,
.e-input-group.e-control-wrapper textarea.e-input:-ms-input-placeholder,
.e-input-group textarea.e-input:-ms-input-placeholder {
  color: var(--color-sf-placeholder-text-color);
}

.e-float-input:not(.e-error):not(.e-input-focus):not(.e-disabled) input:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-float-input:not(.e-error):not(.e-input-focus) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus):not(.e-disabled) input:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-float-input:not(.e-input-focus):not(.e-disabled) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-float-input:not(.e-input-focus) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-float-input.e-control-wrapper:not(.e-input-focus):not(.e-disabled) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top) {
  color: var(--color-sf-placeholder-text-color);
}

.e-float-input.e-error:not(.e-input-group) .e-float-line::before,
.e-float-input.e-error:not(.e-input-group) .e-float-line::after,
.e-float-input.e-error:not(.e-input-group) .e-float-line::before,
.e-float-input.e-error:not(.e-input-group) .e-float-line::after,
.e-float-input.e-control-wrapper.e-error:not(.e-input-group) .e-float-line::before,
.e-float-input.e-control-wrapper.e-error:not(.e-input-group) .e-float-line::after,
.e-float-input.e-control-wrapper.e-error:not(.e-input-group) .e-float-line::before,
.e-float-input.e-control-wrapper.e-error:not(.e-input-group) .e-float-line::after {
  background: var(--color-sf-border-error);
}

.e-input-group:not(.e-disabled) .e-input-group-icon:active,
.e-input-group.e-control-wrapper:not(.e-disabled) .e-input-group-icon:active {
  color: var(--color-sf-icon-color-pressed);
}

input.e-input::-moz-selection, textarea.e-input::-moz-selection, .e-input-group input.e-input::-moz-selection, .e-input-group.e-control-wrapper input.e-input::-moz-selection, .e-float-input input::-moz-selection, .e-float-input.e-control-wrapper input::-moz-selection, .e-input-group textarea.e-input::-moz-selection, .e-input-group.e-control-wrapper textarea.e-input::-moz-selection, .e-float-input textarea::-moz-selection, .e-float-input.e-control-wrapper textarea::-moz-selection, .e-float-input.e-small textarea::-moz-selection, .e-float-input textarea::-moz-selection {
  background: var(--color-sf-primary);
  color: var(--color-sf-primary-text-color);
}

input.e-input::selection,
textarea.e-input::selection,
.e-input-group input.e-input::selection,
.e-input-group.e-control-wrapper input.e-input::selection,
.e-float-input input::selection,
.e-float-input.e-control-wrapper input::selection,
.e-input-group textarea.e-input::selection,
.e-input-group.e-control-wrapper textarea.e-input::selection,
.e-float-input textarea::selection,
.e-float-input.e-control-wrapper textarea::selection,
.e-float-input.e-small textarea::selection,
.e-float-input textarea::selection {
  background: var(--color-sf-primary);
  color: var(--color-sf-primary-text-color);
}

.e-input-group:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input) .e-input-in-wrap::after,
.e-float-input.e-input-group:not(.e-float-icon-left) .e-float-line::before,
.e-float-input.e-input-group:not(.e-float-icon-left) .e-float-line::after,
.e-float-input.e-input-group.e-float-icon-left .e-input-in-wrap .e-float-line::before,
.e-float-input.e-input-group.e-float-icon-left .e-input-in-wrap .e-float-line::after,
.e-float-input.e-control-wrapper.e-input-group:not(.e-float-icon-left) .e-float-line::before,
.e-float-input.e-control-wrapper.e-input-group:not(.e-float-icon-left) .e-float-line::after,
.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left .e-input-in-wrap .e-float-line::before,
.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left .e-input-in-wrap .e-float-line::after,
.e-filled.e-input-group.e-float-icon-left:not(.e-float-input)::before,
.e-filled.e-input-group.e-float-icon-left:not(.e-float-input)::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input)::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-float-input)::after,
.e-filled.e-float-input.e-input-group.e-float-icon-left .e-float-line::before,
.e-filled.e-float-input.e-input-group.e-float-icon-left .e-float-line::after,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left .e-float-line::before,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left .e-float-line::after {
  content: "";
}

.e-input-group::before,
.e-input-group::after,
.e-input-group.e-control-wrapper::before,
.e-input-group.e-control-wrapper::after {
  content: "";
}

.e-input-group:not(.e-float-icon-left):not(.e-float-input).e-success::before,
.e-input-group:not(.e-float-icon-left):not(.e-float-input).e-success::after,
.e-input-group.e-float-icon-left.e-success:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left.e-success:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input).e-success::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input).e-success::after,
.e-input-group.e-control-wrapper.e-float-icon-left.e-success:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left.e-success:not(.e-float-input) .e-input-in-wrap::after,
.e-float-input.e-input-group:not(.e-float-icon-left).e-success .e-float-line::before,
.e-float-input.e-input-group:not(.e-float-icon-left).e-success .e-float-line::after,
.e-float-input.e-input-group.e-float-icon-left.e-success .e-input-in-wrap .e-float-line::before,
.e-float-input.e-input-group.e-float-icon-left.e-success .e-input-in-wrap .e-float-line::after,
.e-float-input.e-control-wrapper.e-input-group:not(.e-float-icon-left).e-success .e-float-line::before,
.e-float-input.e-control-wrapper.e-input-group:not(.e-float-icon-left).e-success .e-float-line::after,
.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-success .e-input-in-wrap .e-float-line::before,
.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-success .e-input-in-wrap .e-float-line::after,
.e-float-input.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-success .e-float-line::before,
.e-float-input.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-success .e-float-line::after,
.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-success .e-input-in-wrap .e-float-line::before,
.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-success .e-input-in-wrap .e-float-line::after,
.e-filled.e-input-group.e-float-icon-left.e-success:not(.e-float-input)::before,
.e-filled.e-input-group.e-float-icon-left.e-success:not(.e-float-input)::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-success:not(.e-float-input)::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-success:not(.e-float-input)::after,
.e-filled.e-float-input.e-input-group.e-float-icon-left.e-success .e-float-line::before,
.e-filled.e-float-input.e-input-group.e-float-icon-left.e-success .e-float-line::after,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-success .e-float-line::before,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-success .e-float-line::after,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-success .e-float-line::before,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-success .e-float-line::after {
  content: "";
}

.e-input-group.e-success::before,
.e-input-group.e-success::after,
.e-input-group.e-control-wrapper.e-success::before,
.e-input-group.e-control-wrapper.e-success::after {
  content: "";
}

.e-input-group:not(.e-float-icon-left).e-warning:not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left).e-warning:not(.e-float-input)::after,
.e-input-group.e-float-icon-left.e-warning:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left.e-warning:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-warning:not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-warning:not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left.e-warning:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left.e-warning:not(.e-float-input) .e-input-in-wrap::after,
.e-float-input.e-input-group:not(.e-float-icon-left).e-warning .e-float-line::before,
.e-float-input.e-input-group:not(.e-float-icon-left).e-warning .e-float-line::after,
.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-warning .e-input-in-wrap .e-float-line::before,
.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-warning .e-input-in-wrap .e-float-line::after,
.e-filled.e-input-group.e-float-icon-left.e-warning:not(.e-float-input)::before,
.e-filled.e-input-group.e-float-icon-left.e-warning:not(.e-float-input)::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-warning:not(.e-float-input)::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-warning:not(.e-float-input)::after,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-warning .e-float-line::before,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-float-icon-left.e-warning .e-float-line::after {
  content: "";
}

.e-input-group.e-warning::before,
.e-input-group.e-warning::after,
.e-input-group.e-control-wrapper.e-warning::before,
.e-input-group.e-control-wrapper.e-warning::after {
  content: "";
}

.e-input-group:not(.e-float-icon-left).e-error:not(.e-float-input)::before,
.e-input-group:not(.e-float-icon-left).e-error:not(.e-float-input)::after,
.e-input-group.e-float-icon-left.e-error:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-float-icon-left.e-error:not(.e-float-input) .e-input-in-wrap::after,
.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-error:not(.e-float-input)::before,
.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-error:not(.e-float-input)::after,
.e-input-group.e-control-wrapper.e-float-icon-left.e-error:not(.e-float-input) .e-input-in-wrap::before,
.e-input-group.e-control-wrapper.e-float-icon-left.e-error:not(.e-float-input) .e-input-in-wrap::after,
.e-float-input.e-input-group:not(.e-float-icon-left).e-error .e-float-line::before,
.e-float-input.e-input-group:not(.e-float-icon-left).e-error .e-float-line::after,
.e-float-input.e-input-group.e-float-icon-left.e-error .e-input-in-wrap .e-float-line::before,
.e-float-input.e-input-group.e-float-icon-left.e-error .e-input-in-wrap .e-float-line::after,
.e-float-input.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-error .e-float-line::before,
.e-float-input.e-input-group.e-control-wrapper:not(.e-float-icon-left).e-error .e-float-line::after,
.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-error .e-input-in-wrap .e-float-line::before,
.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-error .e-input-in-wrap .e-float-line::after,
.e-filled.e-input-group.e-float-icon-left.e-error:not(.e-float-input)::before,
.e-filled.e-input-group.e-float-icon-left.e-error:not(.e-float-input)::after,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-error:not(.e-float-input)::before,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left.e-error:not(.e-float-input)::after,
.e-filled.e-float-input.e-input-group.e-float-icon-left.e-error .e-float-line::before,
.e-filled.e-float-input.e-input-group.e-float-icon-left.e-error .e-float-line::after,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-error .e-float-line::before,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-float-icon-left.e-error .e-float-line::after {
  content: "";
}

.e-input-group.e-error::before,
.e-input-group.e-error::after,
.e-input-group.e-control-wrapper.e-error::before,
.e-input-group.e-control-wrapper.e-error::after {
  content: "";
}

.e-input-group.e-success .e-input-group-icon,
.e-input-group.e-control-wrapper.e-success .e-input-group-icon,
.e-input-group.e-warning .e-input-group-icon,
.e-input-group.e-control-wrapper.e-warning .e-input-group-icon,
.e-input-group.e-error .e-input-group-icon,
.e-input-group.e-control-wrapper.e-error .e-input-group-icon {
  color: var(--color-sf-icon-color);
}

.e-input-group.e-success:not(.e-disabled):not(:active) .e-input-group-icon:hover,
.e-input-group.e-control-wrapper.e-success:not(.e-disabled):not(:active) .e-input-group-icon:hover,
.e-input-group.e-warning:not(.e-disabled):not(:active) .e-input-group-icon:hover,
.e-input-group.e-control-wrapper.e-warning:not(.e-disabled):not(:active) .e-input-group-icon:hover,
.e-input-group.e-error:not(.e-disabled):not(:active) .e-input-group-icon:hover,
.e-input-group.e-control-wrapper.e-error:not(.e-disabled):not(:active) .e-input-group-icon:hover {
  color: var(--color-sf-icon-color);
}

.e-input-group.e-success:not(.e-disabled) .e-input-group-icon:active,
.e-input-group.e-control-wrapper.e-success:not(.e-disabled) .e-input-group-icon:active,
.e-input-group.e-warning:not(.e-disabled) .e-input-group-icon:active,
.e-input-group.e-control-wrapper.e-warning:not(.e-disabled) .e-input-group-icon:active,
.e-input-group.e-error:not(.e-disabled) .e-input-group-icon:active,
.e-input-group.e-control-wrapper.e-error:not(.e-disabled) .e-input-group-icon:active {
  color: var(--color-sf-icon-color);
}

.e-input-group input.e-input,
.e-input-group.e-control-wrapper input.e-input,
.e-input-group textarea.e-input,
.e-input-group.e-control-wrapper textarea.e-input,
.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus,
.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input {
  border-color: var(--color-sf-border);
}

.e-input:focus:not(.e-success):not(.e-warning):not(.e-error),
.e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) input:focus,
.e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) textarea:focus,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) input:focus,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) textarea:focus,
.e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group).e-input-focus input,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group).e-input-focus input {
  border-color: var(--color-sf-primary);
  border-radius: 4px;
  -webkit-box-shadow: 0 0 0 1px var(--color-sf-primary);
          box-shadow: 0 0 0 1px var(--color-sf-primary);
}

.e-input-group .e-input:focus:not(.e-success):not(.e-warning):not(.e-error),
.e-input-group .e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) input:focus,
.e-input-group .e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) textarea:focus,
.e-input-group .e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) input:focus,
.e-input-group .e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) textarea:focus,
.e-input-group .e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group).e-input-focus input,
.e-input-group .e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group).e-input-focus input {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error) input.e-input:focus,
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) input.e-input:focus,
.e-input-group:not(.e-success):not(.e-warning):not(.e-error).e-input-focus input.e-input,
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error).e-input-focus input.e-input,
.e-input-group:not(.e-success):not(.e-warning):not(.e-error) textarea.e-input:focus,
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) textarea.e-input:focus {
  border-color: var(--color-sf-border);
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error),
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: var(--color-sf-primary);
  border-radius: 4px;
  -webkit-box-shadow: 0 0 0 1px var(--color-sf-primary);
          box-shadow: 0 0 0 1px var(--color-sf-primary);
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus,
.e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:last-child.e-input-group-icon,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus,
.e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:last-child.e-input-group-icon {
  border-color: transparent;
}

.e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon,
.e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon,
.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus,
.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus,
.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon,
.e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon,
.e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon,
.e-input-focus.e-control-wrapper.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus,
.e-input-focus.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon {
  border-color: transparent;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon,
.e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus,
.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon,
.e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon {
  border-color: transparent;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error),
.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: var(--color-sf-border);
  -webkit-box-shadow: 0 1px 2px 0 rgba(var(--color-sf-black), 0.05);
          box-shadow: 0 1px 2px 0 rgba(var(--color-sf-black), 0.05);
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus,
.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input {
  border-bottom-color: transparent;
  border-top-color: transparent;
}

.e-input-group.e-success,
.e-input-group.e-control-wrapper.e-success,
.e-input-group.e-success:not(.e-float-icon-left),
.e-input-group.e-control-wrapper.e-success:not(.e-float-icon-left) {
  border-color: var(--color-sf-border-success);
}

.e-input-group.e-success.e-input-focus,
.e-input-group.e-control-wrapper.e-success.e-input-focus,
.e-input-group.e-success:not(.e-float-icon-left).e-input-focus,
.e-input-group.e-control-wrapper.e-success:not(.e-float-icon-left).e-input-focus {
  -webkit-box-shadow: 0 0 0 1px var(--color-sf-success);
          box-shadow: 0 0 0 1px var(--color-sf-success);
}

.e-input-group.e-warning,
.e-input-group.e-control-wrapper.e-warning,
.e-input-group.e-warning:not(.e-float-icon-left),
.e-input-group.e-control-wrapper.e-warning:not(.e-float-icon-left) {
  border-color: var(--color-sf-border-warning);
}

.e-input-group.e-warning.e-input-focus,
.e-input-group.e-control-wrapper.e-warning.e-input-focus,
.e-input-group.e-warning:not(.e-float-icon-left).e-input-focus,
.e-input-group.e-control-wrapper.e-warning:not(.e-float-icon-left).e-input-focus {
  -webkit-box-shadow: 0 0 0 1px var(--color-sf-warning);
          box-shadow: 0 0 0 1px var(--color-sf-warning);
}

.e-input-group.e-error,
.e-input-group.e-control-wrapper.e-error,
.e-input-group.e-error:not(.e-float-icon-left),
.e-input-group.e-control-wrapper.e-error:not(.e-float-icon-left) {
  border-color: var(--color-sf-border-error);
}

.e-input-group.e-error.e-input-focus,
.e-input-group.e-control-wrapper.e-error.e-input-focus,
.e-input-group.e-error:not(.e-float-icon-left).e-input-focus,
.e-input-group.e-control-wrapper.e-error:not(.e-float-icon-left).e-input-focus {
  -webkit-box-shadow: 0 0 0 1px var(--color-sf-danger);
          box-shadow: 0 0 0 1px var(--color-sf-danger);
}

.e-float-input .e-clear-icon,
.e-float-input.e-control-wrapper .e-clear-icon,
.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  color: var(--color-sf-icon-color);
}

.e-float-input .e-clear-icon:hover,
.e-float-input.e-control-wrapper .e-clear-icon:hover,
.e-input-group .e-clear-icon:hover,
.e-input-group.e-control-wrapper .e-clear-icon:hover {
  color: var(--color-sf-icon-color-hover);
}

.e-float-input.e-input-focus .e-input:focus,
.e-float-input.e-control-wrapper.e-input-focus .e-input:focus {
  border-bottom-color: transparent;
  border-top-color: transparent;
}

.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input:not(.e-error) input:valid ~ label.e-float-text,
.e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) input label.e-float-text.e-label-top,
.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) input:valid ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error) input[readonly] ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) input:valid ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-small .e-float-input:not(.e-error) input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-float-input:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-float-input.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input[readonly] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input:not(.e-error) textarea:valid ~ label.e-float-text,
.e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) textarea label.e-float-text.e-label-top,
.e-float-input.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) textarea:valid ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error) textarea[readonly] ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) textarea:valid ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-small .e-float-input:not(.e-error) textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-float-input:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-autofill:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input.e-control-wrapper.e-autofill:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text {
  color: var(--color-sf-content-text-color-alt1);
}

.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input .e-control-wrapper:not(.e-error) input label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea label.e-float-text.e-label-top {
  color: var(--color-sf-content-text-color);
}

.e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  color: var(--color-sf-placeholder-text-color);
}

.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:valid ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[readonly] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top {
  color: var(--color-sf-content-text-color-alt1);
}

.e-float-input.e-input-group.e-disabled .e-float-text,
.e-float-input.e-input-group.e-disabled .e-float-text.e-label-top,
.e-float-input input[disabled] ~ label.e-float-text,
.e-float-input input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-disabled label.e-float-text,
.e-float-input.e-disabled label.e-float-text.e-label-top,
.e-float-input:not(.e-error) input[disabled] ~ label.e-float-text,
.e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-float-input textarea[disabled] ~ label.e-float-text,
.e-float-input textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-input-group.e-disabled .e-float-text,
.e-float-input.e-control-wrapper.e-input-group.e-disabled .e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-disabled input[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper input[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-disabled label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-disabled:not(.e-error) input[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-disabled textarea[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-disabled:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top {
  color: var(--color-sf-content-text-color-alt3);
}

.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input:not(.e-error) input[readonly]:focus ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input[readonly]:focus ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error) input[readonly]:focus ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) input[readonly]:focus ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input[readonly]:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input[readonly]:focus ~ label.e-float-text.e-label-top,
.e-float-input:not(.e-error).e-input-focus input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text,
.e-small .e-float-input:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text.e-label-top,
.e-float-input.e-control-wrapper.e-small:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text.e-label-top,
.e-float-input:not(.e-error) textarea[readonly]:focus ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea[readonly]:focus ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error) textarea[readonly]:focus ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) textarea[readonly]:focus ~ label.e-float-text.e-label-top,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea[readonly]:focus ~ label.e-float-text.e-label-top,
.e-float-input:not(.e-error).e-input-focus textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error).e-input-focus textarea[readonly] ~ label.e-label-top.e-float-text,
.e-float-input.e-small:not(.e-error).e-input-focus textarea[readonly] ~ label.e-float-text,
.e-small .e-float-input:not(.e-error).e-input-focus textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-float-input.e-control-wrapper:not(.e-error).e-input-focus textarea[readonly] ~ label.e-float-text.e-label-top,
.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text {
  color: rgba(var(--color-sf-primary));
}

.e-input-group.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-input-group.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-float-input.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-float-input.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]),
.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]),
.e-float-input.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]),
.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]) {
  border-color: var(--color-sf-primary);
}

.e-underline.e-input-group:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-input-group.e-control-wrapper:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input.e-control-wrapper:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-input-group.e-float-icon-left:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-input-group.e-control-wrapper.e-float-icon-left:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input.e-float-icon-left:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input.e-control-wrapper.e-float-icon-left:not(.e-input-focus):hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input:hover:not(.e-input-focus):not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) {
  border-color: var(--color-sf-border-dark);
  color: rgba(var(--color-sf-content-text-color));
}

.e-underline.e-input-group:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-input-group.e-control-wrapper:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input.e-control-wrapper:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-input-group.e-float-icon-left:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-input-group.e-control-wrapper.e-float-icon-left:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input.e-float-icon-left:hover:not(.e-input-focus):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input.e-control-wrapper.e-float-icon-left:not(.e-input-focus):hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-underline.e-float-input:hover:not(.e-input-focus):not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) {
  border-bottom-color: var(--color-sf-border-hover);
  color: rgba(var(--color-sf-content-text-color));
}

.e-filled.e-input-group,
.e-filled.e-input-group.e-control-wrapper,
.e-filled.e-float-input,
.e-filled.e-float-input.e-input-group,
.e-filled.e-float-input.e-control-wrapper,
.e-filled.e-float-input.e-input-group.e-control-wrapper {
  background: var(--color-sf-content-bg-color-alt1);
  -webkit-transition: opacity 15ms linear, background-color 15ms linear;
  transition: opacity 15ms linear, background-color 15ms linear;
}

.e-filled.e-input-group:hover,
.e-filled.e-input-group.e-control-wrapper:hover,
.e-filled.e-float-input:hover,
.e-filled.e-float-input.e-input-group:hover,
.e-filled.e-float-input.e-control-wrapper:hover,
.e-filled.e-float-input.e-input-group.e-control-wrapper:hover {
  background: var(--color-sf-content-bg-color-alt2);
  -webkit-transition: opacity 15ms linear, background-color 15ms linear;
  transition: opacity 15ms linear, background-color 15ms linear;
}

.e-filled.e-input-group.e-input-focus,
.e-filled.e-input-group.e-control-wrapper.e-input-focus,
.e-filled.e-float-input.e-input-focus,
.e-filled.e-float-input.e-input-group.e-input-focus,
.e-filled.e-float-input.e-control-wrapper.e-input-focus,
.e-filled.e-float-input.e-input-group.e-control-wrapper.e-input-focus,
.e-filled.e-input-group:hover.e-input-focus,
.e-filled.e-input-group:hover.e-control-wrapper.e-input-focus,
.e-filled.e-float-input:hover.e-input-focus,
.e-filled.e-float-input:hover.e-input-group.e-input-focus,
.e-filled.e-float-input:hover.e-control-wrapper.e-input-focus,
.e-filled.e-float-input:hover.e-input-group.e-control-wrapper.e-input-focus {
  background: var(--color-sf-content-bg-color-alt2);
  -webkit-transition: opacity 15ms linear, background-color 15ms linear;
  transition: opacity 15ms linear, background-color 15ms linear;
}

.e-filled.e-input-group:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-input-group.e-control-wrapper:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-filled.e-input-group.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-input-group.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-filled.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-filled.e-input-group.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-input-group.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled):not(.e-float-icon-left),
.e-filled.e-float-input.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-filled.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-filled.e-input-group:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-float-input.e-control-wrapper:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-float-input:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-input-group.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-float-input.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-input-group.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-input-group.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-float-input.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left,
.e-filled.e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled).e-float-icon-left {
  border-color: rgba(var(--color-sf-content-text-color));
}

.e-filled.e-input-group.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error),
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error),
.e-filled.e-float-input:not(.e-success):not(.e-warning):not(.e-error),
.e-filled.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: var(--color-sf-border);
}

.e-filled.e-float-input.e-success,
.e-filled.e-float-input.e-control-wrapper.e-success,
.e-filled.e-input-group.e-float-icon-left.e-success,
.e-filled.e-input-group.e-float-icon-left.e-control-wrapper.e-success {
  border-color: var(--color-sf-border-success);
}

.e-filled.e-float-input.e-warning,
.e-filled.e-float-input.e-control-wrapper.e-warning,
.e-filled.e-input-group.e-float-icon-left.e-warning,
.e-filled.e-input-group.e-float-icon-left.e-control-wrapper.e-warning {
  border-color: var(--color-sf-border-warning);
}

.e-filled.e-float-input.e-error,
.e-filled.e-float-input.e-control-wrapper.e-error,
.e-filled.e-input-group.e-float-icon-left.e-error,
.e-filled.e-input-group.e-float-icon-left.e-control-wrapper.e-error {
  border-color: var(--color-sf-border-error);
}

.e-filled label.e-float-text,
.e-filled.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error):not(.e-input-focus) input:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) input:valid ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) input label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-input-group.e-filled textarea.e-input:-ms-input-placeholder,
input.e-filled.e-input::-webkit-input-placeholder,
.e-input-group.e-filled.e-control-wrapper input.e-input::-webkit-input-placeholder,
.e-input-group.e-filled input.e-input::-webkit-input-placeholder,
textarea.e-filled.e-input::-webkit-input-placeholder,
.e-input-group.e-filled textarea.e-input::-webkit-input-placeholder,
.e-input-group.e-filled.e-control-wrapper textarea.e-input::-webkit-input-placeholder,
.e-filled.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-error):not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
input.e-filled.e-input:-moz-placeholder,
.e-input-group.e-filled input.e-input:-moz-placeholder,
.e-input-group.e-filled input.e-input:-moz-placeholder,
.e-input-group.e-filled.e-control-wrapper input.e-input:-moz-placeholder,
input.e-filled.e-input:-moz-placeholder,
.e-input-group.e-filled input.e-input:-moz-placeholder,
.e-input-group.e-filled.e-control-wrapper input.e-input:-moz-placeholder,
textarea.e-filled.e-input:-moz-placeholder,
.e-input-group.e-filled textarea.e-input:-moz-placeholder,
.e-input-group.e-filled.e-control-wrapper textarea.e-input:-moz-placeholder,
input.e-filled.e-input::-moz-placeholder,
.e-input-group.e-filled input.e-input::-moz-placeholder,
.e-input-group.e-filled.e-control-wrapper input.e-input::-moz-placeholder,
textarea.e-filled.e-input::-moz-placeholder,
.e-input-group.e-filled textarea.e-input::-moz-placeholder,
.e-input-group.e-filled.e-control-wrapper textarea.e-input::-moz-placeholder,
input.e-filled.e-input:-ms-input-placeholder,
.e-input-group.e-filled input.e-input:-ms-input-placeholder,
.e-input-group.e-filled.e-control-wrapper input.e-input:-ms-input-placeholder,
textarea.e-filled.e-input:-ms-input-placeholder,
.e-input-group.e-filled.e-control-wrapper textarea.e-input:-ms-input-placeholder {
  color: rgba(var(--color-sf-placeholder-text-color));
}

.e-filled.e-float-input.e-error label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error label.e-float-text,
.e-filled.e-float-input.e-error input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text {
  color: var(--color-sf-border-error);
}

.e-filled.e-float-input.e-success label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success label.e-float-text,
.e-filled.e-float-input.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-success input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-success input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input .e-control-wrapper.e-success input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small.e-success input[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success input[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success input:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-success input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-success input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-small.e-success.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success input[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success input[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success input:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success.e-input-focus input ~ label.e-float-text {
  color: var(--color-sf-border-success);
}

.e-filled.e-float-input.e-warning label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning label.e-float-text,
.e-filled.e-float-input.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-warning input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-warning input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input .e-control-wrapper.e-warning input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small.e-warning input[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning input[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning input:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-warning input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-warning input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-small.e-warning.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning.e-input-focus input ~ label.e-float-text {
  color: var(--color-sf-border-warning);
}

.e-filled.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) input:valid ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) input label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input .e-control-wrapper:not(.e-error) input label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) input:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input:not(.e-input-focus):not(.e-disabled) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-filled.e-float-input:not(.e-input-focus) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-filled.e-float-input.e-control-wrapper:not(.e-input-focus):not(.e-disabled) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top),
.e-filled.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea:valid ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top {
  color: rgba(var(--color-sf-placeholder-text-color));
}

.e-filled.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) input[readonly]:focus ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input[readonly]:focus ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input[readonly]:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) input[readonly]:focus ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input[readonly]:focus ~ label.e-float-text,
.e-small .e-float-input.e-control-wrapper:not(.e-error) input[readonly]:focus ~ label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error).e-input-focus input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input[readonly] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error) textarea[readonly]:focus ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[readonly]:focus ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea[readonly]:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea[readonly]:focus ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[readonly]:focus ~ label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error).e-input-focus textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error).e-input-focus textarea[readonly] ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error).e-input-focus textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus textarea[readonly] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper:not(.e-error).e-input-focus input ~ label.e-float-text {
  color: rgba(var(--color-sf-primary));
}

.e-input-group textarea.e-input:-ms-input-placeholder,
.e-input-group textarea.e-input:-moz-placeholder,
.e-input-group .e-input:-ms-input-placeholder,
input.e-input::-webkit-input-placeholder {
  color: var(--color-sf-placeholder-text-color);
}

.e-filled.e-float-input:not(.e-disabled) .e-clear-icon:hover,
.e-filled.e-float-input.e-control-wrapper:not(.e-disabled) .e-clear-icon:hover,
.e-filled.e-input-group:not(.e-disabled) .e-clear-icon:hover,
.e-filled.e-input-group.e-control-wrapper:not(.e-disabled) .e-clear-icon:hover {
  color: rgba(var(--color-sf-icon-color));
}

.e-filled.e-float-input:not(.e-disabled) .e-clear-icon:active,
.e-filled.e-float-input.e-control-wrapper:not(.e-disabled) .e-clear-icon:active,
.e-filled.e-input-group:not(.e-disabled) .e-clear-icon:active,
.e-filled.e-input-group.e-control-wrapper:not(.e-disabled) .e-clear-icon:active {
  color: rgba(var(--color-sf-icon-color));
}

.e-filled.e-input[disabled],
.e-input-group.e-filled .e-input[disabled],
.e-input-group.e-filled.e-control-wrapper .e-input[disabled],
.e-input-group.e-filled.e-disabled,
.e-input-group.e-filled.e-control-wrapper.e-disabled,
.e-filled.e-float-input input[disabled],
.e-filled.e-float-input.e-control-wrapper input[disabled],
.e-filled.e-float-input textarea[disabled],
.e-filled.e-float-input.e-control-wrapper textarea[disabled],
.e-filled.e-float-input.e-disabled,
.e-filled.e-float-input.e-control-wrapper.e-disabled {
  background-image: none;
  background-position: initial;
  background-repeat: no-repeat;
  background-size: 0;
  border-color: var(--color-sf-border-disabled);
  color: var(--color-sf-content-text-color-disabled);
}

.e-filled.e-float-input.e-disabled:not(.e-success):not(.e-warning):not(.e-error),
.e-filled.e-float-input.e-control-wrapper.e-disabled:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: var(--color-sf-border-disabled);
}

.e-filled.e-input-group:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled,
.e-filled.e-input-group.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error).e-disabled .e-input-in-wrap,
.e-filled.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled,
.e-filled.e-input-group.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error).e-disabled .e-input-in-wrap {
  background: var(--color-sf-content-bg-color-alt1);
  background-image: none;
  background-position: initial;
  background-repeat: no-repeat;
  background-size: 0;
  border-color: var(--color-sf-border-disabled);
  color: var(--color-sf-content-text-color-disabled);
}

.e-filled.e-float-input.e-input-group.e-disabled .e-float-text,
.e-filled.e-float-input.e-input-group.e-disabled .e-float-text.e-label-top,
.e-filled.e-float-input input[disabled] ~ label.e-float-text,
.e-filled.e-float-input input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-disabled label.e-float-text,
.e-filled.e-float-input.e-disabled label.e-float-text.e-label-top,
.e-filled.e-float-input:not(.e-error) input[disabled] ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-disabled .e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-input-group.e-disabled .e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-disabled input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-disabled:not(.e-error) input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) input[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-disabled textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-disabled:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top,
input.e-filled.e-disabled.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled input.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled input.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper input.e-input:-moz-placeholder,
input.e-filled.e-disabled.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled input.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper input.e-input:-moz-placeholder,
textarea.e-filled.e-disabled.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled textarea.e-input:-moz-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper textarea.e-input:-moz-placeholder,
input.e-filled.e-disabled.e-input::-moz-placeholder,
.e-input-group.e-filled.e-disabled input.e-input::-moz-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper input.e-input::-moz-placeholder,
textarea.e-filled.e-disabled.e-input::-moz-placeholder,
.e-input-group.e-filled.e-disabled textarea.e-input::-moz-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper textarea.e-input::-moz-placeholder,
input.e-filled.e-disabled.e-input:-ms-input-placeholder,
.e-input-group.e-filled.e-disabled input.e-input:-ms-input-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper input.e-input:-ms-input-placeholder,
textarea.e-filled.e-disabled.e-input:-ms-input-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper textarea.e-input:-ms-input-placeholder,
.e-input-group.e-filled.e-disabled textarea.e-input:-ms-input-placeholder,
input.e-filled.e-disabled.e-input::-webkit-input-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper input.e-input::-webkit-input-placeholder,
.e-input-group.e-filled.e-disabled input.e-input::-webkit-input-placeholder,
textarea.e-filled.e-disabled.e-input::-webkit-input-placeholder,
.e-input-group.e-filled.e-disabled textarea.e-input::-webkit-input-placeholder,
.e-input-group.e-filled.e-disabled.e-control-wrapper textarea.e-input::-webkit-input-placeholder,
.e-filled.e-float-input.e-disabled .e-clear-icon,
.e-filled.e-float-input.e-control-wrapper.e-disabled .e-clear-icon,
.e-input-group.e-filled.e-disabled .e-clear-icon,
.e-input-group.e-filled.e-control-wrapper.e-disabled .e-clear-icon,
.e-input-group.e-filled.e-disabled .e-input-group-icon,
.e-input-group.e-filled.e-control-wrapper.e-disabled .e-input-group-icon,
.e-filled.e-float-input:not(.e-disabled) input[disabled],
.e-filled.e-float-input.e-control-wrapper:not(.e-disabled) input[disabled],
.e-filled.e-float-input:not(.e-disabled) textarea[disabled],
.e-filled.e-float-input.e-control-wrapper:not(.e-disabled) textarea[disabled] {
  color: var(--color-sf-content-text-color-disabled);
}

.e-filled.e-float-input.e-success textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-success textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small.e-success textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-success textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-success textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-success textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-small.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small.e-success textarea[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success textarea[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success textarea:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-success textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-success textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-success.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-success.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-success.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-success:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-success:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill.e-success:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill.e-success:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill.e-success:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-success textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-success textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-success textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill.e-success textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill.e-success textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill.e-success textarea:not(:focus):-webkit-autofill ~ label.e-float-text {
  color: var(--color-sf-border-success);
}

.e-filled.e-float-input.e-control-wrapper.e-small.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning input:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning input ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning input[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning input[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning input:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning input ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning input[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning input[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-warning textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-small.e-warning textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-warning textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom,
.e-filled.e-float-input.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-warning textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-warning textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea[readonly] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea[disabled] ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea label.e-float-text.e-label-top,
.e-filled.e-float-input.e-small.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-small.e-warning textarea[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning textarea[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning textarea:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-warning textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-warning textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea:valid ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea ~ label.e-label-top.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea[readonly] ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea[disabled] ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea:valid ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea ~ label.e-label-top.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea[readonly] ~ label.e-float-text.e-label-top,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea[disabled] ~ label.e-float-text.e-label-top,
.e-filled.e-float-input.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning input:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning textarea:focus ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-small.e-warning.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-warning.e-input-focus inputs ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-small.e-warning.e-input-focus input ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-warning.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-warning:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-warning:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill.e-warning:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill.e-warning:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill.e-warning:not(.e-input-focus) input:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-warning textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-warning textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-warning textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-autofill.e-warning textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small .e-filled.e-float-input.e-control-wrapper.e-autofill.e-warning textarea:not(:focus):-webkit-autofill ~ label.e-float-text,
.e-small.e-filled.e-float-input.e-control-wrapper.e-autofill.e-warning textarea:not(:focus):-webkit-autofill ~ label.e-float-text {
  color: var(--color-sf-border-warning);
}

/* stylelint-disable-line no-empty-source */
.e-input-group-icon.e-spin-up::before {
  content: "\e776";
  font-family: "e-icons";
}

.e-input-group-icon.e-spin-down::before {
  content: "\e729";
  font-family: "e-icons";
}

.e-numeric-container {
  width: 100%;
}

.e-content-placeholder.e-numeric.e-placeholder-numeric {
  background-size: 300px 33px;
  min-height: 33px;
}

.e-float-input.e-input-group.e-control-wrapper.e-numeric .e-numeric-hidden,
.e-input-group.e-control-wrapper.e-numeric .e-numeric-hidden,
.e-float-input.e-control-wrapper.e-numeric .e-numeric-hidden,
.e-float-input.e-input-group.e-control-wrapper.e-numeric.e-input-focus .e-numeric-hidden,
.e-input-group.e-control-wrapper.e-numeric.e-input-focus .e-numeric-hidden,
.e-float-input.e-control-wrapper.e-numeric.e-input-focus .e-numeric-hidden {
  border: 0;
  height: 0;
  margin: 0;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}

.e-numeric.e-control-wrapper.e-input-group .e-input-group-icon {
  font-size: 14px;
}

/*! maskedtextbox layout */
.e-control-wrapper.e-mask .e-maskedtextbox {
  font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", arial, "Noto Sans", "Liberation Sans", sans-serif, "apple color emoji", "Segoe UI emoji", "Segoe UI Symbol", "Noto color emoji";
  font-size: 14px;
}

.e-content-placeholder.e-mask.e-placeholder-mask {
  background-size: 300px 33px;
  min-height: 33px;
}

/*! maskedtextbox theme */
.e-control-wrapper.e-mask.e-error .e-maskedtextbox,
.e-utility-mask.e-error {
  color: var(--color-sf-danger);
}

/* stylelint-disable-line no-empty-source */
/* stylelint-disable-line no-empty-source */
/* stylelint-disable property-no-vendor-prefix */
.e-control-wrapper.e-slider-container {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: inline-block;
  height: 26px;
  line-height: normal;
  outline: none;
  position: relative;
  user-select: none;
}
.e-control-wrapper.e-slider-container.e-material-slider .e-slider .e-handle.e-material-handle {
  cursor: default;
  -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: -webkit-transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), -webkit-transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 3;
}
.e-control-wrapper.e-slider-container::after {
  content: "tailwind3";
  display: none;
}
.e-control-wrapper.e-slider-container .e-slider {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: block;
  outline: 0 none;
  padding: 0;
  position: relative;
}
.e-control-wrapper.e-slider-container .e-slider .e-handle {
  border-radius: 10px;
  -webkit-box-shadow: 0 1px 2px -1px rgba(var(--color-sf-black), 0.1), 0 1px 3px 0 rgba(var(--color-sf-black), 0.1);
          box-shadow: 0 1px 2px -1px rgba(var(--color-sf-black), 0.1), 0 1px 3px 0 rgba(var(--color-sf-black), 0.1);
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  height: 12px;
  width: 12px;
  outline: none;
  position: absolute;
  -ms-touch-action: none;
      touch-action: none;
  -webkit-transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, -webkit-transform 300ms ease-out;
  transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, -webkit-transform 300ms ease-out;
  transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, transform 300ms ease-out;
  transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, transform 300ms ease-out, -webkit-transform 300ms ease-out;
  z-index: 10;
}
.e-control-wrapper.e-slider-container .e-slider .e-handle.e-large-thumb-size {
  -webkit-transform: scale(1.5);
          transform: scale(1.5);
}
.e-control-wrapper.e-slider-container .e-slider .e-handle.e-tab-handle {
  background-color: var(--color-sf-primary-bg-color-hover);
  -webkit-box-shadow: 0 0 0 2px var(--color-sf-content-bg-color), 0 0 0 4px var(--color-sf-primary);
          box-shadow: 0 0 0 2px var(--color-sf-content-bg-color), 0 0 0 4px var(--color-sf-primary);
}
.e-control-wrapper.e-slider-container.e-rtl.e-horizontal .e-slider .e-handle {
  margin: 0 -6px 0 0;
}
.e-control-wrapper.e-slider-container.e-rtl.e-horizontal .e-scale.e-h-scale .e-tick.e-first-tick {
  background-position: right center;
  left: 0;
}
.e-control-wrapper.e-slider-container.e-rtl.e-horizontal .e-scale.e-h-scale .e-tick.e-last-tick {
  background-position: left center;
}
.e-control-wrapper.e-slider-container.e-rtl.e-vertical {
  direction: ltr;
}
.e-control-wrapper.e-slider-container.e-disabled .e-btn {
  cursor: default;
}
.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle {
  cursor: default;
}
.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle.e-handle-disable {
  display: none;
}
.e-control-wrapper.e-slider-container.e-horizontal {
  height: 48px;
  width: 100%;
}
.e-control-wrapper.e-slider-container.e-horizontal .e-first-button {
  left: 0;
  margin-top: -10px;
  top: 50%;
}
.e-control-wrapper.e-slider-container.e-horizontal .e-first-button .e-button-icon::before {
  font-size: 17px;
  color: var(--color-sf-secondary-text-color);
  content: "\e765";
}
.e-control-wrapper.e-slider-container.e-horizontal .e-second-button {
  right: 0;
  margin-top: -10px;
  top: 50%;
}
.e-control-wrapper.e-slider-container.e-horizontal .e-second-button .e-button-icon::before {
  font-size: 18px;
  color: var(--color-sf-secondary-text-color);
  content: "\e748";
}
.e-control-wrapper.e-slider-container.e-horizontal.e-slider-btn {
  padding: 0 28px;
}
.e-control-wrapper.e-slider-container.e-horizontal .e-slider {
  height: 32px;
  width: 100%;
  position: relative;
  top: calc(50% - 16px);
}
.e-control-wrapper.e-slider-container.e-horizontal .e-slider-track {
  height: 4px;
  width: 100%;
  left: 0;
  position: absolute;
  overflow: hidden;
  background: var(--color-sf-content-bg-color-alt3);
  border-radius: 4px;
  top: calc(50% - 4px);
}
.e-control-wrapper.e-slider-container.e-horizontal .e-handle {
  margin-left: -8px;
  top: calc(50% - 8px);
}
.e-control-wrapper.e-slider-container.e-horizontal.e-scale-both .e-range {
  height: 4px;
}
.e-control-wrapper.e-slider-container.e-horizontal .e-range {
  height: 4px;
  top: calc(50% - 2px);
  top: calc(50% - 4px);
}
.e-control-wrapper.e-slider-container.e-horizontal .e-limits {
  background-color: var(--color-sf-content-bg-color-alt5);
  position: absolute;
  height: 4px;
  top: calc(50% - 4px);
}
.e-control-wrapper.e-slider-container.e-vertical {
  height: inherit;
  width: 48px;
  padding: 38px 0;
}
.e-control-wrapper.e-slider-container.e-vertical .e-slider {
  height: 100%;
  width: 32px;
  left: calc(50% - 16px);
  position: relative;
}
.e-control-wrapper.e-slider-container.e-vertical .e-slider-track {
  background: var(--color-sf-content-bg-color-alt3);
  bottom: 0;
  height: 100%;
  position: absolute;
  overflow: hidden;
  left: calc(50% - 4px);
  width: 4px;
  border-radius: 4px;
}
.e-control-wrapper.e-slider-container.e-vertical.e-small-size.e-slider-btn {
  height: 100%;
  padding: 28px 0;
}
.e-control-wrapper.e-slider-container.e-vertical.e-small-size.e-slider-btn .e-slider {
  height: 100%;
  width: 4px;
}
.e-control-wrapper.e-slider-container.e-vertical .e-first-button {
  bottom: 0;
  margin-right: -10px;
  right: 50%;
}
.e-control-wrapper.e-slider-container.e-vertical .e-first-button .e-button-icon::before {
  font-size: 18px;
  content: "\e729";
  color: var(--color-sf-secondary-text-color);
}
.e-control-wrapper.e-slider-container.e-vertical .e-second-button {
  top: 0;
  margin-right: -10px;
  right: 50%;
}
.e-control-wrapper.e-slider-container.e-vertical .e-second-button .e-button-icon::before {
  font-size: 18px;
  color: var(--color-sf-secondary-text-color);
  content: "\e776";
}
.e-control-wrapper.e-slider-container.e-vertical.e-scale-both .e-slider .e-handle {
  margin-bottom: -6px;
}
.e-control-wrapper.e-slider-container.e-vertical .e-slider .e-handle {
  margin-bottom: -6px;
  left: calc(50% - 8px);
}
.e-control-wrapper.e-slider-container.e-vertical .e-slider .e-range {
  width: 4px;
  left: calc(50% - 4px);
}
.e-control-wrapper.e-slider-container.e-vertical .e-slider .e-limits {
  background-color: var(--color-sf-content-bg-color-alt5);
  position: absolute;
  width: 4px;
  left: calc(50% - 4px);
}
.e-control-wrapper.e-slider-container .e-range {
  border-radius: 4px;
  position: absolute;
  -webkit-transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, width 300ms ease-out, height 300ms ease-out;
  transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, width 300ms ease-out, height 300ms ease-out;
}
.e-control-wrapper.e-slider-container .e-range.e-drag-horizontal, .e-control-wrapper.e-slider-container .e-range.e-drag-vertical {
  cursor: pointer;
}
.e-control-wrapper.e-slider-container .e-tick-before.e-scale.e-h-scale .e-tick {
  height: 6px;
  top: -10px;
}
.e-control-wrapper.e-slider-container .e-tick-before.e-scale.e-h-scale .e-large {
  height: 10px;
  top: -10px;
}
.e-control-wrapper.e-slider-container .e-tick-before.e-scale.e-v-scale .e-tick {
  width: 6px;
  left: 5px;
}
.e-control-wrapper.e-slider-container .e-tick-before.e-scale.e-v-scale .e-large {
  width: 10px;
  left: 1px;
}
.e-control-wrapper.e-slider-container .e-tick-after.e-scale.e-h-scale .e-tick {
  height: 6px;
  top: 1px;
}
.e-control-wrapper.e-slider-container .e-tick-after.e-scale.e-h-scale .e-large {
  height: 10px;
  top: 5px;
}
.e-control-wrapper.e-slider-container .e-tick-after.e-scale.e-v-scale .e-tick {
  width: 6px;
  left: 17px;
}
.e-control-wrapper.e-slider-container .e-tick-after.e-scale.e-v-scale .e-large {
  width: 10px;
  left: 17px;
}
.e-control-wrapper.e-slider-container.e-scale-before .e-scale.e-v-scale {
  right: 9px;
}
.e-control-wrapper.e-slider-container.e-scale-after .e-scale.e-v-scale {
  right: 9px;
}
.e-control-wrapper.e-slider-container .e-tick-both.e-scale.e-h-scale .e-tick {
  height: 16px;
  top: -12px;
}
.e-control-wrapper.e-slider-container .e-tick-both.e-scale.e-h-scale .e-large {
  height: 24px;
  top: -9px;
}
.e-control-wrapper.e-slider-container .e-scale {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", arial, "Noto Sans", "Liberation Sans", sans-serif, "apple color emoji", "Segoe UI emoji", "Segoe UI Symbol", "Noto color emoji";
  height: 28px;
  width: 100%;
  line-height: normal;
  list-style: none outside none;
  margin: 0;
  outline: 0 none;
  padding: 0;
  position: absolute;
  top: 16px;
  z-index: 1;
  font-size: 12px;
  margin-top: -5px;
}
.e-control-wrapper.e-slider-container .e-scale .e-tick {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAQSURBVHgBAQUA+v8AztTa/whsA3yqMpmsAAAAAElFTkSuQmCC");
  cursor: pointer;
  outline: none;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background-position: center center;
}
.e-control-wrapper.e-slider-container .e-scale .e-tick .e-tick-value {
  color: var(--color-sf-content-text-color-alt1);
  font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", arial, "Noto Sans", "Liberation Sans", sans-serif, "apple color emoji", "Segoe UI emoji", "Segoe UI Symbol", "Noto color emoji";
  font-size: 12px;
  outline: none;
  position: absolute;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale {
  height: 100%;
  width: 28px;
  left: calc(50% - 14px);
  top: 0;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick {
  background-repeat: repeat-x;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick.e-first-tick {
  background-position-y: center;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick.e-last-tick {
  background-position-y: bottom;
  margin-top: 2px;
}
.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick {
  display: inline-block;
  background-repeat: repeat-y;
  height: 100%;
  top: 0;
}
.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-before, .e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-both:first-child {
  top: -18px;
}
.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-after, .e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-both {
  bottom: -20px;
}
.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick.e-first-tick {
  background-position: left center;
}
.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick.e-last-tick {
  background-position: right center;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-before {
  right: 17px;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-after {
  left: 19px;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-both {
  right: 44px;
}
.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-both:first-child {
  left: 42px;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-tip-content {
  padding: 2px 4px;
  text-align: center;
}

.e-slider-horizantal-color {
  height: 100%;
  top: 0;
  position: absolute;
}

.e-slider-vertical-color {
  position: absolute;
  width: 100%;
  left: -1px;
}

/* stylelint-enable property-no-vendor-prefix */
.e-control-wrapper.e-slider-container .e-slider-button {
  background-color: var(--color-sf-secondary-bg-color);
  border: 1px solid var(--color-sf-secondary-border-color);
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  outline: none;
  position: absolute;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  padding: 0;
}
.e-control-wrapper.e-slider-container .e-slider .e-range {
  background-color: var(--color-sf-primary);
}
.e-control-wrapper.e-slider-container .e-slider .e-handle {
  background-color: var(--color-sf-primary);
  border-color: var(--color-sf-primary);
}
.e-control-wrapper.e-slider-container .e-slider .e-handle.e-material-tooltip {
  background-color: transparent;
  border-color: transparent;
}
.e-control-wrapper.e-slider-container.e-slider-hover .e-slider-track {
  background-color: var(--color-sf-content-bg-color-alt2);
}
.e-control-wrapper.e-slider-container.e-slider-hover .e-range {
  background-color: var(--color-sf-primary-bg-color-hover);
}
.e-control-wrapper.e-slider-container.e-slider-hover .e-handle {
  border-color: var(--color-sf-primary);
}
.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-range {
  background: var(--color-sf-primary-light);
}
.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle {
  background: var(--color-sf-primary-light);
}
/* stylelint-disable-line no-empty-source */
.e-content-placeholder.e-textbox.e-placeholder-textbox {
  background-size: 300px 33px;
  min-height: 33px;
}

.e-input-group.e-input-focus.e-multi-line-input textarea.e-textbox {
  padding-right: 30px;
}

.e-input-group.e-input-focus.e-rtl.e-multi-line-input textarea.e-textbox {
  padding-left: 30px;
}

/* stylelint-disable-line no-empty-source */
.e-input-group.e-multi-line-input.e-auto-width {
  width: auto;
}

.e-input-group.e-multi-line-input textarea.e-resize-x {
  resize: horizontal;
}
.e-input-group.e-multi-line-input textarea.e-resize-y {
  resize: vertical;
}
.e-input-group.e-multi-line-input textarea.e-resize-xy {
  resize: both;
}
.e-input-group.e-multi-line-input textarea.e-textarea.e-resize-none {
  resize: none;
}

.e-float-input .e-clear-icon:hover,
.e-float-input.e-control-wrapper .e-clear-icon:hover,
.e-input-group .e-clear-icon:hover,
.e-input-group.e-control-wrapper .e-clear-icon:hover {
  background: none;
  border: none;
}

.e-float-input:not(.e-disabled) .e-clear-icon:hover,
.e-float-input.e-control-wrapper:not(.e-disabled) .e-clear-icon:hover,
.e-input-group:not(.e-disabled) .e-clear-icon:hover,
.e-input-group.e-control-wrapper:not(.e-disabled) .e-clear-icon:hover {
  background: none;
}

/* stylelint-disable property-no-vendor-prefix */
@-webkit-keyframes material-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes material-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes fabric-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes fabric-spinner-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons::before {
  content: "\e7e7";
}

.e-upload .e-upload-files .e-icons.e-file-pause-btn::before {
  content: "\e77b";
}

.e-upload .e-upload-files .e-icons.e-file-reload-btn::before {
  content: "\e706";
}

.e-upload .e-upload-files .e-icons.e-file-play-btn::before {
  content: "\e70c";
}

.e-upload .e-upload-files .e-file-delete-btn.e-icons::before {
  content: "\e820";
}

.e-upload .e-upload-files .e-file-abort-btn.e-icons::before {
  content: "\e81b";
}

.e-upload .e-upload-files .e-icons.e-msie::before {
  position: relative;
  right: 10px;
}

.e-upload .e-upload-files .e-icons.e-file-abort-icon.e-msie::before {
  right: 12px;
}

.e-upload {
  width: 100%;
}
.e-upload.e-control-wrapper {
  font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", arial, "Noto Sans", "Liberation Sans", sans-serif, "apple color emoji", "Segoe UI emoji", "Segoe UI Symbol", "Noto color emoji";
}
.e-upload .e-hidden-file-input {
  border: 0;
  height: 0;
  margin: 0;
  outline: none;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}
.e-upload .e-file-select-wrap {
  padding: 12px 0 12px 12px;
}
.e-upload .e-file-select-wrap .e-file-select,
.e-upload .e-file-select-wrap .e-file-select .e-uploader {
  display: inline-block;
  width: 0;
}
.e-upload .e-file-select-wrap .e-file-select .e-uploader {
  opacity: 0;
}
.e-upload .e-file-select-wrap .e-file-drop {
  font-family: inherit;
  font-size: 14px;
  margin-left: 12px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 15px;
  padding-top: 11px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: var(--color-sf-danger);
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-content-text-color-alt2);
  display: block;
  font-size: 12px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: var(--color-sf-danger);
}
.e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: var(--color-sf-danger);
}
.e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 42px;
}
.e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-icon-color-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-upload .e-upload-files {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.e-upload .e-upload-files .e-upload-file-list {
  font-family: inherit;
  font-size: 14px;
  height: 100%;
  line-height: 22px;
  min-height: 82px;
  position: relative;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container {
  display: block;
  height: 100%;
  margin-left: 12px;
  margin-right: 90px;
  min-height: 35px;
  position: relative;
  top: 0;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: left;
  font-family: inherit;
  font-size: 14px;
  max-width: 75%;
  overflow: hidden;
  padding-top: 12px;
  position: relative;
  text-overflow: ellipsis;
  top: 0;
  white-space: nowrap;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name::before {
  content: attr(data-tail);
  float: right;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  display: block;
  font-family: inherit;
  font-size: 14px;
  padding-top: 12px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-hidden {
  visibility: hidden;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  display: block;
  font-size: 12px;
  padding: 0;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  display: block;
  font-family: inherit;
  font-size: 12px;
  padding-bottom: 12px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-progress, .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information.e-upload-progress {
  display: none;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  display: block;
  height: 10px;
  padding-bottom: 11px;
  padding-top: 6px;
  position: absolute;
  width: 95%;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap {
  border-radius: 4px;
  display: block;
  height: 4px;
  width: 100%;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-inner-wrap .e-upload-progress-bar {
  border-radius: 4px;
  display: inherit;
  height: 4px;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: right;
  font-family: inherit;
  font-size: 12px;
  position: relative;
  right: 0;
  top: -33px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  height: 20px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 15px;
  margin-top: -9px;
  padding: 10px;
  position: absolute;
  right: 0;
  top: 50%;
  vertical-align: middle;
  width: 20px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-abort-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-pause-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-play-btn.e-icons.e-upload-progress, .e-upload .e-upload-files .e-file-reload-btn.e-icons.e-upload-progress {
  cursor: default;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-icon-color-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-upload .e-upload-files .e-file-remove-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-msie.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-msie.e-icons {
  padding: 18px 13px 18px 23px;
}
.e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons {
  right: 36px;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):hover, .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):hover, .e-upload .e-upload-files .e-clear-icon-focus {
  background-color: rgba(var(--color-sf-black), 0.12);
  border-color: transparent;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-upload .e-upload-files .e-clear-icon-focus {
  border-radius: 50%;
}
.e-upload .e-upload-files .e-file-delete-btn.e-icons {
  font-size: 14px;
  opacity: 1;
}
.e-upload .e-upload-files .e-file-abort-btn.e-icons {
  font-size: 14px;
  opacity: 1;
}
.e-upload .e-file-select-wrap .e-btn, .e-upload .e-upload-actions .e-btn {
  font-family: inherit;
  font-weight: 600;
}
.e-upload .e-upload-actions {
  position: relative;
  text-align: right;
}
.e-upload .e-upload-actions .e-file-upload-btn {
  margin: 8px;
}
.e-upload .e-upload-actions .e-file-clear-btn {
  margin: 8px;
}
.e-upload.e-rtl .e-file-select-wrap {
  padding: 16px 12px 16px 0;
}
.e-upload.e-rtl .e-file-select-wrap .e-control.e-btn {
  margin-right: 0;
}
.e-upload.e-rtl .e-file-select-wrap .e-file-drop {
  margin-left: 60px;
  margin-right: 12px;
  position: relative;
}
.e-upload.e-rtl .e-upload-actions {
  text-align: left;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container {
  height: 100%;
  margin-left: 60px;
  margin-right: 11px;
  position: relative;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-rtl-container {
  direction: ltr;
  float: right;
  width: 100%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  float: right;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  float: right;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  direction: ltr;
  float: right;
  position: relative;
  text-align: right;
  width: 100%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  float: right;
  position: initial;
  top: 23px;
  width: 86%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap .e-progress-bar-text {
  float: left;
  right: 0;
  top: -32px;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-remove-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-delete-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-abort-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons {
  left: 0;
  margin-left: 11px;
  margin-right: 11px;
  right: auto;
  top: 50%;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons {
  left: 36px;
  right: auto;
}
.e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-play-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-reload-btn.e-icons, .e-upload.e-rtl .e-upload-files .e-upload-file-list .e-file-pause-btn.e-icons {
  left: 36px;
}
.e-upload.e-disabled .e-file-drop {
  color: var(--color-sf-icon-color-disabled);
}
.e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-type, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-size, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-upload.e-disabled .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-icon-color-disabled);
}
.e-upload .e-content-placeholder.e-upload.e-placeholder-upload {
  background-size: 400px 65px;
  min-height: 65px;
}

.e-small .e-upload .e-file-select-wrap {
  padding: 12px 0 12px 12px;
}
.e-small .e-upload .e-file-select-wrap .e-file-drop {
  font-size: 12px;
  margin-left: 10px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list {
  min-height: 79px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  font-size: 12px;
  padding-top: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  font-size: 12px;
  padding: 6px 0;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  font-size: 12px;
  padding-top: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  padding-bottom: 12px;
}
.e-small .e-upload .e-upload-files .e-upload-file-list .e-file-container .e-upload-progress-wrap {
  padding-bottom: 2px;
  padding-top: 2px;
}
.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-play-btn.e-icons, .e-small .e-upload .e-upload-files .e-file-pause-btn.e-icons {
  font-size: 14px;
  height: 24px;
  padding: 12px;
  width: 24px;
}
.e-small .e-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-small .e-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-icon-color-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}
.e-small .e-upload.e-rtl .e-file-select-wrap {
  padding: 12px 12px 12px 0;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list {
  min-height: 35px;
  padding-bottom: 12px;
  padding-top: 8px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container {
  top: 0;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-hidden-input {
  display: none;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  line-height: 1.5;
  padding-top: 4px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name.e-error, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type.e-error, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-error {
  color: var(--color-sf-danger);
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-content-text-color-alt2);
  display: block;
  font-size: 12px;
  line-height: 1.5;
  padding-bottom: 0;
  padding-top: 4px;
  position: relative;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid {
  color: var(--color-sf-danger);
}
.e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-name, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-type, .e-small .e-upload.e-form-upload .e-upload-files .e-upload-file-list.e-file-invalid .e-file-status {
  color: var(--color-sf-danger);
}
.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-abort-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-reload-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-play-btn.e-icons, .e-small .e-upload.e-form-upload .e-upload-files .e-file-pause-btn.e-icons {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  top: 42px;
}
.e-small .e-upload.e-form-upload .e-upload-files .e-file-remove-btn.e-icons.e-disabled, .e-small .e-upload.e-form-upload .e-upload-files .e-file-delete-btn.e-icons.e-disabled {
  color: var(--color-sf-icon-color-disabled);
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.e-upload {
  border: 1px dashed var(--color-sf-border-dark);
}
.e-upload .e-file-drop {
  color: var(--color-sf-content-text-color-alt2);
  vertical-align: middle;
}
.e-upload .e-upload-files {
  border-top: 1px solid var(--color-sf-border-light);
}
.e-upload .e-upload-files .e-upload-file-list {
  border-bottom: 1px solid var(--color-sf-border-light);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-name {
  color: var(--color-sf-content-text-color);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-type {
  color: var(--color-sf-content-text-color);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-size {
  color: var(--color-sf-content-text-color-alt2);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status,
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-information {
  color: var(--color-sf-content-text-color);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-success {
  color: var(--color-sf-success);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-upload-fails {
  color: var(--color-sf-danger);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-file-status.e-validation-fails {
  color: var(--color-sf-danger);
  font-weight: 500;
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap {
  background-color: var(--color-sf-content-bg-color-alt3);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-progress {
  background: var(--color-sf-primary);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-success {
  background: var(--color-sf-success);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-inner-wrap .e-upload-progress-bar.e-upload-failed {
  background: var(--color-sf-danger);
}
.e-upload .e-upload-files .e-upload-file-list .e-file-container .e-progress-bar-text {
  color: var(--color-sf-content-text-color);
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons, .e-upload .e-upload-files .e-file-delete-btn.e-icons, .e-upload .e-upload-files .e-file-reload-btn.e-icons, .e-upload .e-upload-files .e-file-abort-btn.e-icons, .e-upload .e-upload-files .e-file-pause-btn.e-icons, .e-upload .e-upload-files .e-file-play-btn.e-icons {
  color: var(--color-sf-icon-color);
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:hover, .e-upload .e-upload-files .e-file-delete-btn.e-icons:hover, .e-upload .e-upload-files .e-file-reload-btn.e-icons:hover, .e-upload .e-upload-files .e-file-abort-btn.e-icons:hover, .e-upload .e-upload-files .e-file-pause-btn.e-icons:hover, .e-upload .e-upload-files .e-file-play-btn.e-icons:hover {
  color: var(--color-sf-icon-color-hover);
}
.e-upload .e-upload-files .e-file-remove-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-delete-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-pause-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-play-btn.e-icons:not(.e-upload-progress):focus, .e-upload .e-upload-files .e-file-reload-btn.e-icons:focus, .e-upload .e-upload-files .e-file-abort-btn.e-icons:not(.e-disabled):focus, .e-upload .e-upload-files .e-clear-icon-focus {
  background-color: rgba(var(--color-sf-black), 0.12);
  border-color: transparent;
  -webkit-box-shadow: 0 0 0 transparent;
          box-shadow: 0 0 0 transparent;
}

.e-container .e-value-switch-btn::before {
  content: "\e748";
}

.e-colorpicker-wrapper,
.e-colorpicker-container {
  display: inline-block;
  line-height: 0;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-colorpicker-wrapper .e-colorpicker,
.e-colorpicker-container .e-colorpicker {
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 1px;
}
.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn,
.e-colorpicker-container .e-split-btn-wrapper .e-split-colorpicker.e-split-btn {
  font-family: initial;
  line-height: 14px;
  padding: 3px 8px;
}
.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color,
.e-colorpicker-container .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 8px;
  border-radius: 4px;
  height: 24px;
  margin-top: 0;
  position: relative;
  width: 24px;
}
.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color .e-split-preview,
.e-colorpicker-container .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color .e-split-preview {
  border-radius: 4px;
}
.e-colorpicker-wrapper .e-split-btn-wrapper.e-rtl .e-split-colorpicker.e-split-btn,
.e-colorpicker-container .e-split-btn-wrapper.e-rtl .e-split-colorpicker.e-split-btn {
  padding: 3px 8px;
}
.e-colorpicker-wrapper *,
.e-colorpicker-container * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.e-colorpicker-wrapper.e-disabled .e-palette .e-tile,
.e-colorpicker-container.e-disabled .e-palette .e-tile {
  cursor: default;
}
.e-colorpicker-wrapper.e-disabled .e-palette .e-tile:hover,
.e-colorpicker-container.e-disabled .e-palette .e-tile:hover {
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.e-colorpicker-wrapper.e-disabled .e-palette .e-tile.e-selected,
.e-colorpicker-container.e-disabled .e-palette .e-tile.e-selected {
  border: 0;
}
.e-colorpicker-wrapper.e-disabled .e-container .e-handler, .e-colorpicker-wrapper.e-disabled .e-container .e-preview-container, .e-colorpicker-wrapper.e-disabled .e-container .e-slider-preview .e-colorpicker-slider .e-hue-slider, .e-colorpicker-wrapper.e-disabled .e-container .e-slider-preview .e-colorpicker-slider .e-opacity-slider, .e-colorpicker-wrapper.e-disabled .e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle,
.e-colorpicker-container.e-disabled .e-container .e-handler,
.e-colorpicker-container.e-disabled .e-container .e-preview-container,
.e-colorpicker-container.e-disabled .e-container .e-slider-preview .e-colorpicker-slider .e-hue-slider,
.e-colorpicker-container.e-disabled .e-container .e-slider-preview .e-colorpicker-slider .e-opacity-slider,
.e-colorpicker-container.e-disabled .e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle {
  cursor: default;
}
.e-colorpicker-wrapper.e-disabled .e-container .e-hsv-container,
.e-colorpicker-container.e-disabled .e-container .e-hsv-container {
  pointer-events: none;
}

.e-colorpicker-popup:not(.e-split-btn-wrapper):not(.e-dropdown-btn) {
  line-height: 0;
}

/* stylelint-disable property-no-vendor-prefix */
.e-colorpicker.e-modal, .e-colorpicker.e-hsv-model {
  -webkit-overflow-scrolling: touch;
  height: 100%;
  left: 0;
  position: fixed;
  top: 0;
  width: 100%;
}
.e-colorpicker.e-modal {
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0.5;
  pointer-events: auto;
}
.e-colorpicker.e-hsv-model {
  background-color: transparent;
}

.sf-colorpicker .e-switch-ctrl-btn .e-ctrl-btn {
  position: relative;
}

.e-show-value .sf-colorpicker.e-color-palette .e-selected-value, .e-show-value .sf-colorpicker.e-color-palette .e-switch-ctrl-btn {
  width: 270px;
}

.e-dropdown-popup.e-transparent .e-container {
  display: none;
}

body.e-colorpicker-overflow {
  overflow: visible;
}

.e-container {
  border: 0;
  border-radius: 0;
  display: inline-block;
  line-height: 0;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-box-shadow: 0 0 4px 0 rgba(var(--color-sf-black), 0.1), 0 4px 6px -4px rgba(var(--color-sf-black), 0.1), 0 10px 15px -3px rgba(var(--color-sf-black), 0.1);
          box-shadow: 0 0 4px 0 rgba(var(--color-sf-black), 0.1), 0 4px 6px -4px rgba(var(--color-sf-black), 0.1), 0 10px 15px -3px rgba(var(--color-sf-black), 0.1);
}
.e-container.e-color-picker {
  width: 364px;
}
.e-container.e-color-picker .e-mode-switch-btn {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUwLjIgKDU1MDQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA0IENvcHk8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iQXJ0Ym9hcmQiPgogICAgICAgICAgICA8ZyBpZD0iR3JvdXAtNC1Db3B5IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgMC4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMjUtQ29weS04IiBmaWxsPSIjNDU5NkNFIiB4PSIwIiB5PSIwIiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiPjwvcmVjdD4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMjUtQ29weS05IiBmaWxsPSIjNUNDMTVCIiB4PSIwIiB5PSIxMiIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIj48L3JlY3Q+CiAgICAgICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTI1LUNvcHktMTAiIGZpbGw9IiNGQkQ1MDYiIHg9IjEyIiB5PSIwIiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiPjwvcmVjdD4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMjUtQ29weS0xMSIgZmlsbD0iI0VDNEU0MyIgeD0iMTIiIHk9IjEyIiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiPjwvcmVjdD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+") no-repeat 100% 100%;
}
.e-container.e-color-palette {
  width: auto;
}
.e-container.e-color-palette .e-mode-switch-btn {
  background: transparent url("data:image/svg+xml;base64,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") no-repeat 100% 100%;
}
.e-container .e-custom-palette {
  width: auto;
}
.e-container .e-custom-palette .e-palette {
  padding: 10px;
}
.e-container .e-custom-palette.e-palette-group {
  height: 364px;
  overflow-y: scroll;
}
.e-container .e-palette {
  border-bottom: 0;
  display: table;
  line-height: 0;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-container .e-palette .e-row {
  display: table-row;
  white-space: nowrap;
}
.e-container .e-palette .e-tile {
  border: 0.5px solid transparent;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  height: 28px;
  text-align: center;
  vertical-align: middle;
  width: 28px;
}
.e-container .e-palette .e-tile.e-selected {
  outline: rgba(var(--color-sf-black)) 0.5px solid;
  position: relative;
}
.e-container .e-palette .e-tile:hover {
  -webkit-box-shadow: 2px 2px 7px 2px rgba(var(--color-sf-border-light), 0.3);
          box-shadow: 2px 2px 7px 2px rgba(var(--color-sf-border-light), 0.3);
  position: relative;
}
.e-container .e-palette .e-tile.e-nocolor-item {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 8px;
}
.e-container .e-hsv-container {
  border-bottom: 0;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
}
.e-container .e-hsv-container .e-hsv-color {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0, rgba(0, 0, 0, 0)), to(rgba(var(--color-sf-black)))), -webkit-gradient(linear, left top, right top, color-stop(0, rgba(var(--color-sf-white))), to(rgba(255, 255, 255, 0)));
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0, rgba(var(--color-sf-black)) 100%), linear-gradient(to right, rgba(var(--color-sf-white)) 0, rgba(255, 255, 255, 0) 100%);
  height: 232px;
}
.e-container .e-hsv-container .e-handler {
  border: 2px solid rgba(var(--color-sf-white));
  border-radius: 10px;
  -webkit-box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
          box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
  cursor: pointer;
  display: inline-block;
  height: 16px;
  margin-left: -4px;
  margin-top: -4px;
  position: absolute;
  -ms-touch-action: none;
      touch-action: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 16px;
}
.e-container .e-slider-preview {
  display: inline-block;
  width: 100%;
  padding: 5px 8px 10px 9px;
}
.e-container .e-slider-preview .e-colorpicker-slider {
  display: inline-block;
  width: 84.74%;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container {
  height: 22px;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-slider {
  height: 22px;
  top: calc(50% - 4px);
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-slider-track {
  height: 6px !important; /* stylelint-disable-line declaration-no-important */
  top: calc(50% - 4px);
  border-radius: 4px;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle {
  border-radius: 10px;
  cursor: pointer;
  height: 10px;
  top: calc(68% - 10px);
  width: 10px;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle.e-handle-active {
  cursor: pointer;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle.e-large-thumb-size {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.e-container .e-slider-preview .e-colorpicker-slider .e-hue-slider .e-slider-track {
  background: -webkit-gradient(linear, left top, right top, color-stop(0, #f00), color-stop(16%, #ff0), color-stop(33%, #0f0), color-stop(50%, #0ff), color-stop(67%, #00f), color-stop(84%, #f0f), to(#ff0004));
  background: linear-gradient(to right, #f00 0, #ff0 16%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 84%, #ff0004 100%);
}
.e-container .e-slider-preview .e-colorpicker-slider .e-opacity-slider .e-slider-track {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  border: 0;
  z-index: 0;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-opacity-slider .e-opacity-empty-track {
  background-size: contain;
  border: 0;
  height: 6px;
  position: absolute;
  top: calc(50% - 4px);
  width: 100%;
  z-index: 1;
  border-radius: 4px;
}
.e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-hue-slider .e-handle, .e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-hue-slider .e-handle-start, .e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-opacity-slider .e-handle, .e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-opacity-slider .e-handle-start {
  -webkit-box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
          box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
}
.e-container .e-slider-preview .e-preview-container {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 10px;
  border: 1px solid var(--color-sf-border-light);
  cursor: pointer;
  display: inline-block;
  height: 32px;
  margin-left: 5.205%;
  position: relative;
  top: 0;
  width: 32px;
  height: 36px;
  border-radius: 4px;
}
.e-container .e-slider-preview .e-preview-container .e-preview {
  display: block;
  height: 18px;
  position: absolute;
  width: 100%;
}
.e-container .e-slider-preview .e-preview-container .e-preview.e-current {
  border-bottom: 1px solid var(--color-sf-border-light);
  top: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
.e-container .e-slider-preview .e-preview-container .e-preview.e-previous {
  height: 18px;
  top: 18px;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.e-container .e-selected-value {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 8px 14px 9px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}
.e-container .e-selected-value .e-input-container .e-float-input input {
  padding-left: 0;
  text-align: center;
}
.e-container .e-selected-value .e-input-container .e-numeric-hidden {
  display: none;
}
.e-container .e-selected-value .e-value-switch-btn {
  font-family: "e-icons";
  margin-top: 24px;
  padding: 2px 3px;
  font-size: 16px;
  line-height: 16px;
  font-size: 14px;
}
.e-container .e-input-container {
  display: inline-block;
  width: 89.8%;
}
.e-container .e-input-container .e-float-input {
  display: inline-block;
  margin-right: 2.75%;
  vertical-align: baseline;
}
.e-container .e-input-container .e-float-input:first-child {
  width: 28%;
}
.e-container .e-input-container .e-float-input:first-child input {
  height: 32px;
}
.e-container .e-input-container .e-float-input.e-numeric {
  height: 32px;
  width: 14%;
}
.e-container .e-input-container .e-float-input.e-numeric input {
  height: 30px;
}
.e-container .e-input-container .e-float-input .e-float-text {
  text-align: center;
  text-align: left;
  font-weight: 400 !important; /* stylelint-disable-line declaration-no-important */
  color: var(--color-sf-content-text-color) !important; /* stylelint-disable-line declaration-no-important */
}
.e-container .e-switch-ctrl-btn {
  display: inline-block;
  padding: 8px;
  white-space: nowrap;
  width: 100%;
  border-top: 1px var(--color-sf-border-light) solid;
}
.e-container .e-switch-ctrl-btn .e-ctrl-btn {
  float: right;
  text-align: right;
  white-space: nowrap;
  width: 84.913%;
}
.e-container .e-switch-ctrl-btn .e-ctrl-btn .e-btn {
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  line-height: 18px;
}
.e-container .e-switch-ctrl-btn .e-ctrl-btn .e-btn.e-cancel {
  margin-left: 3.62%;
}
.e-container .e-switch-ctrl-btn .e-mode-switch-btn {
  background-origin: border-box;
  background-position: center;
  background-size: cover;
  float: left;
  margin-top: 0;
  overflow: hidden;
  white-space: nowrap;
}
.e-container .e-switch-ctrl-btn .e-mode-switch-btn:focus {
  -webkit-box-shadow: 0 0 2px 3px rgba(var(--color-sf-black), 0.2);
          box-shadow: 0 0 2px 3px rgba(var(--color-sf-black), 0.2);
}
.e-container .e-value-switch-btn, .e-container .e-mode-switch-btn {
  background-color: transparent;
  border-color: transparent;
  position: relative;
  width: 32px;
  height: 32px;
}
.e-container.e-color-picker .e-value-switch-btn:focus, .e-container.e-color-picker .e-value-switch-btn:hover, .e-container.e-color-picker .e-value-switch-btn:active {
  border-color: transparent;
  outline: none;
}
.e-container.e-color-picker .e-value-switch-btn:focus {
  outline: none;
}
.e-container.e-color-palette .e-palette + .e-selected-value, .e-container.e-color-palette .e-palette-group + .e-selected-value {
  padding: 16px 16px;
}
.e-container.e-color-palette .e-palette + .e-switch-ctrl-btn, .e-container.e-color-palette .e-palette-group + .e-switch-ctrl-btn {
  padding: 23px 8px 8px;
}
.e-container.e-color-palette .e-clr-pal-rec-wpr {
  padding: 0 0 10px 0;
}
.e-container.e-color-palette .e-clr-pal-rec-wpr .e-recent-clr-span {
  display: block;
  font-weight: 500;
  font-size: 14px;
  height: 32px;
  padding: 9px 10px;
  line-height: 18px;
  color: var(--color-sf-content-text-color);
}
.e-container.e-color-palette .e-clr-pal-rec-wpr .e-palette.e-recent-palette {
  padding: 8px 7px;
}
.e-container.e-color-palette .e-clr-pal-rec-wpr .e-palette.e-recent-palette .e-tile {
  height: 18px !important; /* stylelint-disable-line declaration-no-important */
  width: 18px !important; /* stylelint-disable-line declaration-no-important */
  margin: 0 3px;
  border: 0.5px solid var(--color-sf-border-light);
}
.e-container.e-color-palette .e-clr-pal-rec-wpr .e-palette.e-recent-palette .e-tile:hover {
  border: 0.5px solid transparent !important; /* stylelint-disable-line declaration-no-important */
}

.sf-colorpicker.e-container .e-input-container .e-float-input {
  float: left;
  margin-left: -1px;
}

.e-hide-opacity .e-container .e-slider-preview .e-colorpicker-slider {
  vertical-align: super;
}
.e-hide-opacity .e-container .e-slider-preview .e-preview-container {
  top: 0;
}
.e-hide-opacity .e-container .e-float-input:first-child {
  width: 36%;
}
.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 16.3%;
}

.e-hide-hex-value .e-container .e-float-input.e-numeric {
  width: 21.25%;
}
.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 29.59%;
}

.e-hide-valueswitcher .e-container .e-input-container {
  width: 100%;
}
.e-hide-valueswitcher .e-container .e-float-input:first-child {
  width: 28%;
}
.e-hide-valueswitcher .e-container .e-float-input.e-numeric {
  width: 13%;
}
.e-hide-valueswitcher .e-container .e-float-input:last-child {
  margin-right: 0;
}
.e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input:first-child {
  width: 36%;
}
.e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 17.58%;
}
.e-hide-valueswitcher.e-hide-hex-value .e-container .e-float-input.e-numeric {
  width: 22.18%;
}
.e-hide-valueswitcher.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 30.82%;
}

.e-rtl .e-container .e-hsv-container .e-hsv-color {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0, rgba(0, 0, 0, 0)), to(rgba(var(--color-sf-black)))), -webkit-gradient(linear, right top, left top, color-stop(0, rgba(var(--color-sf-white))), to(rgba(255, 255, 255, 0)));
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0, rgba(var(--color-sf-black)) 100%), linear-gradient(to left, rgba(var(--color-sf-white)) 0, rgba(255, 255, 255, 0) 100%);
}
.e-rtl .e-container .e-slider-preview .e-hue-slider .e-slider-track {
  background: -webkit-gradient(linear, right top, left top, color-stop(0, #f00), color-stop(16%, #ff0), color-stop(33%, #0f0), color-stop(50%, #0ff), color-stop(67%, #00f), color-stop(84%, #f0f), to(#ff0004));
  background: linear-gradient(to left, #f00 0, #ff0 16%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 84%, #ff0004 100%);
}
.e-rtl .e-container .e-slider-preview .e-preview-container {
  margin-left: 0;
  margin-right: 5.205%;
}
.e-rtl .e-container .e-selected-value .e-float-input {
  margin-left: 2.75%;
  margin-right: 0;
}
.e-rtl .e-container .e-selected-value .e-float-input input {
  padding-right: 0;
}
.e-rtl .e-container .e-selected-value .e-float-input .e-float-text {
  right: -12px !important; /* stylelint-disable-line declaration-no-important */
}
.e-rtl .e-container .e-selected-value .e-value-switch-btn {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.e-rtl .e-container .e-switch-ctrl-btn .e-mode-switch-btn {
  float: right;
}
.e-rtl .e-container .e-switch-ctrl-btn .e-ctrl-btn {
  float: left;
  text-align: left;
}
.e-rtl .e-container .e-switch-ctrl-btn .e-ctrl-btn .e-cancel {
  margin-left: 0;
  margin-right: 3.62%;
}
.e-rtl .e-container.sf-colorpicker .e-selected-value .e-input-container .e-float-input {
  float: right;
  margin-right: -1px;
}
.e-rtl.e-hide-valueswitcher .e-container .e-float-input:last-child {
  margin-left: 0;
}

.e-hide-switchable-value .e-container .e-input-container .e-float-input:first-child {
  width: 100%;
}

.e-popup.e-tooltip-wrap.e-color-picker-tooltip {
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 0%;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  -webkit-box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
          box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
  cursor: pointer;
  min-width: 26px;
  -webkit-transform: translateY(18px) rotate(45deg) scale(0.01);
          transform: translateY(18px) rotate(45deg) scale(0.01);
  -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: -webkit-transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), -webkit-transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}
.e-popup.e-tooltip-wrap.e-color-picker-tooltip .e-tip-content {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 8px;
  border-radius: 50%;
  height: 24px;
  position: relative;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  width: 24px;
}

.e-split-preview,
.e-tip-transparent {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.e-container {
  background-color: var(--color-sf-flyout-bg-color);
}
.e-container .e-palette .e-tile:hover {
  border-color: rgba(var(--color-sf-white));
}
.e-container .e-palette .e-tile.e-selected {
  border-color: rgba(var(--color-sf-white));
}
.e-container .e-handler.e-hide-handler, .e-container .e-handle.e-hide-handler {
  background-color: transparent;
  border-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.e-container .e-control-wrapper.e-slider-container .e-slider.e-hue-slider .e-handle:not(.e-tab-handle), .e-container .e-control-wrapper.e-slider-container .e-slider.e-opacity-slider .e-handle:not(.e-tab-handle) {
  background-color: var(--color-sf-transparent);
  border-color: var(--color-sf-transparent);
  border: 1px solid rgba(var(--color-sf-white), 1);
}
.e-container .e-control-wrapper.e-slider-container .e-slider.e-hue-slider .e-handle:not(.e-tab-handle).e-handle-start:not(.e-tab-handle), .e-container .e-control-wrapper.e-slider-container .e-slider.e-opacity-slider .e-handle:not(.e-tab-handle).e-handle-start:not(.e-tab-handle) {
  background-color: var(--color-sf-transparent);
  border-color: var(--color-sf-transparent);
  border: 1px solid rgba(var(--color-sf-white), 1);
}
.e-container .e-control-wrapper.e-slider-container .e-slider.e-hue-slider .e-handle.e-tab-handle, .e-container .e-control-wrapper.e-slider-container .e-slider.e-opacity-slider .e-handle.e-tab-handle {
  background-color: var(--color-sf-transparent);
  border-color: rgba(var(--color-sf-white), 1);
  -webkit-box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
          box-shadow: 0 0 0 1px rgba(var(--color-sf-black), 1);
  border: 1px solid rgba(var(--color-sf-white), 1);
}

.e-colorpicker-wrapper.e-disabled .e-value-switch-btn, .e-colorpicker-wrapper.e-disabled .e-mode-switch-btn,
.e-colorpicker-container.e-disabled .e-value-switch-btn,
.e-colorpicker-container.e-disabled .e-mode-switch-btn {
  color: var(--color-sf-secondary-text-color-disabled);
}
.e-colorpicker-wrapper.e-disabled .e-value-switch-btn:focus, .e-colorpicker-wrapper.e-disabled .e-mode-switch-btn:focus,
.e-colorpicker-container.e-disabled .e-value-switch-btn:focus,
.e-colorpicker-container.e-disabled .e-mode-switch-btn:focus {
  background-color: transparent;
  color: var(--color-sf-secondary-text-color-disabled);
  outline: none;
  outline-offset: unset;
}
.e-colorpicker-wrapper.e-disabled .e-value-switch-btn:active, .e-colorpicker-wrapper.e-disabled .e-mode-switch-btn:active,
.e-colorpicker-container.e-disabled .e-value-switch-btn:active,
.e-colorpicker-container.e-disabled .e-mode-switch-btn:active {
  background-color: transparent;
  color: var(--color-sf-secondary-text-color-disabled);
}
.e-colorpicker-wrapper .e-icon-btn:disabled, .e-colorpicker-wrapper .e-icon-btn.e-disabled,
.e-colorpicker-container .e-icon-btn:disabled,
.e-colorpicker-container .e-icon-btn.e-disabled {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.e-popup.e-tooltip-wrap.e-color-picker-tooltip {
  background-color: rgba(var(--color-sf-white));
  border-color: rgba(var(--color-sf-white));
}

.e-rating-container {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: -webkit-max-content;
  min-width: -moz-max-content;
  min-width: max-content;
}
.e-rating-container .e-rating {
  display: none;
}
.e-rating-container .e-rating-item-list {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  margin: 4px;
}
.e-rating-container .e-rating-item-list:focus-visible:not(:hover) .e-rating-focus:not(:has(svg)),
.e-rating-container .e-rating-item-list:focus-visible:not(:hover) .e-rating-focus .e-rating-item:has(svg) {
  outline: 1px solid;
}
.e-rating-container .e-rating-item-list:focus-visible {
  outline: none;
}
.e-rating-container.e-rating-readonly {
  cursor: default;
  pointer-events: none;
}
.e-rating-container .e-rating-item-container {
  cursor: pointer;
  font-size: 22px;
  padding: 4px;
  display: block;
  min-width: calc(1em + 8px);
  min-height: calc(1em + 8px);
  position: relative;
}
.e-rating-container .e-rating-item-container:has(svg) {
  line-height: 13px;
}
.e-rating-container .e-rating-item-container .e-rating-item {
  display: block;
}
.e-rating-container.e-rating-animation.e-touch-select:not(.e-disabled) .e-rating-item-container.e-rating-focus .e-rating-item, .e-rating-container.e-rating-animation:not(.e-disabled, .e-rating-touch) .e-rating-item-container:hover .e-rating-item {
  -webkit-transition: -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: scale(1.4);
          transform: scale(1.4);
  pointer-events: none;
}
.e-rating-container.e-disabled {
  cursor: default;
  pointer-events: none;
}
.e-rating-container.e-rating-hidden {
  display: none;
}
.e-rating-container .e-reset {
  cursor: pointer;
  margin: auto 2px 6px;
  font-size: 18px;
  padding: 2px;
}
.e-rating-container .e-reset.e-disabled {
  cursor: default;
}
.e-rating-container .e-reset:focus-visible {
  outline: 1px solid;
  border-radius: 4px;
}
.e-rating-container .e-rating-label {
  font-size: 12px;
  line-height: 18px;
}
.e-rating-container .e-rating-label.e-label-right, .e-rating-container .e-rating-label.e-label-left {
  min-width: 50px;
}
.e-rating-container .e-rating-label.e-label-bottom, .e-rating-container .e-rating-label.e-label-top {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  width: 0;
  text-align: center;
}
.e-rating-container .e-rating-label.e-label-right {
  text-align: left;
  margin: auto 8px;
}
.e-rating-container .e-rating-label.e-label-left {
  text-align: right;
  margin: auto 11px;
}
.e-rating-container .e-rating-label.e-label-top {
  margin: 8px 0;
}
.e-rating-container .e-rating-label.e-label-bottom {
  margin: 4px 0;
}
.e-rating-container.e-rtl .e-rating-label.e-label-right {
  text-align: right;
}
.e-rating-container.e-rtl .e-rating-label.e-label-left {
  text-align: left;
}
.e-rating-container .e-tooltip-wrap.e-rating-tooltip {
  display: none;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  left: 50%;
  top: -8px;
  -webkit-transform: translate(-50%, -100%);
          transform: translate(-50%, -100%);
}
.e-rating-container .e-tooltip-wrap.e-rating-tooltip.e-show-tooltip {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.e-rating-container .e-tooltip-wrap.e-rating-tooltip .e-tip-content {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}
.e-rating-container .e-tooltip-wrap.e-rating-tooltip .e-arrow-tip.e-tip-bottom {
  left: 50%;
  top: 100%;
  -webkit-transform: translate(-50%, 0);
          transform: translate(-50%, 0);
}
.e-rating-container .e-tooltip-wrap.e-rating-tooltip .e-arrow-tip-inner.e-tip-bottom {
  top: -6px;
}

.e-rating-tooltip .e-tip-content {
  text-align: center;
}

.e-small.e-rating-container .e-rating-item-container,
.e-small .e-rating-container .e-rating-item-container {
  font-size: 16px;
}
.e-small.e-rating-container .e-rating-item-container:has(svg),
.e-small .e-rating-container .e-rating-item-container:has(svg) {
  line-height: 6px;
}
.e-small.e-rating-container .e-rating-label,
.e-small .e-rating-container .e-rating-label {
  font-size: 10px;
  line-height: 16px;
}
.e-small.e-rating-container .e-reset,
.e-small .e-rating-container .e-reset {
  font-size: 12px;
}

.e-rating-container .e-rating-item-list:focus-visible:not(:hover) .e-rating-focus:not(:has(svg)),
.e-rating-container .e-rating-item-list:focus-visible:not(:hover) .e-rating-focus .e-rating-item:has(svg) {
  outline-color: var(--color-sf-rating-selected-color);
  border-radius: 4px;
}
.e-rating-container .e-reset {
  color: var(--color-sf-rating-unrated-color);
}
.e-rating-container .e-reset:hover {
  color: var(--color-sf-rating-selected-hover-color);
}
.e-rating-container .e-reset.e-disabled {
  color: var(--color-sf-rating-selected-disabled-color);
  opacity: 1;
}
.e-rating-container .e-rating-item-container .e-rating-icon {
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 1px var(--color-sf-rating-unrated-color);
  background: none;
}
.e-rating-container .e-rating-item-container.e-rating-selected .e-rating-icon, .e-rating-container .e-rating-item-container.e-rating-intermediate .e-rating-icon {
  background: -webkit-gradient(linear, left top, right top, from(var(--color-sf-rating-selected-color)), to(transparent));
  background: linear-gradient(to right, var(--color-sf-rating-selected-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
  -webkit-text-stroke: 1px var(--color-sf-rating-selected-color);
}
.e-rating-container .e-rating-item-container.e-selected-value .e-rating-icon {
  -webkit-text-stroke: 1px var(--color-sf-rating-selected-hover-color);
}
.e-rating-container .e-rating-item-list:hover .e-rating-item-container.e-rating-selected .e-rating-icon, .e-rating-container .e-rating-item-list:hover .e-rating-item-container.e-rating-intermediate .e-rating-icon {
  background: -webkit-gradient(linear, left top, right top, from(var(--color-sf-rating-selected-hover-color)), to(transparent));
  background: linear-gradient(to right, var(--color-sf-rating-selected-hover-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
  -webkit-text-stroke: 1px var(--color-sf-rating-selected-hover-color);
}
.e-rating-container .e-rating-item-list .e-rating-item-container.e-rating-selected:active .e-rating-icon, .e-rating-container .e-rating-item-list .e-rating-item-container.e-rating-intermediate:active .e-rating-icon {
  background: -webkit-gradient(linear, left top, right top, from(var(--color-sf-rating-pressed-color)), to(transparent));
  background: linear-gradient(to right, var(--color-sf-rating-pressed-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
  -webkit-text-stroke: 1px var(--color-sf-rating-pressed-color);
}
.e-rating-container.e-rtl .e-rating-item-container.e-rating-selected .e-rating-icon, .e-rating-container.e-rtl .e-rating-item-container.e-rating-intermediate .e-rating-icon {
  background: -webkit-gradient(linear, right top, left top, from(var(--color-sf-rating-selected-color)), to(transparent));
  background: linear-gradient(to left, var(--color-sf-rating-selected-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
}
.e-rating-container.e-rtl .e-rating-item-list:hover .e-rating-item-container.e-rating-selected .e-rating-icon, .e-rating-container.e-rtl .e-rating-item-list:hover .e-rating-item-container.e-rating-intermediate .e-rating-icon {
  background: -webkit-gradient(linear, right top, left top, from(var(--color-sf-rating-selected-hover-color)), to(transparent));
  background: linear-gradient(to left, var(--color-sf-rating-selected-hover-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
}
.e-rating-container.e-rtl .e-rating-item-list .e-rating-item-container.e-rating-selected:active .e-rating-icon, .e-rating-container.e-rtl .e-rating-item-list .e-rating-item-container.e-rating-intermediate:active .e-rating-icon {
  background: -webkit-gradient(linear, right top, left top, from(var(--color-sf-rating-pressed-color)), to(transparent));
  background: linear-gradient(to left, var(--color-sf-rating-pressed-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
}
.e-rating-container.e-disabled {
  opacity: 1;
}
.e-rating-container.e-disabled .e-rating-item-container .e-rating-icon {
  -webkit-text-stroke: 1px var(--color-sf-rating-unrated-disabled-color);
}
.e-rating-container.e-disabled .e-rating-item-container.e-rating-selected .e-rating-icon, .e-rating-container.e-disabled .e-rating-item-container.e-rating-intermediate .e-rating-icon {
  background: -webkit-gradient(linear, left top, right top, from(var(--color-sf-rating-selected-disabled-color)), to(transparent));
  background: linear-gradient(to right, var(--color-sf-rating-selected-disabled-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
  -webkit-text-stroke: 1px var(--color-sf-rating-selected-disabled-color);
}
.e-rating-container.e-disabled.e-rtl .e-rating-item-container .e-rating-icon {
  background: -webkit-gradient(linear, right top, left top, from(var(--color-sf-rating-unrated-disabled-color)), to(transparent));
  background: linear-gradient(to left, var(--color-sf-rating-unrated-disabled-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
}
.e-rating-container.e-disabled.e-rtl .e-rating-item-container.e-rating-selected .e-rating-icon, .e-rating-container.e-disabled.e-rtl .e-rating-item-container.e-rating-intermediate .e-rating-icon {
  background: -webkit-gradient(linear, right top, left top, from(var(--color-sf-rating-selected-disabled-color)), to(transparent));
  background: linear-gradient(to left, var(--color-sf-rating-selected-disabled-color) var(--rating-value), transparent var(--rating-value));
  background-clip: text;
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-background-clip: text;
  /* stylelint-enable property-no-vendor-prefix */
}

/*! Form tailwind theme wise override definitions and variables */
/*! data-form layout */
.e-data-form {
  /* stylelint-disable property-no-vendor-prefix */
  /* stylelint-enable property-no-vendor-prefix */
}
.e-data-form .e-form-label {
  font-size: 12px;
  margin-bottom: 4px;
  font-weight: 400;
}
.e-data-form .e-form-layout {
  display: grid;
}
.e-data-form .e-grid-col-2 {
  -ms-grid-columns: repeat(2, 1fr);
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.e-data-form .e-grid-col-3 {
  -ms-grid-columns: repeat(3, 1fr);
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.e-data-form .e-grid-col-4 {
  -ms-grid-columns: repeat(4, 1fr);
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.e-data-form .e-grid-col-5 {
  -ms-grid-columns: repeat(5, 1fr);
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.e-data-form .e-grid-col-6 {
  -ms-grid-columns: repeat(6, 1fr);
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.e-data-form .e-grid-col-7 {
  -ms-grid-columns: repeat(7, 1fr);
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.e-data-form .e-grid-col-8 {
  -ms-grid-columns: repeat(8, 1fr);
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.e-data-form .e-grid-col-9 {
  -ms-grid-columns: repeat(9, 1fr);
  grid-template-columns: repeat(9, minmax(0, 1fr));
}
.e-data-form .e-grid-col-10 {
  -ms-grid-columns: repeat(10, 1fr);
  grid-template-columns: repeat(10, minmax(0, 1fr));
}
.e-data-form .e-grid-col-11 {
  -ms-grid-columns: repeat(11, 1fr);
  grid-template-columns: repeat(11, minmax(0, 1fr));
}
.e-data-form .e-grid-col-12 {
  -ms-grid-columns: repeat(12, 1fr);
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.e-data-form .e-colspan-1 {
  grid-column: span 1/span 1;
}
.e-data-form .e-colspan-2 {
  grid-column: span 2/span 2;
}
.e-data-form .e-colspan-3 {
  grid-column: span 3/span 3;
}
.e-data-form .e-colspan-4 {
  grid-column: span 4/span 4;
}
.e-data-form .e-colspan-5 {
  grid-column: span 5/span 5;
}
.e-data-form .e-colspan-6 {
  grid-column: span 6/span 6;
}
.e-data-form .e-colspan-7 {
  grid-column: span 7/span 7;
}
.e-data-form .e-colspan-8 {
  grid-column: span 8/span 8;
}
.e-data-form .e-colspan-9 {
  grid-column: span 9/span 9;
}
.e-data-form .e-colspan-10 {
  grid-column: span 10/span 10;
}
.e-data-form .e-colspan-11 {
  grid-column: span 11/span 11;
}
.e-data-form .e-colspan-12 {
  grid-column: span 12/span 12;
}
.e-data-form .e-label-position-left .e-form-item-wrapper {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  max-width: calc(75% - 12px);
}
.e-data-form .e-label-position-left .e-form-label {
  margin-right: 12px;
  width: 25%;
  text-align: end;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
.e-data-form .e-label-position-left .validation-message {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.e-data-form .e-form-group {
  border: none;
  padding: 0;
  margin: 0;
  margin-top: 20px;
}
.e-data-form .e-form-group .e-group-title {
  position: relative;
  display: inline-block;
  padding-bottom: 8px;
  /* Gap between text and border */
  margin-bottom: 4px;
  font-size: 16px;
}
.e-data-form .e-form-group .e-group-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #d1d5db;
}
.e-data-form .e-label-position-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-top: 20px;
}
.e-data-form .e-label-position-top .e-label-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.e-data-form .e-label-position-top .e-label-right .e-switch-wrapper + .e-form-label {
  margin-top: 3px;
  margin-left: 5px;
}
.e-data-form .e-label-position-top .e-label-right .e-checkbox-wrapper + .e-form-label {
  margin-top: 5px;
}
.e-data-form .e-label-position-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  text-align: center;
  margin-top: 20px;
}
.e-data-form .e-button-left {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
.e-data-form .e-button-right {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.e-data-form .e-button-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.e-data-form .e-button-left, .e-data-form .e-button-right, .e-data-form .e-button-center, .e-data-form .e-button-stretch {
  margin-top: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.e-data-form .e-button-left .e-btn, .e-data-form .e-button-right .e-btn, .e-data-form .e-button-center .e-btn, .e-data-form .e-button-stretch .e-btn {
  border-radius: 20px;
  padding: 7px 16px;
}
.e-data-form .e-button-stretch .e-btn {
  width: 100%;
}
.e-data-form > :first-child.e-form-layout {
  margin-top: 0;
}

/*! data-form theme */
.e-data-form .e-form-label {
  color: var(--color-sf-content-text-color-alt1);
}
.e-data-form .e-group-title {
  color: var(--color-sf-content-text-color-alt2);
}
.e-data-form .validation-message {
  color: var(--color-sf-danger);
  font-size: 12px;
  font-style: italic;
}

.e-tooltip-wrap.e-popup.e-error-tooltip {
  background-color: var(--color-sf-danger-light);
  border: var(--color-sf-danger-light);
}
.e-tooltip-wrap.e-popup.e-error-tooltip .e-tip-content .validation-message {
  color: var(--color-sf-danger);
}
.e-tooltip-wrap.e-popup.e-error-tooltip .e-arrow-tip .e-arrow-tip-outer.e-tip-top, .e-tooltip-wrap.e-popup.e-error-tooltip .e-arrow-tip .e-arrow-tip-inner.e-tip-top {
  border-bottom-color: var(--color-sf-danger-light);
  color: var(--color-sf-danger-light);
}

.e-otpinput {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
.e-otpinput:has(.e-otp-separator):not(:empty) {
  gap: 4px;
}
.e-otpinput .e-otp-input-field {
  min-width: 32px;
  min-height: 32px;
  text-align: center;
  padding: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  border: none;
}
.e-otpinput .e-otp-input-fieldinput[type=password], .e-otpinput .e-otp-input-field[type=password] {
  font-size: 18px;
}
.e-otpinput .e-otp-input-field.e-input.e-otp-input-focus:focus {
  padding-bottom: 0;
}
.e-otpinput.e-outline .e-otp-input-field {
  border-radius: 6px;
}
.e-otpinput.e-underlined .e-otp-input-field {
  border-radius: 0;
}
.e-otpinput.e-filled .e-otp-input-field {
  border-radius: 0;
  padding: 0;
}
.e-otpinput .e-otp-separator {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.e-otpinput input[type=number]::-webkit-inner-spin-button,
.e-otpinput [type=number]::-webkit-outer-spin-button {
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-appearance: none;
  /* stylelint-enable property-no-vendor-prefix */
  margin: 0;
}
.e-otpinput input[type=number] {
  /* stylelint-disable property-no-vendor-prefix */
  -moz-appearance: textfield;
  /* stylelint-enable property-no-vendor-prefix */
}

.e-small.e-otpinput,
.e-small .e-otpinput {
  gap: 8px;
}
.e-small.e-otpinput:has(.e-otp-separator):not(:empty),
.e-small .e-otpinput:has(.e-otp-separator):not(:empty) {
  gap: 4px;
}
.e-small.e-otpinput .e-otp-input-field,
.e-small .e-otpinput .e-otp-input-field {
  min-width: 24px;
  min-height: 24px;
  font-size: 12px;
  line-height: 18px;
  padding: 0;
}
.e-small.e-otpinput .e-otp-input-fieldinput[type=password], .e-small.e-otpinput .e-otp-input-field[type=password],
.e-small .e-otpinput .e-otp-input-fieldinput[type=password],
.e-small .e-otpinput .e-otp-input-field[type=password] {
  font-size: 18px;
}
.e-small.e-otpinput .e-otp-input-field.e-input.e-otp-input-focus:focus,
.e-small .e-otpinput .e-otp-input-field.e-input.e-otp-input-focus:focus {
  padding-bottom: 0;
}
.e-small.e-otpinput .e-otp-separator,
.e-small .e-otpinput .e-otp-separator {
  font-size: 12px;
  line-height: 18px;
}
.e-small.e-otpinput.e-filled .e-otp-input-field,
.e-small .e-otpinput.e-filled .e-otp-input-field {
  padding: 0;
}

.e-otpinput .e-otp-input-field {
  color: var(--color-sf-content-text-color);
  background: var(--color-sf-content-bg-color);
}
.e-otpinput .e-otp-separator {
  color: var(--color-sf-border);
}
.e-otpinput.e-outline .e-otp-input-field {
  border: 1px solid var(--color-sf-border);
}
.e-otpinput.e-outline .e-otp-input-field, .e-otpinput.e-underlined .e-otp-input-field, .e-otpinput.e-filled .e-otp-input-field {
  border-bottom: 2px solid var(--color-sf-border);
}
.e-otpinput.e-outline .e-otp-input-field:disabled, .e-otpinput.e-underlined .e-otp-input-field:disabled, .e-otpinput.e-filled .e-otp-input-field:disabled {
  border-color: var(--color-sf-content-bg-color-alt2);
  color: var(--color-sf-content-text-color-disabled);
  background: var(--color-sf-content-bg-color-disabled);
}
.e-otpinput.e-filled .e-otp-input-field {
  background: var(--color-sf-content-bg-color-alt1);
}
.e-otpinput.e-underlined .e-otp-input-field {
  background: var(--color-sf-transparent);
}
.e-otpinput.e-error.e-outline .e-otp-input-field,
.e-otpinput.e-error.e-outline .e-otp-input-field.e-otp-input-focus, .e-otpinput.e-error.e-underlined .e-otp-input-field,
.e-otpinput.e-error.e-underlined .e-otp-input-field.e-otp-input-focus, .e-otpinput.e-error.e-filled .e-otp-input-field,
.e-otpinput.e-error.e-filled .e-otp-input-field.e-otp-input-focus {
  border-color: var(--color-sf-danger);
}
.e-otpinput.e-success.e-outline .e-otp-input-field,
.e-otpinput.e-success.e-outline .e-otp-input-field.e-otp-input-focus, .e-otpinput.e-success.e-underlined .e-otp-input-field,
.e-otpinput.e-success.e-underlined .e-otp-input-field.e-otp-input-focus, .e-otpinput.e-success.e-filled .e-otp-input-field,
.e-otpinput.e-success.e-filled .e-otp-input-field.e-otp-input-focus {
  border-color: var(--color-sf-success);
}
.e-otpinput.e-warning.e-outline .e-otp-input-field,
.e-otpinput.e-warning.e-outline .e-otp-input-field.e-otp-input-focus, .e-otpinput.e-warning.e-underlined .e-otp-input-field,
.e-otpinput.e-warning.e-underlined .e-otp-input-field.e-otp-input-focus, .e-otpinput.e-warning.e-filled .e-otp-input-field,
.e-otpinput.e-warning.e-filled .e-otp-input-field.e-otp-input-focus {
  border-color: var(--color-sf-warning);
}

.e-listen-icon::before {
  content: "\e91c";
}

.e-listen-stop::before {
  content: "\e919";
}

.e-speech-to-text.e-btn {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 9999px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  min-width: 40px;
  min-height: 40px;
  padding: 0 14px;
}
.e-speech-to-text.e-btn.e-round {
  width: 40px;
  height: 40px;
  padding: 0;
}
.e-speech-to-text.e-btn .e-btn-icon {
  font-size: 16px;
}
.e-speech-to-text.e-btn:disabled {
  pointer-events: none;
}
.e-speech-to-text.e-btn.e-listening-state {
  -webkit-animation: listening 1.2s infinite;
          animation: listening 1.2s infinite;
}

.e-speech-to-text.e-round .e-btn-icon {
  font-size: 16px;
}

@-webkit-keyframes listening {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes listening {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.e-speech-to-text {
  -webkit-box-shadow: 0 4px 6px -4px rgba(var(--color-sf-black), 0.1), 0 10px 15px -3px rgba(var(--color-sf-black), 0.1);
          box-shadow: 0 4px 6px -4px rgba(var(--color-sf-black), 0.1), 0 10px 15px -3px rgba(var(--color-sf-black), 0.1);
}
.e-speech-to-text:hover:not(:focus), .e-speech-to-text:active, .e-speech-to-text.e-active, .e-speech-to-text:disabled {
  -webkit-box-shadow: 0 4px 6px -4px rgba(var(--color-sf-black), 0.1), 0 10px 15px -3px rgba(var(--color-sf-black), 0.1);
          box-shadow: 0 4px 6px -4px rgba(var(--color-sf-black), 0.1), 0 10px 15px -3px rgba(var(--color-sf-black), 0.1);
}
.e-speech-to-text:focus-visible {
  -webkit-box-shadow: 0 0 0 2px var(--color-sf-content-bg-color), 0 0 0 4px var(--color-sf-primary) !important;
          box-shadow: 0 0 0 2px var(--color-sf-content-bg-color), 0 0 0 4px var(--color-sf-primary) !important; /* stylelint-disable-line declaration-no-important */
}
.e-speech-to-text.e-flat {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.smart-textarea-suggestion-overlay {
  display: none;
  position: absolute;
  padding: 0.25rem 0.75rem;
  border-radius: 0.3rem;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  margin-right: 0.5rem;
}

.smart-textarea-suggestion-overlay.smart-textarea-suggestion-overlay-visible {
  display: block;
}

.smart-textarea-caret {
  position: absolute;
  width: 0.8px;
  display: none;
  -webkit-animation: caret-blink 1.025s step-end infinite;
          animation: caret-blink 1.025s step-end infinite;
}

@-webkit-keyframes caret-blink {
  from, to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes caret-blink {
  from, to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.smart-textarea-suggestion-overlay {
  background-color: var(--color-sf-white);
  color: var(--color-sf-black);
  -webkit-box-shadow: 0 1.25px 4px 0 rgba(0, 0, 0, 0.4);
          box-shadow: 0 1.25px 4px 0 rgba(0, 0, 0, 0.4);
}

[data-suggestion-visible]::-moz-selection {
  color: #999 !important; /* stylelint-disable-line declaration-no-important */
  background: none !important; /* stylelint-disable-line declaration-no-important */
}

[data-suggestion-visible]::selection {
  color: #999 !important; /* stylelint-disable-line declaration-no-important */
  background: none !important; /* stylelint-disable-line declaration-no-important */
}

.smart-textarea-caret {
  background: #ff0000;
}