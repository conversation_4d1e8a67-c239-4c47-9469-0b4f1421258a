@use 'sass:color';

//default
/* stylelint-disable */
$treeview-font-size: 14px !default;
$treeview-icon-font-size: 8px !default;

$treeview-image-font-size: 14px !default;
$treeview-big-font-size: 16px !default;
$treeview-big-text-color: $gray-900 !default;
$treeview-big-icon-font-size: 10px !default;
$treeview-big-image-font-size: 16px !default;

$treeview-icon-color: $gray-700 !default;
$treeview-text-color: $gray-900 !default;
$treeview-item-border-color: transparent !default;
$treeview-item-active-bg: $primary !default;
$treeview-icon-active-color: $primary-font !default;
$treeview-text-active-color: $primary-font !default;
$treeview-item-active-border-color: $primary !default;
$treeview-item-hover-bg: color.adjust($gray-100, $lightness: -2%) !default;
$treeview-icon-hover-color: $gray-700 !default;
$treeview-text-hover-color: $gray-900 !default;
$treeview-item-hover-border-color: color.adjust( $gray-100, $lightness: -2%)!default;
$treeview-item-active-hover-bg: color.adjust($primary, $lightness: -7.5%)!default;
$treeview-icon-active-hover-color: $primary-font !default;
$treeview-text-active-hover-color: $primary-font !default;
$treeview-item-active-hover-border-color: color.adjust($primary, $lightness: -7.5%) !default;
$treeview-text-disable-color:  $gray-600 !default;
$treeview-icon-disable-color: $gray-600  !default;
$treeview-drag-line-bg: $primary !default;
$treeview-drag-line-color: $gray-500 !default;
$treeview-popup-bg-color: transparent !default;
$treeview-popup-border-color: transparent !default;
$treeview-drop-count-bg: $primary !default;
$treeview-drop-count-border: $primary !default;
$treeview-drop-count-color: $primary-font !default;
$treeview-drag-item-bg: $gray-200 !default;
$treeview-drag-item-color: $gray-900 !default ;
$treeview-drag-icon-color: $gray-700 !default;
$treeview-item-active-check-bg: $primary-font !default;
$treeview-item-active-check-border-color: $primary-font !default;
$treeview-item-active-check-color: $primary !default;
//enddefault
//dimensions
$treeview-skin-name: 'bootstrap4';
$treeview-item-height: 32px !default;
$treeview-text-height: 30px !default;
$treeview-input-height: 30px !default;
$treeview-root-ul-padding: 0 0 0 14px !default;
$treeview-rtl-root-ul-padding: 0 14px 0 0 !default;
$treeview-child-ul-padding: 0 0 0 14px !default;
$treeview-rtl-child-ul-padding: 0 14px 0 0 !default;
$treeview-text-wrap-padding: 0 0 0 24px !default;
$treeview-rtl-text-wrap-padding: 0 24px 0 0 !default;
$treeview-icon-size: 20px !default;
$treeview-big-icon-size: 24px !default;
$treeview-icon-margin: 0 4px 0 -24px !default;
$treeview-drag-icon-margin: 0 8px 8px -24px;
$treeview-big-icon-margin: 0 0 0 -24px !default;
$treeview-rtl-icon-margin: -0.5px -20px 0 0 !default;
$treeview-rtl-drag-margin: 10px -13px -1px 0;
$treeview-icon-padding: 6px !default;
$treeview-text-padding: 0 8px 0 6px !default;
$treeview-text-margin: 0 !default;
$treeview-image-size: 18px !default;
$treeview-icon-size: 16px !default;
$treeview-image-margin: 2.5px 0 0 5px !default;
$treeview-navigable-image-icon-margin: 0 8px 0 12px !default;
$treeview-navigable-uncheck-image-margin: 0 8px 0 2px !default;
$treeview-navigable-image-icon-rtl-margin: 0 12px 0 8px !default;
$treeview-navigable-uncheck-image-rtl-margin: 0 2px 0 8px !default;
$treeview-navigable-icon-image-margin: 0 8px 0 0 !default;
$treeview-navigable-icon-image-margin-reverse: 0 0 0 8px !default;
$treeview-navigable-check-margin-bigger: 0 0 0 10px !default;
$treeview-navigable-check-margin-bigger-reverse: 0 10px 0 0 !default;
$treeview-navigable-icon-image-anchor-margin-bigger: 0 12px 0 4px !default;
$treeview-navigable-icon-image-anchor-margin-reverse-bigger: 0 4px 0 12px !default;
$treeview-navigable-icon-image-anchor-margin: 0 8px 0 4px !default;
$treeview-navigable-icon-image-anchor-margin-reverse: 0 4px 0 8px !default;
$treeview-navigable-rtl-margin-reverse: 0 12px 0 0 !default;
$treeview-rtl-image-margin: 2px 4px 0 0 !default;
$treeview-input-padding: 0 7px !default;
$treeview-image-text-padding: 0 8px !default;
$treeview-icon-image-margin: 0 0 0 10px !default;
$treeview-rtl-icon-image-margin: 0 10px 0 0 !default;
$treeview-check-margin: 0 0 0 5px !default;
$treeview-rtl-check-margin: 0 5px 0 0 !default;
$treeview-check-text-padding: 0 8px !default;
$treeview-check-image-margin: 0 0 0 12px !default;
$treeview-rtl-check-image-margin: 5px 12px 0 0 !default;
$treeview-drop-count-border-size: 0px !default;
$treeview-big-drop-count-border-size: 1px !default;
$treeview-drop-count-font-size: 14px !default;
$treeview-drag-item-box-shadow:  0 0 3px 3px rgba(0, 0, 0, 0.25) !default;
$treeview-edit-wrap-width: calc(100% - 2px) !default;
$treeview-check-wrap-width: calc(100% - 18px) !default;
$treeview-check-icon-wrap-width: calc(100% - 55px) !default;
$treeview-check-icon-img-wrap-width: calc(100% - 83px) !default;
$treeview-icon-wrap-width: calc(100% - 29px) !default;
$treeview-icon-img-wrap-width: calc(100% - 57px) !default;

$treeview-big-item-height: 40px !default;
$treeview-big-text-height: 36px !default;
$treeview-big-input-height: 38px !default;
$treeview-big-text-padding: 0 12px 0 8px !default;
$treeview-big-drag-text-padding: 2px 12px 0 4px !default;
$treeview-big-drag-icon-margin:-6px 0px 5px -14px !default;
$treeview-big-drag-icon-padding: 0px 12px 0px 4px !default;
$treeview-big-drag-before-icon-padding: 12px !default;
$treeview-big-drag-item-text-padding-left: 12px !default;
$treeview-drag-text-padding: 0px 8px 0px 4px !default;
$treeview-big-input-padding: 0 9px !default;
$treeview-big-icon-padding: 7px !default;
$treeview-big-image-text-padding: 0 12px !default;
$treeview-big-image-margin: 4px 0 0 10px !default;
$treeview-big-rtl-icon-margin: 0 -24px 1px 0;
$treeview-big-rtl-image-margin: 2px 10px 0 0 !default;
$treeview-big-icon-image-margin: 0 0 0 10px !default;
$treeview-big-rtl-icon-image-margin: 0 10px 0 0 !default;
$treeview-big-check-margin: 0 0 0 10px !default;
$treeview-big-check-text-padding: 0 12px !default;
$treeview-big-rtl-check-margin: 0 10px 0 0 !default;
$treeview-big-check-image-margin: 0 0 0 16px !default;
$treeview-big-rtl-check-image-margin: 4px 16px 2px 0 !default;
$treeview-big-check-wrap-width: calc(100% - 24px) !default;
$treeview-big-check-icon-wrap-width: calc(100% - 65px) !default;
$treeview-big-check-icon-img-wrap-width: calc(100% - 93px) !default;
$treeview-big-icon-wrap-width: calc(100% - 34px) !default;
$treeview-big-icon-img-wrap-width: calc(100% - 62px) !default;
$treeview-drag-icon-padding: 8px !default;
$treeview-drag-icon-padding-right: 4px !default;
$treeview-drag-icon-font-size: 14px;
$treeview-font-family: $font-family !default;
$treeview-drag-icon-font-size: 12px !default;

$treeview-font-family: $font-family !default;
$treeview-drag-icon-font-size: 12px !default;
$treeview-big-drag-icon-padding: 6px 4px 6px 12px !default;
$treeview-big-drag-icon-font-size: 16px;
$ripple-size: -7px !default;
$ripple-height: 32px !default;
$ripple-width: 32px !default;
