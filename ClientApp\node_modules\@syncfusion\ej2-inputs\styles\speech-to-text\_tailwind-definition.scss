// Speechtotext component styles

$stt-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .2), 0 -2px 15px -3px rgba(0, 0, 0, .1), 0 6px 10px rgba(0, 0, 0, .14) !default;
$stt-focus-box-shadow: $stt-box-shadow, $shadow-focus-ring2 !default;
$stt-border-radius: 9999px !default;
$stt-font-weight: 500 !default;
$stt-font-size: 14px !default;
$stt-line-height: 16px !default;
$stt-round-width: 40px !default;
$stt-round-height: 40px !default;
$stt-btn-font-size: 16px !default;
$stt-min-height: 40px !default;
$stt-min-width: 40px !default;
$stt-padding: 0 16px !default;

$stt-bigger-border-radius: $stt-border-radius !default;
$stt-bigger-font-weight: 500 !default;
$stt-bigger-font-size: 16px !default;
$stt-bigger-line-height: 24px !default;
$stt-bigger-round-width: 52px !default;
$stt-bigger-round-height: 52px !default;
$stt-bigger-btn-font-size: 20px !default;
$stt-bigger-min-height: 52px !default;
$stt-bigger-min-width: 52px !default;
$stt-bigger-padding: 0 20px !default;
