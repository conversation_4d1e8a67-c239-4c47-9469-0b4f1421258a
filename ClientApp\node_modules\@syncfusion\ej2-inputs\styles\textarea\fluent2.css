





/* stylelint-disable property-no-vendor-prefix */
@-webkit-keyframes e-input-ripple {
  100% {
    opacity: 0;
    -webkit-transform: scale(4);
            transform: scale(4);
  }
}
@keyframes e-input-ripple {
  100% {
    opacity: 0;
    -webkit-transform: scale(4);
            transform: scale(4);
  }
}
@-webkit-keyframes slideTopUp {
  from {
    -webkit-transform: translate3d(0, 0, 0) scale(1);
            transform: translate3d(0, 0, 0) scale(1);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0) scale(1);
            transform: translate3d(0, 0, 0) scale(1);
  }
}
@keyframes slideTopUp {
  from {
    -webkit-transform: translate3d(0, 0, 0) scale(1);
            transform: translate3d(0, 0, 0) scale(1);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0) scale(1);
            transform: translate3d(0, 0, 0) scale(1);
  }
}
/* stylelint-disable */
.e-input:focus:not(.e-success):not(.e-warning).e-error,
.e-float-input:not(.e-success):not(.e-warning).e-error:not(.e-input-group) input:focus,
.e-float-input:not(.e-success):not(.e-warning).e-error:not(.e-input-group) textarea:focus,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning).e-error:not(.e-input-group) input:focus,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning).e-error:not(.e-input-group) textarea:focus,
.e-float-input:not(.e-success):not(.e-warning).e-error:not(.e-input-group).e-input-focus input,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-warning).e-error:not(.e-input-group).e-input-focus input {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input:focus:not(.e-error):not(.e-warning).e-success,
.e-float-input:not(.e-error):not(.e-warning).e-success:not(.e-input-group) input:focus,
.e-float-input:not(.e-error):not(.e-warning).e-success:not(.e-input-group) textarea:focus,
.e-float-input.e-control-wrapper:not(.e-error):not(.e-warning).e-success:not(.e-input-group) input:focus,
.e-float-input.e-control-wrapper:not(.e-error):not(.e-warning).e-success:not(.e-input-group) textarea:focus,
.e-float-input:not(.e-error):not(.e-warning).e-success:not(.e-input-group).e-input-focus input,
.e-float-input.e-control-wrapper:not(.e-error):not(.e-warning).e-success:not(.e-input-group).e-input-focus input {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input:focus:not(.e-success):not(.e-error).e-warning,
.e-float-input:not(.e-success):not(.e-error).e-warning:not(.e-input-group) input:focus,
.e-float-input:not(.e-success):not(.e-error).e-warning:not(.e-input-group) textarea:focus,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-error).e-warning:not(.e-input-group) input:focus,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-error).e-warning:not(.e-input-group) textarea:focus,
.e-float-input:not(.e-success):not(.e-error).e-warning:not(.e-input-group).e-input-focus input,
.e-float-input.e-control-wrapper:not(.e-success):not(.e-error).e-warning:not(.e-input-group).e-input-focus input {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning).e-error,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning).e-error {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input-group.e-input-focus:not(.e-error):not(.e-warning).e-success,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-error):not(.e-warning).e-success {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-error).e-warning,
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-error).e-warning {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input-group:not(.e-disabled):active:not(.e-success):not(.e-warning).e-error,
.e-input-group.e-control-wrapper:not(.e-disabled):active:not(.e-success):not(.e-warning).e-error {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input-group:not(.e-disabled):active:not(.e-error):not(.e-warning).e-success,
.e-input-group.e-control-wrapper:not(.e-disabled):active:not(.e-error):not(.e-warning).e-success {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-input-group:not(.e-disabled):active:not(.e-success):not(.e-error).e-warning,
.e-input-group.e-control-wrapper:not(.e-disabled):active:not(.e-success):not(.e-error).e-warning {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.e-float-input.e-error label.e-float-text,
.e-float-input.e-control-wrapper.e-error label.e-float-text,
.e-float-input.e-error input:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text,
.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text {
  color: var(--color-sf-danger) !important;
}

.e-outline.e-float-input.e-error.e-input-focus input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-input-group.e-error.e-valid-input label.e-label-top.e-float-text,
.e-outline.e-float-input.e-bigger.e-error.e-input-focus input:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-error.e-input-focus input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-small.e-error.e-input-focus input:focus ~ label.e-float-text,
.e-small .e-outline.e-float-input.e-error input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-small.e-bigger.e-error input:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-bigger.e-error textarea:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-small.e-error textarea:focus ~ label.e-float-text,
.e-small .e-outline.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-small.e-bigger.e-error textarea:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-small.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-bigger.e-error input:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-error input:focus ~ label.e-float-text,
.e-small .e-outline.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-bigger.e-error input:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-control-wrapper.e-small.e-error input:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-bigger.e-error textarea:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-error textarea:focus ~ label.e-float-text,
.e-small .e-outline.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-bigger.e-error textarea:focus ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-control-wrapper.e-small.e-error textarea:focus ~ label.e-float-text,
.e-outline.e-float-input.e-error label.e-float-text.e-label-top,
.e-outline.e-float-input.e-input-group.e-error label.e-float-text.e-label-top,
.e-outline.e-float-input.e-control-wrapper.e-error label.e-float-text.e-label-top,
.e-outline.e-float-input.e-valid-input.e-error:not(.e-input-focus) input:valid ~ label.e-float-text.e-label-top,
.e-outline.e-float-input.e-control-wrapper.e-valid-input.e-error:not(.e-input-focus) input:focus ~ label.e-float-text.e-label-top,
.e-outline.e-float-input.e-valid-input.e-error:not(.e-input-focus) textarea:valid ~ label.e-float-text.e-label-top,
.e-outline.e-float-input.e-control-wrapper.e-valid-input.e-error:not(.e-input-focus) textarea:focus ~ label.e-float-text.e-label-top,
.e-outline.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-bigger.e-error.e-input-focus input ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-small.e-error.e-input-focus input ~ label.e-float-text,
.e-small .e-outline.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-small.e-bigger.e-error.e-input-focus input ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-small:not(.e-error).e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-bigger.e-error.e-input-focus input ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-error.e-input-focus input ~ label.e-float-text,
.e-small .e-outline.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text,
.e-outline.e-float-input.e-control-wrapper.e-small.e-bigger.e-error.e-input-focus input ~ label.e-float-text,
.e-bigger .e-outline.e-float-input.e-control-wrapper.e-small.e-error.e-input-focus input ~ label.e-float-text {
  color: var(--color-sf-danger) !important;
}

.e-filled.e-float-input.e-error label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error label.e-float-text,
.e-filled.e-float-input.e-error input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text,
.e-filled.e-float-input.e-error textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text,
.e-filled.e-float-input.e-error.e-input-focus input ~ label.e-float-text,
.e-filled.e-float-input.e-control-wrapper.e-error.e-input-focus input ~ label.e-float-text {
  color: var(--color-sf-danger) !important;
}

/* stylelint-disable-line no-empty-source */
/* stylelint-disable-line no-empty-source */
.e-input-group.e-multi-line-input.e-auto-width {
  width: auto;
}

.e-input-group.e-multi-line-input textarea.e-resize-x {
  resize: horizontal;
}
.e-input-group.e-multi-line-input textarea.e-resize-y {
  resize: vertical;
}
.e-input-group.e-multi-line-input textarea.e-resize-xy {
  resize: both;
}
.e-input-group.e-multi-line-input textarea.e-textarea.e-resize-none {
  resize: none;
}

.e-float-input .e-clear-icon:hover,
.e-float-input.e-control-wrapper .e-clear-icon:hover,
.e-input-group .e-clear-icon:hover,
.e-input-group.e-control-wrapper .e-clear-icon:hover {
  background: none;
  border: none;
}

.e-float-input:not(.e-disabled) .e-clear-icon:hover,
.e-float-input.e-control-wrapper:not(.e-disabled) .e-clear-icon:hover,
.e-input-group:not(.e-disabled) .e-clear-icon:hover,
.e-input-group.e-control-wrapper:not(.e-disabled) .e-clear-icon:hover {
  background: none;
}