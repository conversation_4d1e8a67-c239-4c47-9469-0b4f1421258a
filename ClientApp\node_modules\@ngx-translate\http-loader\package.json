{"name": "@ngx-translate/http-loader", "version": "16.0.1", "description": "http loader for dynamically loading translation files for @ngx-translate/core", "keywords": ["@ngx-translate", "ngx-translate", "angular", "i18n", "translation"], "author": "<PERSON> / CodeAndWeb GmbH, Olivier <PERSON>", "license": "MIT", "peerDependencies": {"@angular/common": ">=16", "@angular/core": ">=16"}, "sideEffects": false, "module": "fesm2022/ngx-translate-http-loader.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/ngx-translate-http-loader.mjs"}}, "dependencies": {"tslib": "^2.3.0"}}