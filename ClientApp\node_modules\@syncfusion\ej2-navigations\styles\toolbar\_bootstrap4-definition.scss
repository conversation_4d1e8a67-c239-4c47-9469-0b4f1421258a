@use 'sass:color';

$border-type: solid !default;
$border-size: 1px !default;
$tbar-btn-border: 1px solid transparent !default;
$tbar-skin: 'bootstrap4' !default;
$tbar-zero-value: 0 !default;
$tbar-separator-bgr-minheight: 36px !default;
$tbar-btn-bgr-minwidth: 38px !default;
$tbar-btn-nrml-minwidth: 32px !default;
$tbar-btn-weight: 400 !default;
$tbar-border-radius: 0 !default;
$tbar-pop-btn-nrml-padding: $tbar-zero-value 15px 0 15px !default;
$tbar-pop-btn-bgr-padding: $tbar-zero-value 20px 0 20px !default;
$tbar-separator-nrml-minheight: 27px !default;
$tbar-separator-nrml-height: calc(100% - 15px) !default;
$tbar-tab-highlight-color:  rgba(0, 0, 0, 0) !default;
$tbar-border-nav-type: solid !default;
$tbar-btn-box-shadow: none !default;
$tbar-focus-btn-box-shadow: 0 0 0 3px rgba(130, 138, 145, .5);
$tbar-hover-font: $primary-font !default;
$tbar-press-font: $primary-font !default;
$tbar-active-font-color: $primary-font !default;
$tbar-default-font-overlay: $gray-700 !default;
$tbar-default-icon-overlay: $gray-700 !default;
$tbar-select-font: $tbar-hover-font !default;
$tbar-pressed-font: $tbar-hover-font !default;
$tbar-default-bg: $gray-100 !default;
$tbar-hor-active-bg: $gray-600 !default;
$tbar-pop-bg: $tbar-default-bg !default;
$tbar-items-default-bg: $tbar-default-bg !default;
$tbar-hover-bg: $gray-600 !default;
$tbar-popup-hover-bg: color.adjust($gray-600, $lightness: -7.5%) !default;
$tbar-pressed-bg: $gray-600 !default;
$tbar-btn-txt-font-size: 14px !default;
$tbar-hover-border-color: color.adjust($gray-600, $lightness: -10%) !default;
$tbar-focus-bg: $tbar-hover-bg !default;
$tbar-active-btn-bg: color.adjust($gray-600, $lightness: -10%) !default;
$tbar-focus-border-color: $gray-600 !default;
$tbar-press-bg: $tbar-hover-bg !default;
$tbar-btn-press-bg: $tbar-press-bg !default;
$tbar-press-border-color: $tbar-hover-border-color !default;
$tbar-active-border-color: color.adjust($gray-600, $lightness: -12.5%) !default;
$tbar-nrml-size: 38px !default;
$tbar-bgr-size: 54px !default;
$tbar-nrml-items-size: 38px !default;
$tbar-bgr-items-size: 54px !default;
$tbar-border-type: solid !default;
$tbar-default-border: $gray-300 !default;
$tbar-border-size: 1px !default;
$tbar-box-shadow: none !default;
$tbar-separator-size: 1px !default;
$tba-horizontal-separator: 0 $tbar-separator-size 0 0 !default;
$tba-vertical-separator: 0 0 $tbar-separator-size 0 !default;
$tbar-popup-border-width: 0 0 0 $border-size !default;
$tbar-popup-rtl-border-width: 0 $border-size 0 0 !default;
$tbar-popup-vertical-border-width: $border-size 0 0 0 !default;
$tbar-popup-vertical-rtl-border-width: 0 0 $border-size 0 !default;
$tbar-separator-border: $gray-400 !default;
$tbar-separator-border-type: solid !default;
$tbar-default-icon-color: $gray-700 !default;
$tbar-default-font: $gray-700 !default;
$tbar-nav-press-border: 3px solid $gray-500 !default;
$tbar-nav-hover-border: 0 !default;
$tbar-nav-focus-border: 0 !default;
$tbar-pressed-border: transparent !default;
$tbar-nav-pressed-box-shadow: none !default;
$tbar-pop-box-shadow: none !default;
$tbar-pop-radius: 4px !default;
$tbar-item-pop-bg-color: transparent !default;
$tbar-radius: 4px !default;
$tbar-border-nav-active-type: none !default;
$tbar-btn-pop-nrml-minheight: 26px !default;
$tbar-btn-pop-bgr-minheight: 25px !default;
$tbar-pop-icon-bgr-padding: 0 6px 0 4px !default;
$tbar-pop-icon-nrml-padding: 0 4px 0 5px !default;
$tbar-pop-btn-txt-nrml-pad: 0 5px 0 4px !default;
$tbar-pop-btn-txt-bgr-pad: 0 4px 0 6px !default;
$tbar-nav-nrml-width: 38px !default;
$tbar-nav-bgr-width: 38px !default;
$tbar-popup-padding: 5px 0 !default;
$tbar-btn-icn-nrml-padding: 0 4px 0 6px !default;
$tbar-btn-icn-bgr-padding: 0 4px 0 6px !default;
$tbar-rtl-btn-icn-nrml-padding: 0 4px 0 6px !default;
$tbar-rtl-btn-icn-bgr-padding: 0 4px 0 6px !default;
$tbar-btn-icn-right-bgr-padding: 0 12px 0 0 !default;
$tbar-btn-icn-right-nrml-padding: 0 12px 0 0 !default;
$tbar-rtl-btn-icn-right-nrml-padding: 0 0 0 12px !default;
$tbar-rtl-btn-icn-right-bgr-padding: 0 0 0 12px !default;
$btn-txt-nrml-padding: 0 6px 0 4px !default;
$btn-txt-bgr-padding: 0 6px 0 4px !default;
$btn-rtl-txt-nrml-padding: 0 6px 0 4px !default;
$btn-rtl-txt-bgr-padding: 0 6px 0 4px !default;
$tbar-item-pop-nrml-padding: 0 !default;
$tbar-item-pop-bgr-padding: 0 !default;
$tbar-item-nrml-mrgn: 12px !default;
$tbar-item-bgr-mrgn: 16px !default;
$tbar-btn-nrml-mrgn: 0 !default;
$tbar-btn-nrml-minheight: 32px !default;
$tbar-btn-nrml-line-height: 27px !default;
$tbar-btn-icon-nrml-line-height: 26px !default;
$tbar-btn-bgr-minheight: 38px !default;
$tbar-btn-bgr-line-height: 34px !default;
$tbar-btn-icon-bgr-line-height: 32px !default;
$tbar-btn-font-size: 14px !default;
$tbar-btn-border-radius: 4px !default;
$tbar-btn-pressed-box-shadow: inset  0 3px 5px rgba(0, 0, 0, .125) !default;
$tbar-btn-icon-bgr-width: 24px !default;
$tbar-btn-icon-nrml-width: 26px !default;
$tbar-btn-icon-nrml-height: 26px !default;
$tbar-right-item-line-height: 24px !default;
$tbar-nrml-item-size: 0 !default;
$tbar-item-height: auto !default;
$tbar-bgr-item-size: 0 !default;
$tbar-item-bgr-padding: 8px 2.5px !default;
$tbar-item-nrml-padding: 3px 4px !default;
$tbar-item-nrml-minwidth: 30px !default;
$tbar-separator-bgr-height: calc(100% - 12px) !default;
$tbar-separator-bgr-mrgn: 1px 4px !default;
$tbar-separator-nrml-mrgn: 5.5px 6px !default;
$tbar-separator-vertical-bgr-mrgn: 6px !default;
$tbar-separator-vertical-nrml-mrgn: 6px 5.5px !default;
$tbar-btn-bgr-padding: 1.5px 5.5px !default;
$tbar-btn-bgr-focus-padding: 1.5px 5.5px !default;
$tbar-btn-nrml-padding: 0 3px !default;
$tbar-icons-bgr-font-size: 16px !default;
$tbar-multirow-items-mrgn-bigger: 15px !default;
$tbar-multirow-items-mrgn-small: 15px !default;
$tbar-multirow-item-top-btm-mrgn-bigger: 0 !default;
$tbar-multirow-item-top-btm-mrgn-small: 0 !default;
$tbar-bgr-btn-text-font-size: 16px !default;
$tbar-bgr-btn-icon-font-size: 16px !default;
$tbar-bgr-btn-focus-padding: 0 5.5px !default;
$tbar-nrml-btn-border-radius: 4px !default;
$tbar-nrml-btn-focus-padding: 0 3px !default;
$tbar-nrml-btn-focus-outline: 0 !default;
$tbar-btn-icons-focus-color: inherit !default;
$tbar-btn-text-focus-color: inherit !default;
$tbar-btn-focus-border-color: $tbar-focus-border-color !default;
$tbar-btn-hover-border-size: $tbar-border-size !default;
$tbar-btn-hover-active-icons-color: inherit !default;
$tbar-btn-hover-active-text-color: inherit !default;
$tbar-btn-overlay-opacity: .65 !default;
$tbar-btn-active-bg: $tbar-active-btn-bg !default;
$tbar-btn-active-icons-color: inherit !default;
$tbar-btn-active-text-color: inherit !default;
$tbar-btn-text-color: $gray-700 !default;
$tbar-btn-pressed-text-color: $white !default;
$tbar-btn-pressed-focus-box-shadow: 0 0 0 3px rgba(130, 138, 145, .5) !default;
$tbar-btn-pressed-bg: color.adjust($gray-600, $lightness: -10%) !default;
$tbar-flat-btn-active-box-shadow: 0 0 0 .25em rgba(color.mix(color.adjust($tbar-hover-border-color, $lightness: 50%), $tbar-active-border-color, 15%), .5) !default;
$tbar-ext-btn-focus-padding: 0 3px !default;
$tbar-ext-btn-icon-padding: 0 !default;
$tbar-ext-btn-icon-font-size: 14px !default;
$tbar-ext-btn-focus-box-shadow: $tbar-focus-btn-box-shadow !default;
$tbar-ext-btn-hover-border-color: $tbar-hover-border-color !default;
$tbar-ext-btn-border: 1px solid transparent !default;
$tbar-popup-icon-font-size: 12px !default;
$tbar-popup-text-btn-icon-padding: 0 !default;
$tbar-popup-bgr-text-btn-icon-padding: 0 !default;
$tbar-popup-btn-border: 0 !default;
$tbar-popup-btn-border-radius: 0 !default;
$tbar-popup-bgr-height: auto !default;
$tbar-popup-bgr-btn-icon-font-size: 16px !default;
$tbar-popup-bgr-btn-text-font-size: 16px !default;
$tbar-popup-nav-active-border-bottom-right-radius: 0 !default;
$tbar-popup-nav-active-bg: $tbar-focus-border-color !default;
$tbar-popup-nav-active-icons-color: inherit !default;
$tbar-popup-nav-hover-bg: $tbar-popup-hover-bg !default;
$tbar-popup-nav-hover-color: $tbar-hover-font !default;
$tbar-popup-nav-hover-icons-color: inherit !default;
$tbar-popup-nav-hover-border-color: $tbar-default-border !default;
$tbar-popup-nav-hover-border-size: 0 !default;
$tbar-popup-nav-hover-active-bg: $tbar-popup-hover-bg !default;
$tbar-popup-nav-hover-active-border-color: $gray-500 !default;
$tbar-popup-nav-hover-active-border-size:  3px 3px 3px 0 !default;
$tbar-popup-nav-focus-bg: $tbar-focus-border-color !default;
$tbar-popup-nav-focus-color: $tbar-hover-font !default;
$tbar-popup-nav-focus-border-color: transparent !default;
$tbar-popup-nav-focus-border-size: 0 !default;
$tbar-popup-btn-bg: $content-bg !default;
$tbar-popup-btn-hover-bg: $primary !default;
$tbar-popup-btn-hover-box-shadow: none !default;
$tbar-popup-btn-active-bg: $primary !default;
$tbar-popup-btn-active-box-shadow: none !default;
$tbar-popup-btn-focus-bg: $primary !default;
$tbar-popup-btn-focus-box-shadow: none !default;
$tbar-popup-nav-pressed-icons-active-color: inherit !default;
$tbar-popup-btn-focus-outline: 0 !default;
$tbar-popup-nav-pressed-border-color: $gray-500 !default;
$tbar-popup-nav-pressed-border-size: 3px !default;
$tbar-popup-nav-pressed-focus-border-color: $gray-500 !default;
$tbar-popup-nav-pressed-focus-border-size: 3px !default;
$tbar-popup-btn-hover-border-size: $tbar-zero-value !default;
@mixin tbar-btn-animation {
  content: '';
}
@mixin tbar-btn-animation-after {
  content: '';
}
