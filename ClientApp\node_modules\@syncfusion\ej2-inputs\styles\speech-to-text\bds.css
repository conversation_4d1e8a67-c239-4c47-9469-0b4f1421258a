



/* stylelint-disable */
.e-btn.e-primary:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
}

.e-btn.e-success:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(23, 177, 105, 0.24) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(23, 177, 105, 0.24) !important;
}

.e-btn.e-info:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow) !important;
}

.e-btn.e-warning:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(247, 143, 8, 0.24) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(247, 143, 8, 0.24) !important;
}

.e-btn.e-danger:focus {
  -webkit-box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(240, 68, 55, 0.24) !important;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px rgba(240, 68, 55, 0.24) !important;
}

.e-btn.e-outline.e-info {
  background: transparent !important;
  border-color: var(--color-sf-utility-info-border-color) !important;
  color: var(--color-sf-utility-info-text) !important;
}

.e-btn.e-outline.e-info:hover {
  background: var(--color-sf-utility-info-bg-color-hover) !important;
  border-color: var(--color-sf-utility-info-border-color) !important;
  color: var(--color-sf-utility-info-text-hover) !important;
}

.e-btn.e-outline.e-info:active {
  background: var(--color-sf-utility-info-bg-color-pressed) !important;
  border-color: var(--color-sf-utility-info-border-color) !important;
  color: var(--color-sf-utility-info-text-hover) !important;
}

.e-btn.e-flat.e-info {
  color: var(--color-sf-utility-info-text) !important;
}

.e-bigger .e-btn,
.e-bigger .e-btn,
.e-bigger .e-css.e-btn,
.e-bigger.e-css.e-btn {
  font-size: 14px;
  line-height: 1.572em;
  padding: 8px 15px;
  border-radius: 8px !important;
  font-weight: 600;
}

.e-listen-icon::before {
  content: "\e91c";
}

.e-listen-stop::before {
  content: "\e919";
}

.e-speech-to-text.e-btn {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 9999px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  min-width: 40px;
  min-height: 40px;
  padding: 0 13px;
}
.e-speech-to-text.e-btn.e-round {
  width: 40px;
  height: 40px;
  padding: 0;
}
.e-speech-to-text.e-btn .e-btn-icon {
  font-size: 16px;
}
.e-speech-to-text.e-btn:disabled {
  pointer-events: none;
}
.e-speech-to-text.e-btn.e-listening-state {
  -webkit-animation: listening 1.2s infinite;
          animation: listening 1.2s infinite;
}

.e-speech-to-text.e-round .e-btn-icon {
  font-size: 16px;
}

@-webkit-keyframes listening {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes listening {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.e-speech-to-text {
  -webkit-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 -2px 15px -3px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.14);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 -2px 15px -3px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.14);
}
.e-speech-to-text:hover:not(:focus), .e-speech-to-text:active, .e-speech-to-text.e-active, .e-speech-to-text:disabled {
  -webkit-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 -2px 15px -3px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.14);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 -2px 15px -3px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.14);
}
.e-speech-to-text:focus {
  -webkit-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 -2px 15px -3px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.14), 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 -2px 15px -3px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.14), 0 1px 2px 0 rgba(16, 24, 40, 0.05), 0 0 0 4px var(--color-sf-brand-solid-shadow);
}
.e-speech-to-text.e-flat {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.e-bigger .e-speech-to-text.e-btn,
.e-bigger.e-speech-to-text.e-btn {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 9999px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  min-width: 52px;
  min-height: 52px;
  padding: 0 18px;
}
.e-bigger .e-speech-to-text.e-btn.e-round,
.e-bigger.e-speech-to-text.e-btn.e-round {
  width: 40px;
  height: 40px;
  padding: 0;
}
.e-bigger .e-speech-to-text.e-btn .e-btn-icon,
.e-bigger.e-speech-to-text.e-btn .e-btn-icon {
  font-size: 16px;
}

.e-bigger .e-speech-to-text.e-round .e-btn-icon,
.e-bigger.e-speech-to-text.e-round .e-btn-icon {
  font-size: 20px;
}