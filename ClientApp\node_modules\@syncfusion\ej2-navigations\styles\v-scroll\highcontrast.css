/*! component's theme wise override definitions and variables */
/*! vscroll icons */
.e-vscroll.e-scroll-device .e-nav-up-arrow::before {
  content: "\e85e";
}
.e-vscroll.e-scroll-device .e-nav-down-arrow::before {
  content: "\e84f";
}
.e-vscroll .e-nav-up-arrow::before {
  content: "\e85e";
  line-height: normal;
}
.e-vscroll .e-nav-down-arrow::before {
  content: "\e84f";
  line-height: normal;
}

/*! v-scroll layout */
.e-vscroll {
  display: block;
  position: relative;
  width: inherit;
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav {
  -webkit-transform: skewX(-16deg) translateX(-6px);
          transform: skewX(-16deg) translateX(-6px);
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: 52px;
  right: auto;
  -webkit-transform: skewX(-16deg) translateX(-6px);
          transform: skewX(-16deg) translateX(-6px);
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: auto;
  right: 0;
}
.e-vscroll:not(.e-scroll-device) {
  padding: 0 40px;
}
.e-vscroll.e-scroll-device .e-scroll-nav {
  -webkit-transform: skewX(-16deg) translateX(6px);
          transform: skewX(-16deg) translateX(6px);
  width: 52px;
  z-index: 1001;
}
.e-vscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  -webkit-transform: skewX(16deg);
          transform: skewX(16deg);
}
.e-vscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}
.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: auto;
  right: 52px;
  -webkit-transform: skewX(-16deg) translateX(6px);
          transform: skewX(-16deg) translateX(6px);
}
.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: 0;
  right: auto;
}
.e-vscroll > * {
  height: inherit;
}
.e-vscroll .e-vscroll-content {
  display: inline-block;
  height: auto;
  position: relative;
  width: 100%;
}
.e-vscroll .e-vscroll-content > * {
  pointer-events: auto;
}
.e-vscroll.e-rtl .e-scroll-nav.e-scroll-up-nav {
  left: auto;
  right: 0;
}
.e-vscroll.e-rtl .e-scroll-nav.e-scroll-down-nav {
  left: 0;
  right: auto;
}
.e-vscroll .e-scroll-nav {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  overflow: hidden;
  position: absolute;
  width: 100%;
}
.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  top: 0;
}
.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  bottom: 0;
}
.e-vscroll .e-scroll-nav.e-ie-align {
  display: table;
}
.e-vscroll .e-nav-arrow {
  position: relative;
}
.e-vscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

/*! v-scroll theme */
.e-vscroll .e-icons {
  color: #ffd939;
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: #fff;
  -webkit-box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
          box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}
.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #262626;
  border-color: #fff;
  border-width: 1px;
  -webkit-box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
          box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
}
.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #ffd939;
}
.e-vscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}
.e-vscroll .e-scroll-overlay.e-scroll-up-overlay {
  background-image: -webkit-gradient(linear, left top, right top, from(#1a1a1a), to(rgba(26, 26, 26, 0)));
  background-image: linear-gradient(-270deg, #1a1a1a 0%, rgba(26, 26, 26, 0) 100%);
}
.e-vscroll .e-scroll-overlay.e-scroll-down-overlay {
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(26, 26, 26, 0)), to(#1a1a1a));
  background-image: linear-gradient(-270deg, rgba(26, 26, 26, 0) 0%, #1a1a1a 100%);
}
.e-vscroll.e-rtl .e-scroll-nav {
  background: #000;
}
.e-vscroll.e-rtl .e-scroll-nav:hover {
  background: #685708;
  border: 0;
  border-color: transparent;
  color: #fff;
}
.e-vscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: #685708;
  border: 0;
  color: #fff;
}
.e-vscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: #685708;
  border: 0;
  border-color: transparent;
  color: #fff;
}
.e-vscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #ffd939;
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
}
.e-vscroll .e-scroll-nav {
  background: #000;
}
.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  border-bottom: 1px solid #fff;
}
.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  border-top: 1px solid #fff;
}
.e-vscroll .e-scroll-nav::after {
  content: "";
}
.e-vscroll .e-scroll-nav:active::after {
  content: "";
}

.e-bigger .e-vscroll:not(.e-scroll-device),
.e-vscroll.e-bigger:not(.e-scroll-device) {
  padding: 50px 0;
}
.e-bigger .e-vscroll .e-icons,
.e-vscroll.e-bigger .e-icons {
  font-size: 18px;
}
.e-bigger .e-vscroll.e-rtl .e-scroll-overlay.e-scroll-down-overlay,
.e-vscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-down-overlay {
  left: 50px;
}
.e-bigger .e-vscroll .e-scroll-overlay.e-scroll-down-overlay,
.e-vscroll.e-bigger .e-scroll-overlay.e-scroll-down-overlay {
  right: 50px;
}
.e-bigger .e-vscroll .e-scroll-nav,
.e-vscroll.e-bigger .e-scroll-nav {
  height: 50px;
}