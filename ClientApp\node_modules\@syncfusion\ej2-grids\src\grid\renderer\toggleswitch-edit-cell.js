var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
import { isNullOrUndefined } from '@syncfusion/ej2-base';
import { Switch } from '@syncfusion/ej2-buttons';
import { extend } from '@syncfusion/ej2-base';
import { isEditable, addRemoveActiveClasses, createEditElement, getObject } from '../base/util';
import * as literals from '../base/string-literals';
import { EditCellBase } from './edit-cell-base';
/**
 * `ToggleEditCell` is used to handle boolean cell type editing.
 *
 * @hidden
 */
var ToggleEditCell = /** @class */ (function (_super) {
    __extends(ToggleEditCell, _super);
    function ToggleEditCell() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.activeClasses = ['e-selectionbackground', 'e-active'];
        return _this;
    }
    ToggleEditCell.prototype.create = function (args) {
        var clsNames = 'e-field e-boolcell';
        if (args.column.type === 'checkbox') {
            clsNames = 'e-field e-boolcell e-edit-checkselect';
        }
        return createEditElement(this.parent, args.column, clsNames, { type: 'checkbox', value: args.value });
    };
    ToggleEditCell.prototype.read = function (element) {
        return element.checked;
    };
    ToggleEditCell.prototype.write = function (args) {
        var chkBoxElement = !isNullOrUndefined(args.row) && args.row.querySelector('.e-edit-checkselect');
        var data = getObject(args.column.field, args.rowData);
        var checkState = data && JSON.parse(data.toString().toLowerCase());
        if (!isNullOrUndefined(chkBoxElement)) {
            this.editType = this.parent.editSettings.mode;
            this.editRow = args.row;
            if (args.requestType !== 'add') {
                var row = this.parent.getRowObjectFromUID(args.row.getAttribute('data-uid'));
                checkState = row ? row.isSelected : false;
            }
            addRemoveActiveClasses.apply(void 0, [[].slice.call(args.row.getElementsByClassName(literals.rowCell)), checkState].concat(this.activeClasses));
        }
        this.obj = new Switch(extend({
            label: this.parent.editSettings.mode !== 'Dialog' ? ' ' : args.column.headerText,
            checked: checkState,
            disabled: !isEditable(args.column, args.requestType, args.element), enableRtl: this.parent.enableRtl,
            change: this.switchModeChange.bind(this),
            cssClass: this.parent.cssClass ? this.parent.cssClass : ''
        }, args.column.edit.params));
        this.obj.appendTo(args.element);
    };
    ToggleEditCell.prototype.switchModeChange = function (args) {
        if (this.editRow && this.editType !== 'Dialog') {
            var addClass = false;
            if (!args.checked) {
                this.editRow.removeAttribute('aria-selected');
            }
            else {
                addClass = true;
                this.editRow.setAttribute('aria-selected', addClass.toString());
            }
            addRemoveActiveClasses.apply(void 0, [[].slice.call(this.editRow.getElementsByClassName(literals.rowCell)), addClass].concat(this.activeClasses));
        }
    };
    return ToggleEditCell;
}(EditCellBase));
export { ToggleEditCell };
