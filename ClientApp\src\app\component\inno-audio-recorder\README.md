# Audio Recorder Component

## Overview

The `InnoAudioRecorderComponent` is a comprehensive audio recording component that provides a complete recording interface with real-time audio visualization, playback controls, and seamless integration with the CANEVA system.

## Features

- **Real-time Audio Recording**: Uses the Web Audio API and MediaRecorder for high-quality audio capture
- **Visual Feedback**: Real-time audio waveform visualization during recording
- **Recording Controls**: Start, pause, resume, and stop recording functionality
- **Automatic Upload**: Recordings are automatically uploaded when stopped (no manual save required)
- **Playback Preview**: Built-in audio player to preview recordings after upload
- **Permission Handling**: Graceful handling of microphone permissions with user-friendly error messages
- **Multiple Audio Formats**: Supports WAV, MP3, and WebM formats with automatic conversion
- **Format Selection**: User can choose preferred output format before recording
- **Upload Progress**: Visual feedback during file upload with loading indicators
- **Re-recording**: Option to record again if not satisfied with the result
- **Responsive Design**: Works on both desktop and mobile devices
- **Accessibility**: Keyboard navigation and screen reader support

## Usage

### Basic Usage

```typescript
import { AudioRecorderDialog } from 'app/service/dialog/audio-recorder.dialog';

constructor(private audioRecorderDialog: AudioRecorderDialog) {}

openRecorder() {
  this.audioRecorderDialog.open({
    canevasId: 'your-canvas-id',
    preferredFormat: 'wav' // 'wav', 'mp3', or 'webm'
  }).then((dialogRef) => {
    dialogRef.afterClosed().subscribe((result) => {
      if (result === true) {
        // Upload was successful, refresh your data
        console.log('Audio uploaded successfully');
        this.loadData(); // Refresh your component data
      } else if (result instanceof File) {
        // Fallback: manual file handling (rare case)
        console.log('Manual file save:', result);
      }
    });
  });
}
```

### Integration with CANEVA

The component is already integrated into the CANEVA system. When users click the microphone button in a CANEVA item, it opens the audio recorder dialog.

```typescript
handleAudioRecording(canevasId: string) {
  this.audioRecorderDialog.open({ canevasId: canevasId }).then((dialogRef: any) => {
    dialogRef.afterClosed().subscribe((recordedAudioFile: File) => {
      if (!recordedAudioFile) return;

      const payload: CreateDocumentDTO = {
        name: recordedAudioFile.name,
        document: recordedAudioFile,
        fileName: recordedAudioFile.name,
        type: DocumentType.Audio,
        canevasId: canevasId
      };

      // Upload the audio file
      this.documentService.addDocument(payload).subscribe({
        next: (res) => {
          this.toastService.showSuccess("Enregistrement audio ajouté avec succès");
        },
        error: (error) => {
          this.toastService.showError("Erreur lors de l'ajout de l'enregistrement audio");
        }
      });
    });
  });
}
```

## Component Interface

### Input Data

```typescript
interface IAudioRecorderDialog {
  canevasId?: string;
  projectId?: string;
  moduleId?: string;
}
```

### Output

The component returns a `File` object containing the recorded audio when the user saves the recording.

## Browser Compatibility

- **Chrome**: Full support (recommended)
- **Firefox**: Full support
- **Safari**: Full support (iOS 14.3+)
- **Edge**: Full support

## Permissions

The component requires microphone access. It handles permission requests gracefully and provides clear instructions to users if permissions are denied.

## Audio Formats

The component supports multiple output formats with automatic conversion:

### Supported Formats

1. **WAV (Recommended)**
   - Uncompressed, high-quality audio
   - Best compatibility across all platforms
   - Larger file size but perfect quality
   - File extension: `.wav`

2. **MP3**
   - Compressed audio format
   - Smaller file size
   - Good compatibility
   - File extension: `.mp3`
   - Note: Currently converts to WAV with MP3 mime type. For true MP3 encoding, consider adding the `lamejs` library.

3. **WebM (Native)**
   - Browser's native recording format
   - Good compression and quality
   - Modern browser support
   - File extension: `.webm`

### Format Selection

Users can select their preferred format before recording using the dropdown selector. The format can also be pre-selected programmatically:

```typescript
// Pre-select WAV format
this.audioRecorderDialog.open({
  canevasId: 'canvas-id',
  preferredFormat: 'wav'
});
```

## Technical Details

### Audio Processing

- **Sample Rate**: Uses the device's default sample rate
- **Channels**: Mono recording for optimal file size
- **Bit Rate**: Automatic based on browser capabilities
- **Echo Cancellation**: Enabled for better audio quality
- **Noise Suppression**: Enabled to reduce background noise

### Visualization

The component uses the Web Audio API's AnalyserNode to create real-time frequency domain visualization of the audio input.

### File Naming

Recorded files are automatically named with a timestamp: `recording-YYYY-MM-DDTHH-mm-ss-sssZ.webm`

## Styling

The component uses Tailwind CSS classes and custom SCSS for styling. It supports both light and dark themes and is fully responsive.

## Error Handling

- **Microphone Access Denied**: Shows user-friendly error message with instructions
- **Browser Not Supported**: Graceful fallback with error notification
- **Recording Errors**: Automatic error recovery and user notification

## Future Enhancements

- **Audio Effects**: Real-time audio effects during recording
- **Multiple Format Export**: Allow users to choose output format
- **Cloud Storage**: Direct upload to cloud storage services
- **Transcription**: Automatic speech-to-text conversion
- **Audio Editing**: Basic audio editing capabilities (trim, fade, etc.)
