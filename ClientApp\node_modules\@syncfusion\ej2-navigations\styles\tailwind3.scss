@import 'ej2-base/styles/definition/tailwind3.scss';
@import 'pager/tailwind3-definition.scss';
@import 'pager/icons/tailwind3.scss';
@import 'pager/all.scss';
@import 'pager/bigger.scss';
@import 'h-scroll/tailwind3-definition.scss';
@import 'h-scroll/icons/tailwind3.scss';
@import 'h-scroll/all.scss';
@import 'h-scroll/bigger.scss';
@import 'v-scroll/tailwind3-definition.scss';
@import 'v-scroll/icons/tailwind3.scss';
@import 'v-scroll/all.scss';
@import 'v-scroll/bigger.scss';
@import 'ej2-buttons/styles/button/tailwind3-definition.scss';
@import 'ej2-popups/styles/popup/tailwind3-definition.scss';
@import 'toolbar/tailwind3-definition.scss';
@import 'toolbar/icons/tailwind3.scss';
@import 'toolbar/all.scss';
@import 'toolbar/bigger.scss';
@import 'accordion/tailwind3-definition.scss';
@import 'accordion/icons/tailwind3.scss';
@import 'accordion/all.scss';
@import 'accordion/bigger.scss';
@import 'carousel/tailwind3-definition.scss';
@import 'carousel/icons/tailwind3.scss';
@import 'carousel/all.scss';

@import 'context-menu/tailwind3-definition.scss';
@import 'context-menu/icons/tailwind3.scss';
@import 'context-menu/all.scss';
@import 'context-menu/bigger.scss';
@import 'tab/tailwind3-definition.scss';
@import 'tab/icons/tailwind3.scss';
@import 'tab/all.scss';
@import 'tab/bigger.scss';
@import 'ej2-inputs/styles/input/tailwind3-definition.scss';
@import 'ej2-buttons/styles/check-box/tailwind3-definition.scss';
@import 'treeview/tailwind3-definition.scss';
@import 'treeview/icons/tailwind3.scss';
@import 'treeview/all.scss';
@import 'treeview/bigger.scss';
@import 'sidebar/tailwind3-definition.scss';
@import 'sidebar/all.scss';
@import 'menu/tailwind3-definition.scss';
@import 'menu/icons/tailwind3.scss';
@import 'menu/all.scss';
@import 'menu/bigger.scss';
@import 'breadcrumb/tailwind3-definition.scss';
@import 'breadcrumb/icons/tailwind3.scss';
@import 'breadcrumb/all.scss';
@import 'breadcrumb/bigger.scss';
@import 'appbar/tailwind3-definition.scss';
@import 'appbar/all.scss';
@import 'appbar/bigger.scss';
@import 'ej2-popups/styles/tooltip/tailwind3-definition.scss';
@import 'stepper/tailwind3-definition.scss';
@import 'stepper/icons/tailwind3.scss';
@import 'stepper/all.scss';
@import 'stepper/bigger.scss';