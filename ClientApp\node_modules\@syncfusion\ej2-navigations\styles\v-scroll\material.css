/*! component's theme wise override definitions and variables */
@-webkit-keyframes vscroll-popup-shadow {
  0% {
    border-color: rgba(255, 255, 255, 0.5);
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
  }
  100% {
    -webkit-box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
  }
}
@keyframes vscroll-popup-shadow {
  0% {
    border-color: rgba(255, 255, 255, 0.5);
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
  }
  100% {
    -webkit-box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
  }
}
/*! vscroll icons */
.e-vscroll.e-scroll-device .e-nav-up-arrow::before {
  content: "\e85e";
}
.e-vscroll.e-scroll-device .e-nav-down-arrow::before {
  content: "\e84f";
}
.e-vscroll .e-nav-up-arrow::before {
  content: "\e910";
  line-height: normal;
}
.e-vscroll .e-nav-down-arrow::before {
  content: "\e916";
  line-height: normal;
}

/*! v-scroll layout */
.e-vscroll {
  display: block;
  position: relative;
  width: inherit;
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav {
  -webkit-transform: skewX(-16deg) translateX(-6px);
          transform: skewX(-16deg) translateX(-6px);
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: 56px;
  right: auto;
  -webkit-transform: skewX(-16deg) translateX(-6px);
          transform: skewX(-16deg) translateX(-6px);
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: auto;
  right: 0;
}
.e-vscroll:not(.e-scroll-device) {
  padding: 0 16px;
}
.e-vscroll.e-scroll-device .e-scroll-nav {
  -webkit-transform: skewX(-16deg) translateX(6px);
          transform: skewX(-16deg) translateX(6px);
  width: 56px;
  z-index: 1001;
}
.e-vscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  -webkit-transform: skewX(16deg);
          transform: skewX(16deg);
}
.e-vscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}
.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: auto;
  right: 56px;
  -webkit-transform: skewX(-16deg) translateX(6px);
          transform: skewX(-16deg) translateX(6px);
}
.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: 0;
  right: auto;
}
.e-vscroll > * {
  height: inherit;
}
.e-vscroll .e-vscroll-content {
  display: inline-block;
  height: auto;
  position: relative;
  width: 100%;
}
.e-vscroll .e-vscroll-content > * {
  pointer-events: auto;
}
.e-vscroll.e-rtl .e-scroll-nav.e-scroll-up-nav {
  left: auto;
  right: 0;
}
.e-vscroll.e-rtl .e-scroll-nav.e-scroll-down-nav {
  left: 0;
  right: auto;
}
.e-vscroll .e-scroll-nav {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  overflow: hidden;
  position: absolute;
  width: 100%;
}
.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  top: 0;
}
.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  bottom: 0;
}
.e-vscroll .e-scroll-nav.e-ie-align {
  display: table;
}
.e-vscroll .e-nav-arrow {
  position: relative;
}
.e-vscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

/*! v-scroll theme */
.e-vscroll .e-icons {
  color: rgba(0, 0, 0, 0.54);
}
.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
          box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}
.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #fafafa;
  border-color: rgba(0, 0, 0, 0.12);
  border-width: 1px;
  -webkit-box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
          box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
}
.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #e3165b;
}
.e-vscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}
.e-vscroll .e-scroll-overlay.e-scroll-up-overlay {
  background-image: -webkit-gradient(linear, left top, right top, from(#fafafa), to(rgba(250, 250, 250, 0)));
  background-image: linear-gradient(-270deg, #fafafa 0%, rgba(250, 250, 250, 0) 100%);
}
.e-vscroll .e-scroll-overlay.e-scroll-down-overlay {
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(250, 250, 250, 0)), to(#fafafa));
  background-image: linear-gradient(-270deg, rgba(250, 250, 250, 0) 0%, #fafafa 100%);
}
.e-vscroll.e-rtl .e-scroll-nav {
  background: #fafafa;
}
.e-vscroll.e-rtl .e-scroll-nav:hover {
  background: rgba(0, 0, 0, 0.12);
  border: 0;
  border-color: rgba(0, 0, 0, 0.12);
  color: #000;
}
.e-vscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: rgba(0, 0, 0, 0.12);
  border: 0;
  color: #000;
}
.e-vscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: rgba(0, 0, 0, 0.12);
  border: 0;
  border-color: rgba(0, 0, 0, 0.12);
  color: #000;
}
.e-vscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #bdbdbd;
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #000;
}
.e-vscroll .e-scroll-nav {
  background: #fafafa;
}
.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
.e-vscroll .e-scroll-nav::after {
  background-color: transparent;
  border-radius: 50%;
  border-width: 1px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  content: "";
  height: 1px;
  left: 50%;
  position: absolute;
  top: 50%;
  visibility: hidden;
  width: 1px;
}
.e-vscroll .e-scroll-nav:active::after {
  -webkit-animation: vscroll-popup-shadow 0.6s ease-out 0ms;
          animation: vscroll-popup-shadow 0.6s ease-out 0ms;
  visibility: visible;
}

.e-bigger .e-vscroll:not(.e-scroll-device),
.e-vscroll.e-bigger:not(.e-scroll-device) {
  padding: 24px 0;
}
.e-bigger .e-vscroll .e-icons,
.e-vscroll.e-bigger .e-icons {
  font-size: 18px;
}
.e-bigger .e-vscroll.e-rtl .e-scroll-overlay.e-scroll-down-overlay,
.e-vscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-down-overlay {
  left: 24px;
}
.e-bigger .e-vscroll .e-scroll-overlay.e-scroll-down-overlay,
.e-vscroll.e-bigger .e-scroll-overlay.e-scroll-down-overlay {
  right: 24px;
}
.e-bigger .e-vscroll .e-scroll-nav,
.e-vscroll.e-bigger .e-scroll-nav {
  height: 24px;
}