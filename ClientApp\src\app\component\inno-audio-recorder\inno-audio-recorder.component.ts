import { Component, ElementRef, ViewChild, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoModalWrapperComponent } from '../inno-modal-wrapper/inno-modal-wrapper.component';
import { ToastService } from 'app/service/toast.service';

export interface IAudioRecorderDialog {
  canevasId?: string;
  projectId?: string;
  moduleId?: string;
}

@Component({
  selector: 'app-inno-audio-recorder',
  templateUrl: './inno-audio-recorder.component.html',
  styleUrls: ['./inno-audio-recorder.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    InnoModalWrapperComponent
  ]
})
export class InnoAudioRecorderComponent implements OnDestroy {
  @ViewChild('audioPlayer') audioPlayerRef!: ElementRef<HTMLAudioElement>;
  @ViewChild('visualizer') visualizerRef!: ElementRef<HTMLCanvasElement>;

  public isRecording = false;
  public isPaused = false;
  public isPlaying = false;
  public hasRecording = false;
  public recordingTime = 0;
  public playbackTime = 0;
  public duration = 0;
  public isMicrophoneAllowed = true;
  public isInitializing = true;

  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recordedBlob: Blob | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private animationId: number | null = null;
  private recordingTimer: any = null;
  private playbackTimer: any = null;
  private stream: MediaStream | null = null;

  constructor(
    private dialogRef: MatDialogRef<InnoAudioRecorderComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: IAudioRecorderDialog,
    private toastService: ToastService
  ) {
    this.initializeAudio();
  }

  static getComponent(): typeof InnoAudioRecorderComponent {
    return InnoAudioRecorderComponent;
  }

  async initializeAudio(): Promise<void> {
    try {
      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported');
      }

      // Request microphone permission
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });

      // Initialize audio context for visualization
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Handle audio context state
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;

      this.microphone = this.audioContext.createMediaStreamSource(this.stream);
      this.microphone.connect(this.analyser);

      // Check MediaRecorder support
      if (!window.MediaRecorder) {
        throw new Error('MediaRecorder not supported');
      }

      // Initialize MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: this.getSupportedMimeType()
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.recordedBlob = new Blob(this.audioChunks, {
          type: this.mediaRecorder?.mimeType || 'audio/webm'
        });
        this.hasRecording = true;
        this.setupPlayback();
      };

      this.mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        this.toastService.showError('Erreur lors de l\'enregistrement');
      };

      this.isMicrophoneAllowed = true;
      this.isInitializing = false;
      this.startVisualization();

    } catch (error: any) {
      console.error('Error accessing microphone:', error);
      this.isMicrophoneAllowed = false;
      this.isInitializing = false;

      let errorMessage = 'Impossible d\'accéder au microphone';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Accès au microphone refusé. Veuillez autoriser l\'accès dans les paramètres du navigateur.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';
      } else if (error.name === 'NotSupportedError') {
        errorMessage = 'Enregistrement audio non supporté par ce navigateur.';
      }

      this.toastService.showError(errorMessage);
    }
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // fallback
  }

  startRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'inactive') {
      return;
    }

    this.audioChunks = [];
    this.recordingTime = 0;
    this.isRecording = true;
    this.isPaused = false;
    this.hasRecording = false;

    this.mediaRecorder.start(100); // Collect data every 100ms

    this.recordingTimer = setInterval(() => {
      this.recordingTime += 0.1;
    }, 100);
  }

  pauseRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'recording') {
      return;
    }

    this.mediaRecorder.pause();
    this.isPaused = true;

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
  }

  resumeRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'paused') {
      return;
    }

    this.mediaRecorder.resume();
    this.isPaused = false;

    this.recordingTimer = setInterval(() => {
      this.recordingTime += 0.1;
    }, 100);
  }

  stopRecording(): void {
    if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
      return;
    }

    this.mediaRecorder.stop();
    this.isRecording = false;
    this.isPaused = false;

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
  }

  private setupPlayback(): void {
    if (!this.recordedBlob) return;

    const audioUrl = URL.createObjectURL(this.recordedBlob);
    const audioElement = this.audioPlayerRef?.nativeElement;

    if (audioElement) {
      audioElement.src = audioUrl;
      audioElement.addEventListener('loadedmetadata', () => {
        this.duration = audioElement.duration;
      });

      audioElement.addEventListener('timeupdate', () => {
        this.playbackTime = audioElement.currentTime;
      });

      audioElement.addEventListener('ended', () => {
        this.isPlaying = false;
        this.playbackTime = 0;
        if (this.playbackTimer) {
          clearInterval(this.playbackTimer);
        }
      });
    }
  }

  togglePlayback(): void {
    const audioElement = this.audioPlayerRef?.nativeElement;
    if (!audioElement || !this.hasRecording) return;

    if (this.isPlaying) {
      audioElement.pause();
      this.isPlaying = false;
      if (this.playbackTimer) {
        clearInterval(this.playbackTimer);
      }
    } else {
      audioElement.play();
      this.isPlaying = true;
    }
  }

  formatTime(time: number): string {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    const milliseconds = Math.floor((time % 1) * 10);
    return `${minutes}:${seconds.toString().padStart(2, '0')}.${milliseconds}`;
  }

  onSeek(event: Event): void {
    const audioElement = this.audioPlayerRef?.nativeElement;
    if (!audioElement) return;

    const input = event.target as HTMLInputElement;
    const seekTime = parseFloat(input.value);
    audioElement.currentTime = seekTime;
    this.playbackTime = seekTime;
  }

  discardRecording(): void {
    this.hasRecording = false;
    this.recordedBlob = null;
    this.audioChunks = [];
    this.playbackTime = 0;
    this.duration = 0;
    this.isPlaying = false;

    if (this.playbackTimer) {
      clearInterval(this.playbackTimer);
    }
  }

  saveRecording(): void {
    if (!this.recordedBlob) {
      this.toastService.showError('Aucun enregistrement à sauvegarder');
      return;
    }

    // Create a File object from the blob
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `recording-${timestamp}.webm`;
    const file = new File([this.recordedBlob], fileName, {
      type: this.recordedBlob.type
    });

    this.dialogRef.close(file);
  }

  handleClose(): void {
    this.dialogRef.close();
  }

  async requestMicrophoneAgain(): Promise<void> {
    try {
      const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      console.log('Microphone permission state:', permission.state);

      await this.initializeAudio();
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
      this.toastService.showError('Impossible d\'accéder au microphone. Veuillez vérifier les permissions.');
    }
  }

  private startVisualization(): void {
    if (!this.analyser || !this.visualizerRef) return;

    const canvas = this.visualizerRef.nativeElement;
    const canvasContext = canvas.getContext('2d');
    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      this.animationId = requestAnimationFrame(draw);

      this.analyser!.getByteFrequencyData(dataArray);

      canvasContext!.fillStyle = '#f0f0f0';
      canvasContext!.fillRect(0, 0, canvas.width, canvas.height);

      const barWidth = (canvas.width / bufferLength) * 2.5;
      let barHeight;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        barHeight = (dataArray[i] / 255) * canvas.height * 0.8;

        const gradient = canvasContext!.createLinearGradient(0, canvas.height - barHeight, 0, canvas.height);
        gradient.addColorStop(0, '#4F46E5');
        gradient.addColorStop(1, '#7C3AED');

        canvasContext!.fillStyle = gradient;
        canvasContext!.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

        x += barWidth + 1;
      }
    };

    draw();
  }

  ngOnDestroy(): void {
    // Clean up resources
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    if (this.playbackTimer) {
      clearInterval(this.playbackTimer);
    }

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
    }

    if (this.audioContext) {
      this.audioContext.close();
    }
  }
}
