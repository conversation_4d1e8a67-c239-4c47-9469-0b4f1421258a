import { Pager, IR<PERSON> } from './pager';
/**
 * `NumericContainer` module handles rendering and refreshing numeric container.
 */
export declare class NumericContainer implements IRender {
    private element;
    private first;
    private prev;
    private PP;
    private NP;
    private next;
    private last;
    private links;
    private pagerElement;
    private target;
    private pagerModule;
    /**
     * Constructor for numericContainer module
     *
     * @param {Pager} pagerModule - specifies the pagerModule
     * @hidden
     */
    constructor(pagerModule?: Pager);
    /**
     * The function is used to render numericContainer
     *
     * @returns {void}
     * @hidden
     */
    render(): void;
    /**
     * Refreshes the numeric container of Pager.
     *
     * @returns {void}
     */
    refresh(): void;
    /**
     * The function is used to refresh refreshNumericLinks
     *
     * @returns {void}
     * @hidden
     */
    refreshNumericLinks(): void;
    /**
     * Binding events to the element while component creation
     *
     * @returns {void}
     * @hidden
     */
    wireEvents(): void;
    /**
     * Unbinding events from the element while component destroy
     *
     * @returns {void}
     * @hidden
     */
    unwireEvents(): void;
    /**
     * To destroy the PagerMessage
     *
     * @function destroy
     * @returns {void}
     * @hidden
     */
    destroy(): void;
    private refreshAriaAttrLabel;
    private renderNumericContainer;
    private renderFirstNPrev;
    private renderPrevPagerSet;
    private renderNextPagerSet;
    private renderNextNLast;
    private clickHandler;
    private auxiliaryClickHandler;
    private updateLinksHtml;
    private updateStyles;
    private updateFirstNPrevStyles;
    private updatePrevPagerSetStyles;
    private updateNextPagerSetStyles;
    private updateNextNLastStyles;
}
