@import 'ej2-base/styles/definition/tailwind3.scss';
@import 'signature/tailwind3-definition.scss';
@import 'signature/all.scss';

@import 'input/tailwind3-definition.scss';
@import 'input/icons/tailwind3.scss';
@import 'input/all.scss';
@import 'input/bigger.scss';
@import 'numerictextbox/tailwind3-definition.scss';
@import 'numerictextbox/icons/tailwind3.scss';
@import 'numerictextbox/all.scss';
@import 'numerictextbox/bigger.scss';
@import 'maskedtextbox/tailwind3-definition.scss';
@import 'maskedtextbox/all.scss';
@import 'maskedtextbox/bigger.scss';
@import 'ej2-popups/styles/popup/tailwind3-definition.scss';
@import 'ej2-popups/styles/tooltip/tailwind3-definition.scss';
@import 'slider/tailwind3-definition.scss';
@import 'slider/all.scss';
@import 'slider/bigger.scss';
@import 'textbox/tailwind3-definition.scss';
@import 'textbox/all.scss';
@import 'textbox/bigger.scss';
@import 'textarea/tailwind3-definition.scss';
@import 'textarea/all.scss';
@import 'ej2-buttons/styles/button/tailwind3-definition.scss';
@import 'ej2-popups/styles/spinner/tailwind3-definition.scss';
@import 'uploader/tailwind3-definition.scss';
@import 'uploader/icons/tailwind3.scss';
@import 'uploader/all.scss';
@import 'uploader/bigger.scss';
@import 'ej2-splitbuttons/styles/split-button/tailwind3-definition.scss';
@import 'color-picker/tailwind3-definition.scss';
@import 'color-picker/icons/tailwind3.scss';
@import 'color-picker/all.scss';
@import 'color-picker/bigger.scss';
@import 'rating/tailwind3-definition.scss';
@import 'rating/all.scss';
@import 'rating/bigger.scss';
@import 'data-form/tailwind3-definition.scss';
@import 'data-form/all.scss';
@import 'otp-input/tailwind3-definition.scss';
@import 'otp-input/all.scss';
@import 'otp-input/bigger.scss';
@import 'speech-to-text/tailwind3-definition.scss';
@import 'speech-to-text/icons/tailwind3.scss';
@import 'speech-to-text/all.scss';
@import 'speech-to-text/bigger.scss';
@import 'smart-textarea/tailwind3-definition.scss';
@import 'smart-textarea/all.scss';