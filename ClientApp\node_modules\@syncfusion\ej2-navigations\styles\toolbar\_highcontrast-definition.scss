$tbar-skin: 'highcontrast' !default;
$tbar-icons-bgr-font-size: 18px;
$tbar-separator-bgr-mrgn: 10px 5px;
$tbar-separator-vertical-bgr-mrgn: 5px 10px;
$tbar-radius: 0;
$tbar-separator-nrml-mrgn: 7.5px 3px;
$tbar-pop-radius: 0;
$tbar-item-pop-bg-color: transparent !default;
$tbar-separator-vertical-nrml-mrgn: 3px 7.5px;
$tbar-border-size: $border-size;
$tbar-separator-border-type: $border-type;
$tbar-dash-border: 2px solid #fff;
$tbar-pop-box-shadow: 0 2px 2px 1px rgba(0, 0, 0, .21);
$tbar-hover-border-color: $hover-border-color;
$tbar-pressed-border: $tbar-hover-border-color;
$tbar-zero-value: 0 !default;
$tbar-border-radius: $border-radius !default;
$tbar-tab-highlight-color:  rgba(0, 0, 0, 0) !default;
$tbar-border-nav-type: solid !default;
$tbar-btn-box-shadow: none !default;
$tbar-default-bg: $bg-base-0 !default;
$tbar-items-default-bg: $tbar-default-bg !default;
$tbar-pop-bg: $tbar-default-bg !default;
$tbar-default-font: $content-font !default;
$tbar-active-bg: transparent !default;
$tbar-active-icon-color: $selection-font !default;
$tbar-press-bg: $selection-bg !default;
$tbar-press-border-color: $tbar-press-bg !default;
$tbar-btn-press-bg: $tbar-press-bg !default;
$tbar-hover-bg:  $hover-bg !default;
$tbar-hover-font: $hover-font !default;
$tbar-default-icon-color: $content-font !default;
$tbar-pressed-bg: $selection-bg !default;
$tbar-pressed-font: $selection-font !default;
$tbar-select-font: $selection-font !default;
$tbar-default-icon-overlay: $border-alt !default;
$tbar-separator-border: $border-fg !default;
$tbar-default-border: $border-fg !default;
$tbar-hover-border-color: $hover-border !default;
$tbar-focus-border-color: $tbar-hover-border-color !default;
$tbar-focus-bg: inherit !default;
$tbar-press-font: $selection-font !default;
$tbar-default-font-overlay: $border-alt !default;
$tbar-active-font-color: $tbar-press-font !default;
$tbar-border-nav-type: solid !default;
$tbar-border-nav-active-type: solid !default;
$tbar-box-shadow: none !default;
$tbar-border-type: solid !default;
$tbar-separator-size: 1px !default;
$tba-horizontal-separator: 0 $tbar-separator-size 0 0 !default;
$tba-vertical-separator: 0 0 $tbar-separator-size 0 !default;
$tbar-popup-border-width: 0 0 0 $border-size !default;
$tbar-popup-rtl-border-width: 0 $border-size 0 0 !default;
$tbar-popup-vertical-border-width: $border-size 0 0 0 !default;
$tbar-popup-vertical-rtl-border-width: 0 0 $border-size 0 !default;
$tbar-nrml-size: 40px !default;
$tbar-bgr-size: 50px !default;
$tbar-nrml-items-size: 39px !default;
$tbar-bgr-items-size: 49px !default;
$tbar-nrml-item-size: 39px !default;
$tbar-item-height: auto !default;
$tbar-item-nrml-minwidth: 34px !default;
$tbar-bgr-item-size: 49px !default;
$tbar-btn-font-size: 16px !default;
$tbar-btn-txt-font-size: 14px !default;
$tbar-btn-border: 2px solid $bg-base-0 !default;
$tbar-item-bgr-padding: 0  1.5px !default;
$tbar-item-nrml-padding: 0 1px !default;
$tbar-btn-nrml-padding: 0 !default;
$tbar-btn-bgr-padding: 0 !default;
$tbar-btn-bgr-focus-padding: 0 !default;
$tbar-btn-icn-nrml-padding: 0 0 0 12px !default;
$tbar-btn-icn-bgr-padding: 0 0 0 15px !default;
$tbar-rtl-btn-icn-nrml-padding: 0 12px 0 0 !default;
$tbar-rtl-btn-icn-bgr-padding: 0 15px 0 0 !default;
$tbar-btn-icn-right-bgr-padding: 0 12px 0 0 !default;
$tbar-btn-icn-right-nrml-padding: 0 15px 0 0 !default;
$tbar-rtl-btn-icn-right-nrml-padding: 0 0 0 15px !default;
$tbar-rtl-btn-icn-right-bgr-padding: 0 0 0 12px !default;
$btn-txt-nrml-padding: 0 12px 0 8px !default;
$btn-txt-bgr-padding: 0 15px 0 10px !default;
$btn-rtl-txt-nrml-padding: 0 8px 0 12px !default;
$btn-rtl-txt-bgr-padding: 0 10px 0 15px !default;
$tbar-item-pop-nrml-padding: 0 !default;
$tbar-item-pop-bgr-padding: 0 !default;
$tbar-pop-btn-bgr-padding: 0 12.5px 0 0 !default;
$tbar-pop-btn-nrml-padding: 0 10px 0 0 !default;
$tbar-pop-icon-bgr-padding: 0 12.5px !default;
$tbar-pop-icon-nrml-padding: 0 10px !default;
$tbar-pop-btn-txt-nrml-pad: 0 10px !default;
$tbar-pop-btn-txt-bgr-pad: 0 12.5px !default;
$tbar-nav-press-border: 0 !default;
$tbar-nav-hover-border: 1px solid $hover-border !default;
$tbar-nav-focus-border: 0 !default;
$tbar-nav-pressed-box-shadow: none !default;
$tbar-btn-border-radius: 0 !default;
$tbar-btn-pressed-box-shadow: none !default;
$tbar-btn-nrml-mrgn: 0 !default;
$tbar-popup-padding: 0 !default;
$tbar-btn-nrml-minheight: 39px !default;
$tbar-btn-nrml-line-height: 25px !default;
$tbar-btn-icon-nrml-line-height: 24px !default;
$tbar-btn-bgr-minheight: 49px !default;
$tbar-btn-bgr-line-height: 35px !default;
$tbar-btn-icon-bgr-line-height: 34px !default;
$tbar-btn-nrml-minwidth: 40px !default;
$tbar-btn-weight: 400 !default;
$tbar-btn-bgr-minwidth: 50px !default;
$tbar-separator-nrml-height: 24px !default;
$tbar-separator-bgr-height: 30px !default;
$tbar-separator-nrml-minheight: $tbar-separator-nrml-height !default;
$tbar-separator-bgr-minheight: $tbar-separator-bgr-height !default;
$tbar-btn-icon-nrml-width: 20px !default;
$tbar-btn-icon-nrml-height: 25px !default;
$tbar-right-item-line-height: 24px !default;
$tbar-btn-icon-bgr-width: 25px !default;
$tbar-nav-nrml-width: 40px !default;
$tbar-nav-bgr-width: 50px !default;
$tbar-item-nrml-mrgn: 3px !default;
$tbar-item-bgr-mrgn: 3px !default;
$tbar-btn-pop-nrml-minheight: 36px !default;
$tbar-btn-pop-bgr-minheight: 45px !default;
$tbar-multirow-items-mrgn-bigger: 12.5px !default;
$tbar-multirow-items-mrgn-small: 10px !default;
$tbar-multirow-item-top-btm-mrgn-bigger: 1.5px 0 !default;
$tbar-multirow-item-top-btm-mrgn-small: 1px 0 !default;
$tbar-btn-border-color: $bg-base-0 !default;
$tbar-btn-focus-active-bg-color: $selection-bg !default;
$tbar-btn-hover-border-color: $hover-border !default;
$ext-tbar-btn-border-color: $bg-base-0 !default;
$ext-tbar-btn-hover-border-color: $hover-border !default;
$ext-tbar-btn-focus-active-bg-color: $selection-bg !default;
$tbar-nav-hover-color: $hover-border !default;
$tbar-nav-hover-active-bg-color: $selection-bg !default;
$tbar-bgr-btn-text-font-size: 14px !default;
$tbar-bgr-btn-icon-font-size: 16px !default;
$tbar-bgr-btn-focus-padding: 0 !default;
$tbar-nrml-btn-border-radius: 0 !default;
$tbar-nrml-btn-focus-padding: 0 !default;
$tbar-nrml-btn-focus-outline: none !default;
$tbar-btn-icons-focus-color: $hover-font !default;
$tbar-btn-text-focus-color: $hover-font !default;
$tbar-btn-focus-border-color: $bg-base-100 !default;
$tbar-btn-hover-border-size: 2px !default;
$tbar-btn-hover-active-icons-color: $shadow !default;
$tbar-btn-hover-active-text-color: $shadow !default;
$tbar-btn-overlay-opacity: .5 !default;
$tbar-btn-active-bg: $tbar-press-bg !default;
$tbar-btn-active-icons-color: $tbar-pressed-font !default;
$tbar-btn-active-text-color: $tbar-pressed-font !default;
$tbar-btn-text-color: $hover-font !default;
$tbar-btn-pressed-text-color: $selection-font !default;
$tbar-btn-pressed-focus-box-shadow: none !default;
$tbar-btn-pressed-bg: $selection-bg !default;
$tbar-flat-btn-active-box-shadow: none !default;
$tbar-ext-btn-focus-padding: 0 !default;
$tbar-ext-btn-icon-padding: 0 !default;
$tbar-ext-btn-icon-font-size: 14px !default;
$tbar-ext-btn-focus-box-shadow: none !default;
$tbar-ext-btn-hover-border-color: $hover-border !default;
$tbar-ext-btn-border: 2px solid $bg-base-0 !default;
$tbar-popup-icon-font-size: 12px !default;
$tbar-popup-text-btn-icon-padding: 0 !default;
$tbar-popup-bgr-text-btn-icon-padding: 0 !default;
$tbar-popup-btn-border: 2px solid $bg-base-0 !default;
$tbar-popup-btn-border-radius: 0 !default;
$tbar-popup-bgr-height: auto !default;
$tbar-popup-bgr-btn-icon-font-size: 16px !default;
$tbar-popup-bgr-btn-text-font-size: 14px !default;
$tbar-popup-nav-active-border-bottom-right-radius: 0 !default;
$tbar-popup-nav-active-bg: $tbar-default-bg !default;
$tbar-popup-nav-active-icons-color: $tbar-default-icon-color !default;
$tbar-popup-nav-hover-bg: $tbar-hover-bg !default;
$tbar-popup-nav-hover-color: $tbar-hover-font !default;
$tbar-popup-nav-hover-icons-color: $tbar-hover-font !default;
$tbar-popup-nav-hover-border-color: $tbar-btn-hover-border-color !default;
$tbar-popup-nav-hover-border-size: 2px !default;
$tbar-popup-nav-hover-active-bg: $selection-bg !default;
$tbar-popup-nav-hover-active-border-color: $tbar-default-border !default;
$tbar-popup-nav-hover-active-border-size: 0 !default;
$tbar-popup-nav-focus-bg: inherit !default;
$tbar-popup-nav-focus-color: $shadow !default;
$tbar-popup-nav-focus-border-color: $tbar-hover-border-color !default;
$tbar-popup-nav-focus-border-size: 2px !default;
$tbar-popup-btn-bg: $tbar-default-bg !default;
$tbar-popup-btn-hover-bg: $tbar-hover-bg !default;
$tbar-popup-btn-hover-box-shadow: none !default;
$tbar-popup-btn-active-bg: $tbar-press-bg !default;
$tbar-popup-btn-active-box-shadow: none !default;
$tbar-popup-btn-focus-bg: inherit !default;
$tbar-popup-btn-focus-box-shadow: none !default;
$tbar-popup-nav-pressed-icons-active-color: $tbar-active-font-color !default;
$tbar-popup-btn-focus-outline: none !default;
$tbar-popup-nav-pressed-border-color: $tbar-pressed-bg !default;
$tbar-popup-nav-pressed-border-size: 0 0 0 1px !default;
$tbar-popup-nav-pressed-focus-border-color: $bg-base-100 !default;
$tbar-popup-nav-pressed-focus-border-size: 2px !default;
$tbar-popup-btn-hover-border-size: 2px !default;
@mixin tbar-btn-animation {
  content: '';
}
@mixin tbar-btn-animation-after {
  content: '';
}
