@include export-module('tab-bootstrap4-icons') {
  #{&}.e-tab {

    .e-tab-header {

      .e-toolbar-items.e-hscroll.e-rtl {

        .e-nav-left-arrow::before {
          content: '\e70b';
        }

        .e-nav-right-arrow::before {
          content: '\e71f';
        }
      }

      .e-close-icon::before {
        content: '\e745';
        position: relative;
      }
    }

    &.e-vertical-icon .e-tab-header {

      @media screen and (max-width: 480px) {

        .e-popup-up-icon::before {
          content: '\e721';
        }

        .e-popup-down-icon::before {
          content: '\e744';
        }
      }
    }

    &.e-vertical-tab {

      .e-tab-header {

        .e-popup-up-icon {

          &::before {
            content: 'More';
          }

          &::after {
            content: '\e721';
          }
        }

        .e-popup-down-icon {

          &::before {
            content: 'More';
          }

          &::after {
            content: '\e744';
          }
        }
      }

      &.e-icon-tab {

        .e-tab-header {

          .e-popup-up-icon {

            &::before {
              content: '';
            }

            &::after {
              content: '\e721';
            }
          }

          .e-popup-down-icon {

            &::before {
              content: '';
            }

            &::after {
              content: '\e744';
            }
          }
        }
      }
    }
  }

  .e-tab-clone-element {

    .e-close-icon::before {
      content: '\e745';
      position: relative;
    }
  }
}
